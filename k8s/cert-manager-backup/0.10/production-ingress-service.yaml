apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    certmanager.k8s.io/cluster-issuer: production-letsencrypt-issuer
    kubectl.kubernetes.io/last-applied-configuration: |
      {"apiVersion":"extensions/v1beta1","kind":"Ingress","metadata":{"annotations":{"certmanager.k8s.io/cluster-issuer":"production-letsencrypt-issuer","kubernetes.io/ingress.class":"nginx","nginx.ingress.kubernetes.io/affinity":"cookie","nginx.ingress.kubernetes.io/force-ssl-redirect":"true","nginx.ingress.kubernetes.io/proxy-body-size":"50m","nginx.ingress.kubernetes.io/proxy-buffer-size":"16k","nginx.ingress.kubernetes.io/proxy-buffers-number":"8","nginx.ingress.kubernetes.io/proxy-connect-timeout":"240","nginx.ingress.kubernetes.io/proxy-read-timeout":"240","nginx.ingress.kubernetes.io/proxy-send-timeout":"240","nginx.ingress.kubernetes.io/rewrite-target":"/$1","nginx.ingress.kubernetes.io/session-cookie-expires":"172800","nginx.ingress.kubernetes.io/session-cookie-max-age":"172800","nginx.ingress.kubernetes.io/session-cookie-name":"route","nginx.ingress.kubernetes.io/ssl-redirect":"true"},"name":"production-ingress-service","namespace":"wavo-production"},"spec":{"rules":[{"host":"app2.askhuron.com","http":{"paths":[{"backend":{"serviceName":"production-frontend-cluster-ip-service","servicePort":8080},"path":"/?(.*)"}]}},{"host":"dash.reignmakers.io","http":{"paths":[{"backend":{"serviceName":"production-frontend-cluster-ip-service","servicePort":8080},"path":"/?(.*)"}]}},{"host":"outreach.sparkoutbound.com","http":{"paths":[{"backend":{"serviceName":"production-frontend-cluster-ip-service","servicePort":8080},"path":"/?(.*)"}]}},{"host":"app.wavo.co","http":{"paths":[{"backend":{"serviceName":"production-frontend-cluster-ip-service","servicePort":8080},"path":"/?(.*)"}]}},{"host":"app.brokergenerator.com","http":{"paths":[{"backend":{"serviceName":"production-frontend-cluster-ip-service","servicePort":8080},"path":"/?(.*)"}]}},{"host":"outbound.techpromarketing.com","http":{"paths":[{"backend":{"serviceName":"production-frontend-cluster-ip-service","servicePort":8080},"path":"/?(.*)"}]}},{"host":"prospecting.superhumansales.com","http":{"paths":[{"backend":{"serviceName":"production-frontend-cluster-ip-service","servicePort":8080},"path":"/?(.*)"}]}},{"host":"email.moreresultsmarketing.com","http":{"paths":[{"backend":{"serviceName":"production-frontend-cluster-ip-service","servicePort":8080},"path":"/?(.*)"}]}},{"host":"wavo.leadable.io","http":{"paths":[{"backend":{"serviceName":"production-frontend-cluster-ip-service","servicePort":8080},"path":"/?(.*)"}]}},{"host":"app.fivecontacts.com","http":{"paths":[{"backend":{"serviceName":"production-frontend-cluster-ip-service","servicePort":8080},"path":"/?(.*)"}]}},{"host":"send.emailmovers-group.com","http":{"paths":[{"backend":{"serviceName":"production-frontend-cluster-ip-service","servicePort":8080},"path":"/?(.*)"}]}},{"host":"leadfactory.whitelabelclub.com","http":{"paths":[{"backend":{"serviceName":"production-frontend-cluster-ip-service","servicePort":8080},"path":"/?(.*)"}]}},{"host":"email.automationwolf.com","http":{"paths":[{"backend":{"serviceName":"production-frontend-cluster-ip-service","servicePort":8080},"path":"/?(.*)"}]}},{"host":"emailogin.salesleadautomation.com","http":{"paths":[{"backend":{"serviceName":"production-frontend-cluster-ip-service","servicePort":8080},"path":"/?(.*)"}]}},{"host":"app.inboxmaps.com","http":{"paths":[{"backend":{"serviceName":"production-frontend-cluster-ip-service","servicePort":8080},"path":"/?(.*)"}]}},{"host":"outreach.soleadify.com","http":{"paths":[{"backend":{"serviceName":"production-frontend-cluster-ip-service","servicePort":8080},"path":"/?(.*)"}]}},{"host":"app.snaptiv.com","http":{"paths":[{"backend":{"serviceName":"production-frontend-cluster-ip-service","servicePort":8080},"path":"/?(.*)"}]}},{"host":"app.b2bleadflow.io","http":{"paths":[{"backend":{"serviceName":"production-frontend-cluster-ip-service","servicePort":8080},"path":"/?(.*)"}]}},{"host":"campaigns.leadroll.co","http":{"paths":[{"backend":{"serviceName":"production-frontend-cluster-ip-service","servicePort":8080},"path":"/?(.*)"}]}},{"host":"email.brainylabs.io","http":{"paths":[{"backend":{"serviceName":"production-frontend-cluster-ip-service","servicePort":8080},"path":"/?(.*)"}]}},{"host":"outbound.agencyleads.pro","http":{"paths":[{"backend":{"serviceName":"production-frontend-cluster-ip-service","servicePort":8080},"path":"/?(.*)"}]}},{"host":"email.leadfactury.com","http":{"paths":[{"backend":{"serviceName":"production-frontend-cluster-ip-service","servicePort":8080},"path":"/?(.*)"}]}},{"host":"campaigns.salezilla.io","http":{"paths":[{"backend":{"serviceName":"production-frontend-cluster-ip-service","servicePort":8080},"path":"/?(.*)"}]}},{"host":"campaigns.quantumga.com","http":{"paths":[{"backend":{"serviceName":"production-frontend-cluster-ip-service","servicePort":8080},"path":"/?(.*)"}]}},{"host":"company.ai-bees.io","http":{"paths":[{"backend":{"serviceName":"production-frontend-cluster-ip-service","servicePort":8080},"path":"/?(.*)"}]}},{"host":"app.boonsnap.com","http":{"paths":[{"backend":{"serviceName":"production-frontend-cluster-ip-service","servicePort":8080},"path":"/?(.*)"}]}},{"host":"app.tryupsurge.com","http":{"paths":[{"backend":{"serviceName":"production-frontend-cluster-ip-service","servicePort":8080},"path":"/?(.*)"}]}},{"host":"portal.leadfeed.pro","http":{"paths":[{"backend":{"serviceName":"production-frontend-cluster-ip-service","servicePort":8080},"path":"/?(.*)"}]}},{"host":"hub.growthsource.io","http":{"paths":[{"backend":{"serviceName":"production-frontend-cluster-ip-service","servicePort":8080},"path":"/?(.*)"}]}},{"host":"app.leado.ca","http":{"paths":[{"backend":{"serviceName":"production-frontend-cluster-ip-service","servicePort":8080},"path":"/?(.*)"}]}},{"host":"amazing.netskiel.com","http":{"paths":[{"backend":{"serviceName":"production-frontend-cluster-ip-service","servicePort":8080},"path":"/?(.*)"}]}},{"host":"dash.mailstrats.com","http":{"paths":[{"backend":{"serviceName":"production-frontend-cluster-ip-service","servicePort":8080},"path":"/?(.*)"}]}},{"host":"worldleaders.automatedsalesprospecting.com","http":{"paths":[{"backend":{"serviceName":"production-frontend-cluster-ip-service","servicePort":8080},"path":"/?(.*)"}]}},{"host":"campaigns.leadsoft.io","http":{"paths":[{"backend":{"serviceName":"production-frontend-cluster-ip-service","servicePort":8080},"path":"/?(.*)"}]}},{"host":"app.reachsupreme.com","http":{"paths":[{"backend":{"serviceName":"production-frontend-cluster-ip-service","servicePort":8080},"path":"/?(.*)"}]}}],"tls":[{"hosts":["app2.askhuron.com","dash.reignmakers.io","outreach.sparkoutbound.com","app.wavo.co","app.brokergenerator.com","outbound.techpromarketing.com","prospecting.superhumansales.com","email.moreresultsmarketing.com","wavo.leadable.io","app.fivecontacts.com","send.emailmovers-group.com","leadfactory.whitelabelclub.com","email.automationwolf.com","emailogin.salesleadautomation.com","app.inboxmaps.com","outreach.soleadify.com","app.snaptiv.com","app.b2bleadflow.io","campaigns.leadroll.co","email.brainylabs.io","outbound.agencyleads.pro","email.leadfactury.com","campaigns.salezilla.io","campaigns.quantumga.com","company.ai-bees.io","app.boonsnap.com","app.tryupsurge.com","portal.leadfeed.pro","hub.growthsource.io","app.leado.ca","amazing.netskiel.com","dash.mailstrats.com","worldleaders.automatedsalesprospecting.com","campaigns.leadsoft.io","app.reachsupreme.com"],"secretName":"production-ingress-service-cert"}]}}
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/affinity: cookie
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: 50m
    nginx.ingress.kubernetes.io/proxy-buffer-size: 16k
    nginx.ingress.kubernetes.io/proxy-buffers-number: "8"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "240"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "240"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "240"
    nginx.ingress.kubernetes.io/rewrite-target: /$1
    nginx.ingress.kubernetes.io/session-cookie-expires: "172800"
    nginx.ingress.kubernetes.io/session-cookie-max-age: "172800"
    nginx.ingress.kubernetes.io/session-cookie-name: route
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
  creationTimestamp: "2019-07-31T11:01:09Z"
  generation: 232
  name: production-ingress-service
  namespace: wavo-production
  resourceVersion: "517633274"
  uid: 8088d702-b382-11e9-b7d3-42010a8e0102
spec:
  rules:
  - host: app2.askhuron.com
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  - host: dash.reignmakers.io
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  - host: outreach.sparkoutbound.com
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  - host: app.wavo.co
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  - host: app.brokergenerator.com
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  - host: outbound.techpromarketing.com
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  - host: prospecting.superhumansales.com
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  - host: email.moreresultsmarketing.com
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  - host: wavo.leadable.io
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  - host: app.fivecontacts.com
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  - host: send.emailmovers-group.com
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  - host: leadfactory.whitelabelclub.com
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  - host: email.automationwolf.com
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  - host: emailogin.salesleadautomation.com
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  - host: app.inboxmaps.com
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  - host: outreach.soleadify.com
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  - host: app.snaptiv.com
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  - host: app.b2bleadflow.io
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  - host: campaigns.leadroll.co
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  - host: email.brainylabs.io
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  - host: outbound.agencyleads.pro
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  - host: email.leadfactury.com
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  - host: campaigns.salezilla.io
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  - host: campaigns.quantumga.com
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  - host: app.boonsnap.com
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  - host: app.tryupsurge.com
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  - host: portal.leadfeed.pro
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  - host: hub.growthsource.io
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  - host: amazing.netskiel.com
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  - host: dash.mailstrats.com
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  - host: worldleaders.automatedsalesprospecting.com
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  - host: campaigns.leadsoft.io
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  - host: app.reachsupreme.com
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  - host: email.ai-bees.io
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  - host: outreach.fuzeiq.com
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  - host: app.leadforce360.com
    http:
      paths:
      - backend:
          service:
            name: production-frontend-cluster-ip-service
            port:
              number: 8080
        path: /?(.*)
        pathType: ImplementationSpecific
  tls:
  - hosts:
    - app2.askhuron.com
    - dash.reignmakers.io
    - outreach.sparkoutbound.com
    - app.wavo.co
    - app.brokergenerator.com
    - outbound.techpromarketing.com
    - prospecting.superhumansales.com
    - email.moreresultsmarketing.com
    - wavo.leadable.io
    - app.fivecontacts.com
    - send.emailmovers-group.com
    - leadfactory.whitelabelclub.com
    - email.automationwolf.com
    - emailogin.salesleadautomation.com
    - app.inboxmaps.com
    - outreach.soleadify.com
    - app.snaptiv.com
    - app.b2bleadflow.io
    - campaigns.leadroll.co
    - email.brainylabs.io
    - outbound.agencyleads.pro
    - email.leadfactury.com
    - campaigns.salezilla.io
    - campaigns.quantumga.com
    - app.boonsnap.com
    - app.tryupsurge.com
    - portal.leadfeed.pro
    - hub.growthsource.io
    - amazing.netskiel.com
    - dash.mailstrats.com
    - worldleaders.automatedsalesprospecting.com
    - campaigns.leadsoft.io
    - app.reachsupreme.com
    - email.ai-bees.io
    - outreach.fuzeiq.com
    - app.leadforce360.com
    secretName: production-ingress-service-cert
status:
  loadBalancer:
    ingress:
    - ip: *************
