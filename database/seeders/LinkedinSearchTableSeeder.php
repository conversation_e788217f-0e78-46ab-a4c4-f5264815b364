<?php

namespace Database\Seeders;

use App\LinkedinSearch;
use App\LinkedinProfile;
use App\Contact;
use App\Company;
use App\Team;
use Carbon\Carbon;
use Faker\Generator as Faker;
use Illuminate\Database\Seeder;
use Illuminate\Support\Arr;

class LinkedinSearchTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(Faker $faker)
    {
        $titles = ['Programmer', 'Manager', 'VP', 'Designer'];
        $profileStatuses = ['DONE', 'PENDING', 'DONE', 'DONE', 'DONE', 'ERROR', 'DONE'];
        $searchStatuses = ['DONE', 'STOPPED', 'DONE', 'DONE', 'DONE', 'DONE'];
        $companies = [
            ['name'=>'Cisco', 'linkedin_url'=>'https://www.linkedin.com/sales/company/1063'],
            ['name'=>'Nokia', 'linkedin_url'=>'https://www.linkedin.com/sales/company/1070'],
            ['name'=>'IBM', 'linkedin_url'=>'https://www.linkedin.com/sales/company/1009'],
            ['name'=>'Accenture', 'linkedin_url'=>'https://www.linkedin.com/sales/company/1033'],
            ['name'=>'Microsoft', 'linkedin_url'=>'https://www.linkedin.com/sales/company/1035']
        ];

        $company = Arr::random($companies);
        $title = Arr::random($titles);  
        $sStatus = Arr::random($searchStatuses);  

        $team = Team::inRandomOrder()->first(); 

        $numOfProfiles = rand(15, 60);
        $maxPageNum = (int) ceil($numOfProfiles/25);

        $searchUrl = 'https://www.linkedin.com/sales/search/people?keywords='.strtolower($title).'&page=1';

        $linkedinSearch = LinkedinSearch::create([
            'name' => 'DEMO - LinkedIn Search '.$title,
            'url' => $searchUrl,
            'agency_id' => $team->agency_id,
            'team_id' => $team->id,
            'page' => $maxPageNum,
            'status' => $sStatus,
            'searched_at' => now()
        ]);

        // profiles created by Seeder cannot be used for reachout since the linkedin URLs are fake
        for ($i=0; $i < $numOfProfiles; $i++) 
        {
            $objCompany = Arr::random($companies);
            $pStatus = Arr::random($profileStatuses);
            $fname = $faker->firstName;
            $lname = $faker->lastName;
            $emailMeta = null;
            $liprofileUrl = 'https://www.linkedin.com/sales/people/seeded__'.$faker->uuid;

            // if search status is done, no pending profiles
            if($sStatus == 'DONE' && $pStatus == 'PENDING') {
                $pStatus = Arr::random(['DONE', 'ERROR', 'DONE']);
            }

            $liEmail = $pStatus == 'DONE' ? strtolower($fname.$lname.'@'.$objCompany['name'].'_seeded.com') : null;
            $coEmail = $liEmail ? $liEmail : 'linkedin_email_placeholder';
            
            $company = Company::updateOrCreate(
                ['linkedin_url' => $objCompany['linkedin_url']],
                [
                    'name' => $objCompany['name'],
                    'linkedin_logo_url' => null,
                    'linkedin_browsed_at' => now(),
                    'website' => null,
                    'industry' => null
                ]
            );

            $contact = Contact::updateOrCreate(
                ['linkedin_url' => $liprofileUrl],
                [
                    'email' => $coEmail,
                    'linkedin_browsed_at' => now(),
                    'first_name' => $fname,
                    'last_name' => $lname,
                    'company_name' => $company->name,
                    'company_id' => $company->id
                ]
            );

            if($liEmail) {
                $emailClass = Arr::random(['verified', 'not_verified', 'verified', 'not_verified', 'not_verified']);

                $emailMeta = [
                    'source' => 'anymailfinder',
                    'status' => 'success',
                    'confidence' => $emailClass,
                    'score' => $emailClass == 'verified' ? '99' : '75',
                    'email' => $liEmail
                ];
            }

            $profile = LinkedinProfile::create([
                'agency_id' => $linkedinSearch->agency_id,
                'team_id' => $linkedinSearch->team_id, 
                'linkedin_search_id' => $linkedinSearch->id,
                'email' => $liEmail, 
                'status' => $pStatus,
                'first_name' => $fname,
                'last_name' => $lname,
                'title' => $title,
                'address' => null,
                'linkedin_url' => $liprofileUrl,
                'contact_id' => $contact->id,
                'email_meta' => $emailMeta
            ]);
        }
    }
}
