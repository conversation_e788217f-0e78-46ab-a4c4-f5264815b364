<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sl_currencies', function (Blueprint $table) {
            $table->unsignedInteger('domain_count')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sl_currencies', function (Blueprint $table) {
            $table->dropColumn(['domain_count']);
            $table->dropTimestamps();
        });
    }
};
