@section('page-scripts')
    <script src="//rawcdn.githack.com/RickStrahl/jquery-resizable/master/dist/jquery-resizable.min.js"></script>
@endsection

<update-agency-information 
    :user="user" 
    :agency="agency" 
    app_domain="{{ config('app.domain') }}"
    agency_slug="{{ Illuminate\Support\Str::slug(Auth::user()->agency->name) }}"
    inline-template
>
    <div class="card card-shadow p-10">
        <div class="card-header card-header-transparent">
            <h4 class="card-title">Agency Information</h4>
        </div>

        <div class="card-block">
            <!-- Success Message -->
            <div class="alert alert-success" v-if="form.successful">
                Your agency information has been updated!
            </div>

            <form role="form">
                <!-- Name -->
                <div class="form-group" :class="{'has-error': form.errors.has('name')}">
                    <label class="d-block clearfix">
                        Agency Name

                        <span class="float-right" data-toggle="tooltip" data-placement="left"
                            title="This will be shown throughout the dashboard and in email invitations"
                        >
                            <i class="wb-info-circle"></i>  
                        </span>
                    </label>
                    <input type="text" class="form-control" name="name" v-model="form.name">
                    <div id="name-error" class="error" for="name" v-show="form.errors.has('name')">
                        @{{ form.errors.get('name') }}
                    </div>
                </div>

                <!-- E-Mail Address -->
                <div class="form-group" :class="{'has-error': form.errors.has('email')}">
                    <label class="d-block clearfix">
                        Email Address
                        <span class="float-right" data-toggle="tooltip" data-placement="left"
                            title="This will be shown throughout the dashboard and in email invitations"
                        >
                            <i class="wb-info-circle"></i>  
                        </span>
                    </label>
                    <input type="email" class="form-control" name="email" v-model="form.email">
                    <div id="email-error" class="error" for="email" v-show="form.errors.has('email')">
                        @{{ form.errors.get('email') }}
                    </div>
                </div>

                <!-- Domain Name -->
                <div class="form-group d-none" :class="{'has-error': form.errors.has('domain')}">
                    <label>Domain Name(without http)</label>
                    <input type="domain" class="form-control" name="domain" v-model="form.domain">
                    <div id="domain-error" class="error" for="domain" v-show="form.errors.has('domain')">
                        @{{ form.errors.get('domain') }}
                    </div>
                </div>

                <!-- Main Theme Color -->
                <div class="form-group" :class="{'has-error': form.errors.has('color')}">
                    <label>Color Scheme</label>
                    <select name="color" class="form-control" v-model="form.color"
                        data-plugin="selectpicker" 
                        data-style="btn-outline btn-default"
                    >
                        @foreach(Environment::templateSkins() as $templateSkin)
                            @if($templateSkin == 'wavo')
                                @if(Auth::user()->isAdmin())
                                    <option value="{{ $templateSkin }}">{{ $templateSkin }}</option>
                                @endif
                            @else
                                <option value="{{ $templateSkin }}">{{ $templateSkin }}</option>
                            @endif
                        @endforeach
                    </select>
                    <div id="color-error" class="error" for="color" v-show="form.errors.has('color')">
                        @{{ form.errors.get('color') }}
                    </div>
                </div>


                @if (Auth::user()->agency->domain != config('app.domain'))
                <div class="row" :class="{'has-error': form.errors.has('domain')}">
                    <div class="col-md-12 col-lg-12 mb-5">  
                        <span class="d-inline-block">
                            Domain Name
                        </span> 
                    </div>
                    <div class="col-md-7 col-xl-9 col-lg-8">
                        <div class="form-group" v-if="isDefaultDomain">
                            <div class="input-group">
                                <span class="input-group-addon">https://</span>
                                <input type="text" placeholder=""  
                                    class="form-control text-center" 
                                    v-model="appsubdomain"
                                >
                                <span class="input-group-addon w-p50">.@{{app_domain}}</span>
                            </div>
                        </div>

                        <div class="form-group" v-else>
                            <div class="input-group">
                                <span class="input-group-addon">https://</span>
                                <input type="text" class="form-control" placeholder="" v-model="form.domain">
                            </div>
                        </div> 
                        
                        <div id="domain-error" class="error" for="domain" v-show="form.errors.has('domain')">
                            @{{ form.errors.get('domain') }}
                        </div> 
                    </div>  
                    <div class="col-md-5 col-lg-4 col-xl-3">
                        <button class="btn btn-block btn-primary " type="button" 
                            @click.prevent="defaultDomain(false)"  v-if="isDefaultDomain"
                        >
                            <span class="custom-text"><i class="fa fa-link"></i> Use</span> Custom Domain
                        </button>

                        <button class="btn btn-block btn-dark " type="button" 
                            @click.prevent="defaultDomain(true)"  v-else
                        >
                            <span class="custom-text"><i class="fa fa-close"></i> Use</span> Default Domain
                        </button>
                    </div>

                    <div class="col-md-12 col-lg-12 mb-20">
                        <div class="alert-icon alert alert-info mt-0">
                            <i class="icon wb-info-circle"></i>
                            <p class="mb-5">Please enter the domain where you'd like to access your dashboard instance.</p>
                            <p class="mb-5">
                                You can use the domain 
                                <strong v-if="!isDefaultDomain">
                                    {{ Illuminate\Support\Str::slug(Auth::user()->agency->name) }}.{{ config('app.domain') }}
                                </strong> 
                                <strong v-else>
                                    {{ Auth::user()->agency->domain }}
                                </strong> 
                                temporarily or while DNS settings propagate and set up a custom domain later.
                            </p>
                            <p class="mb-5" v-if="!isDefaultDomain">
                                If using a custom domain, please create a CNAME record to 
                                <strong>{{ parse_url(config('app.url'))['host'] }}</strong>
                            </p>
                            <p class="mb-5" v-if="!isDefaultDomain">
                                You will have to wait for the DNS update to propagate before you can access your dashboard instance using a custom domain.
                            </p>  

                        </div>
                    </div>
                </div> 
                @endif 

                <!-- email invitation subject -->
                <div class="mb-30">
                    <label class="d-block clearfix">
                        Email Invitation Subject
                    </label>
                    <input type="text" 
                        v-model="form.subject" 
                        class="form-control"
                        placeholder="" 
                    >
                    <div id="subject-error" 
                        class="error" 
                        for="subject" 
                        v-show="form.errors.has('subject')"
                    >
                        @{{ form.errors.get('subject') }}
                    </div>
                </div>

                <!-- email invitation message -->
                <div id="email-invite-editor" class="mb-30" :class="this.spark.agency.color">
                    <label class="d-block clearfix">
                        Email Invitation Message
                    </label>
                    <tiny-editor
                        v-model="form.message"
                        type="invititation"
                    >
                    </tiny-editor>
                    <div id="message-error" 
                        class="error" 
                        for="message" 
                        v-show="form.errors.has('message')"
                    >
                        @{{ form.errors.get('message') }}
                    </div>
                </div>

                <!-- terms and conditions --> 
                <div class="form-group mb-40" :class="{'has-error': form.errors.has('email')}">
                    <label class="d-block clearfix">
                        Terms and Conditions
                    </label>
                    <tiny-editor
                        v-model="form.terms"
                        type="simple"
                        id="replyEditorArea"
                    ></tiny-editor>
                </div>

                <!-- Update Button -->
                <div class="form-group w-full d-block text-center">
                    <button type="submit" class="btn btn-primary"
                            @click.prevent="update"
                            :disabled="form.busy"
                    >
                        <span v-if="form.busy">
                            <i class="icon fa fa-spinner fa-spin"></i> Updating...
                        </span>
                        <span v-else><i class="icon wb-chevron-right"></i> Update</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</update-agency-information>
