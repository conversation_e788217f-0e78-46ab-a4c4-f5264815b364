<linkedin-search-activity
    linkedin_search_id="{{ $linkedinSearch->hashid }}"
    inline-template
>
	<div class="row">
	    <div class="col-md-12">
	        
	        <div class="panel">
	            <div class="panel-heading">
	                <div class="panel-title font-size-18 blue-grey-700 font-weight-400 pb-0 pt-30">
	                    <div class="text-truncate">
	                        Linkedin Search Activity
	                    </div>
	                </div> 
	                  
	            </div>
	            <div class="panel-body pt-30" >
	                <div class="loader-wrapper">
	                    <div class="mb-20">
	                        <div >
	                            <div v-if="linkedinSearchActivities.data?.length">
	                                <div v-for="activity in linkedinSearchActivities.data" 
	                                    class="card card-shadow mb-3 border"
	                                >
	                                    <div class="card-block p-15">
	                                        <div class="row py-5">
	                                            <div class="col-sm-9 col-md-6 col-lg-7 col-xl-8">
	                                                <h4 class="m-0 text-truncate pt-5"
	                                                    v-if="activity.user"
	                                                >
	                                                    <span class="text-capitalize">
	                                                        @{{activity.user.name}} 
	                                                    </span>
	                                                    <small>@{{activity.user.email}}</small>
	                                                </h4> 
	                                                <h4 class="m-0 text-truncate pt-5" v-else>
	                                                    <span class="text-capitalize">
	                                                        Updated by the system
	                                                    </span>
	                                                </h4> 
	                                                <span class="hidden-md-up">
	                                                    @{{getActivityDate(activity)}}
	                                                </span>
	                                            </div>
	                                            <div class="hidden-sm-down col-sm-2 col-md-3 col-lg-3 col-xl-2 vertical-align text-center px-0">  
	                                                <span class="vertical-align-middle ">
	                                                    @{{getActivityDate(activity)}}
	                                                </span>
	                                            </div>
	                                            <div class="col-sm-3 col-md-3 col-lg-2 col-xl-2 vertical-align text-right"> 
	                                                <span class="w-120 mr-55 btn btn-sm vertical-align-middle cd btn-success"
	                                                    :class="getActivityClass(activity.activity)" 
	                                                >
	                                                    @{{activity.activity}}
	                                                </span> 
	                                            </div>
	                                        </div> 
	                                    </div> 
	                                </div>

	                                <div class="text-center">
	                                    <pagination :data="linkedinSearchActivities" 
	                                        :limit="2"
	                                        @pagination-change-page="getLinkedinSearchActivities"
	                                    ></pagination>
	                                </div>  
	                            </div> 
	                            <div v-else class="text-center">
                                    No activities found.
                                </div>
	                        </div>
	                    </div> 
	                    

	                    <div class="loader-box vertical-align text-center">
	                        <div class="loader vertical-align-middle loader-circle"></div>
	                    </div>
	                </div> 
	            </div>
	        </div>
	        
	    </div>
	</div>
</linkedin-search-activity>