<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>Wavo API Reference</title>

    <link href="https://fonts.googleapis.com/css?family=Open+Sans&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="{{ asset("/vendor/scribe/css/theme-default.style.css") }}" media="screen">
    <link rel="stylesheet" href="{{ asset("/vendor/scribe/css/theme-default.print.css") }}" media="print">

    <script src="https://cdn.jsdelivr.net/npm/lodash@4.17.10/lodash.min.js"></script>

    <link rel="stylesheet"
          href="https://unpkg.com/@highlightjs/cdn-assets@11.6.0/styles/obsidian.min.css">
    <script src="https://unpkg.com/@highlightjs/cdn-assets@11.6.0/highlight.min.js"></script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jets/0.14.1/jets.min.js"></script>

    <style id="language-style">
        /* starts out as display none and is replaced with js later  */
                    body .content .bash-example code { display: none; }
                    body .content .javascript-example code { display: none; }
                    body .content .php-example code { display: none; }
                    body .content .python-example code { display: none; }
            </style>


    <script src="{{ asset("/vendor/scribe/js/theme-default-4.16.0.js") }}"></script>

</head>

<body data-languages="[&quot;bash&quot;,&quot;javascript&quot;,&quot;php&quot;,&quot;python&quot;]">

<a href="#" id="nav-button">
    <span>
        MENU
        <img src="{{ asset("/vendor/scribe/images/navbar.png") }}" alt="navbar-image"/>
    </span>
</a>
<div class="tocify-wrapper">
            <img src="/img/logo230.png" alt="logo" class="logo" style="padding-top: 10px;" width="100%"/>
    
            <div class="lang-selector">
                                            <button type="button" class="lang-button" data-language-name="bash">bash</button>
                                            <button type="button" class="lang-button" data-language-name="javascript">javascript</button>
                                            <button type="button" class="lang-button" data-language-name="php">php</button>
                                            <button type="button" class="lang-button" data-language-name="python">python</button>
                    </div>
    
    <div class="search">
        <input type="text" class="search" id="input-search" placeholder="Search">
    </div>

    <div id="toc">
                    <ul id="tocify-header-introduction" class="tocify-header">
                <li class="tocify-item level-1" data-unique="introduction">
                    <a href="#introduction">Introduction</a>
                </li>
                            </ul>
                    <ul id="tocify-header-authenticating-requests" class="tocify-header">
                <li class="tocify-item level-1" data-unique="authenticating-requests">
                    <a href="#authenticating-requests">Authenticating requests</a>
                </li>
                            </ul>
                    <ul id="tocify-header-campaigns" class="tocify-header">
                <li class="tocify-item level-1" data-unique="campaigns">
                    <a href="#campaigns">Campaigns</a>
                </li>
                                    <ul id="tocify-subheader-campaigns" class="tocify-subheader">
                                                    <li class="tocify-item level-2" data-unique="campaigns-GETapi-v1-campaigns">
                                <a href="#campaigns-GETapi-v1-campaigns">Display a listing of campaigns</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="campaigns-POSTapi-v1-campaigns">
                                <a href="#campaigns-POSTapi-v1-campaigns">Create a new campaign</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="campaigns-GETapi-v1-campaigns--campaign_id--daily-stats">
                                <a href="#campaigns-GETapi-v1-campaigns--campaign_id--daily-stats">Fetch the daily campaign stats of a campaign</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="campaigns-GETapi-v1-campaigns--campaign_id--stats">
                                <a href="#campaigns-GETapi-v1-campaigns--campaign_id--stats">Fetch the total stats of a campaign</a>
                            </li>
                                                                        </ul>
                            </ul>
                    <ul id="tocify-header-clients" class="tocify-header">
                <li class="tocify-item level-1" data-unique="clients">
                    <a href="#clients">Clients</a>
                </li>
                                    <ul id="tocify-subheader-clients" class="tocify-subheader">
                                                    <li class="tocify-item level-2" data-unique="clients-GETapi-v1-clients">
                                <a href="#clients-GETapi-v1-clients">Display a user's accessible clients</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="clients-POSTapi-v1-clients">
                                <a href="#clients-POSTapi-v1-clients">Create a new client</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="clients-GETapi-v1-clients--client_id--users">
                                <a href="#clients-GETapi-v1-clients--client_id--users">Display a client's users</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="clients-POSTapi-v1-clients--client_id--users">
                                <a href="#clients-POSTapi-v1-clients--client_id--users">Add a new client user</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="clients-PUTapi-v1-clients--client_id--users">
                                <a href="#clients-PUTapi-v1-clients--client_id--users">Update a client user</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="clients-DELETEapi-v1-clients--client_id--users">
                                <a href="#clients-DELETEapi-v1-clients--client_id--users">Delete a client user</a>
                            </li>
                                                                        </ul>
                            </ul>
                    <ul id="tocify-header-contacts" class="tocify-header">
                <li class="tocify-item level-1" data-unique="contacts">
                    <a href="#contacts">Contacts</a>
                </li>
                                    <ul id="tocify-subheader-contacts" class="tocify-subheader">
                                                    <li class="tocify-item level-2" data-unique="contacts-POSTapi-v1-contacts">
                                <a href="#contacts-POSTapi-v1-contacts">Create a new contact</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="contacts-GETapi-v1-contacts--contact_id-">
                                <a href="#contacts-GETapi-v1-contacts--contact_id-">Display a contact</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="contacts-PUTapi-v1-contact-interest">
                                <a href="#contacts-PUTapi-v1-contact-interest">Update contact interest</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="contacts-DELETEapi-v1-contact-interest">
                                <a href="#contacts-DELETEapi-v1-contact-interest">Remove contact interest</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="contacts-GETapi-v1-campaigns--campaign_id--contacts">
                                <a href="#contacts-GETapi-v1-campaigns--campaign_id--contacts">Display a listing of contacts</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="contacts-GETapi-v1-contacts">
                                <a href="#contacts-GETapi-v1-contacts">Search for contacts</a>
                            </li>
                                                                        </ul>
                            </ul>
                    <ul id="tocify-header-email-messages" class="tocify-header">
                <li class="tocify-item level-1" data-unique="email-messages">
                    <a href="#email-messages">Email Messages</a>
                </li>
                                    <ul id="tocify-subheader-email-messages" class="tocify-subheader">
                                                    <li class="tocify-item level-2" data-unique="email-messages-GETapi-v1-campaigns--campaign_id--replies">
                                <a href="#email-messages-GETapi-v1-campaigns--campaign_id--replies">Display a listing of a campaign's replies.</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="email-messages-GETapi-v1-contacts--contact_id--replies">
                                <a href="#email-messages-GETapi-v1-contacts--contact_id--replies">Display a listing of a contact's replies.</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="email-messages-GETapi-v1-email-threads--email_thread_id-">
                                <a href="#email-messages-GETapi-v1-email-threads--email_thread_id-">Display an email thread.</a>
                            </li>
                                                                        </ul>
                            </ul>
                    <ul id="tocify-header-suppression" class="tocify-header">
                <li class="tocify-item level-1" data-unique="suppression">
                    <a href="#suppression">Suppression</a>
                </li>
                                    <ul id="tocify-subheader-suppression" class="tocify-subheader">
                                                    <li class="tocify-item level-2" data-unique="suppression-POSTapi-v1-suppressions-emails">
                                <a href="#suppression-POSTapi-v1-suppressions-emails">Add new email address to suppression list</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="suppression-POSTapi-v1-suppressions-domains">
                                <a href="#suppression-POSTapi-v1-suppressions-domains">Add new domain address to suppression list</a>
                            </li>
                                                                        </ul>
                            </ul>
            </div>

    <ul class="toc-footer" id="toc-footer">
                    <li style="padding-bottom: 5px;"><a href="{{ route("scribe.postman") }}">View Postman collection</a></li>
                            <li style="padding-bottom: 5px;"><a href="{{ route("scribe.openapi") }}">View OpenAPI spec</a></li>
                <li><a href="http://github.com/knuckleswtf/scribe">Documentation powered by Scribe ✍</a></li>
    </ul>

    <ul class="toc-footer" id="last-updated">
        <li>Last updated: March 2, 2023</li>
    </ul>
</div>

<div class="page-wrapper">
    <div class="dark-box"></div>
    <div class="content">
        <h1 id="introduction">Introduction</h1>
<p>Wavo Platform API reference</p>
<aside>
    <strong>Base URL</strong>: <code>https://app.wavo.co</code>
</aside>
<p>The Wavo Platform provides a modern REST API.</p>
<p>Requests support standard HTTP methods (GET, POST, PUT, DELETE). The API responses are JSON objects using standard HTTP status codes.</p>
<aside>As you scroll, you'll see code examples for working with the API in different programming languages in the dark area to the right (or as part of the content on mobile).
You can switch the language used with the tabs at the top right (or from the nav menu at the top left on mobile).</aside>

        <h1 id="authenticating-requests">Authenticating requests</h1>
<p>Authenticate requests to this API's endpoints by sending an <strong><code>Authorization</code></strong> header with the value <strong><code>"Bearer {ACCESS_TOKEN}"</code></strong>.</p>
<p>All authenticated endpoints are marked with a <code>requires authentication</code> badge in the documentation below.</p>
<p>You can retrieve your token by visiting your dashboard Settings, Third Party Integrations and <b>Generate API token</b>.</p>

        <h1 id="campaigns">Campaigns</h1>

    <p>APIs for managing campaigns.</p>

                                <h2 id="campaigns-GETapi-v1-campaigns">Display a listing of campaigns</h2>

<p>
<small class="badge badge-darkred">requires authentication</small>
</p>

<p>Return all campaigns accessible by a user.
If used by an agency with white-label dashboard subscription, an optional client_id parameter can be defined to return the campaigns of a specific client.</p>

<span id="example-requests-GETapi-v1-campaigns">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "https://app.wavo.co/api/v1/campaigns?client_id=ah83445df46as5432mga" \
    --header "Authorization: Bearer {token}" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://app.wavo.co/api/v1/campaigns"
);

const params = {
    "client_id": "ah83445df46as5432mga",
};
Object.keys(params)
    .forEach(key =&gt; url.searchParams.append(key, params[key]));

const headers = {
    "Authorization": "Bearer {token}",
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>


<div class="php-example">
    <pre><code class="language-php">$client = new \GuzzleHttp\Client();
$response = $client-&gt;get(
    'https://app.wavo.co/api/v1/campaigns',
    [
        'headers' =&gt; [
            'Authorization' =&gt; 'Bearer {token}',
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
        'query' =&gt; [
            'client_id' =&gt; 'ah83445df46as5432mga',
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre></div>


<div class="python-example">
    <pre><code class="language-python">import requests
import json

url = 'https://app.wavo.co/api/v1/campaigns'
params = {
  'client_id': 'ah83445df46as5432mga',
}
headers = {
  'Authorization': 'Bearer {token}',
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}

response = requests.request('GET', url, headers=headers, params=params)
response.json()</code></pre></div>

</span>

<span id="example-responses-GETapi-v1-campaigns">
            <blockquote>
            <p>Example response (200):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;data&quot;: [
        {
            &quot;id&quot;: &quot;3q4gezj87m5gn65x9rpo&quot;,
            &quot;name&quot;: &quot;Cremin LLC 13&quot;,
            &quot;timezone&quot;: &quot;US/Mountain&quot;,
            &quot;status&quot;: &quot;DRAFT&quot;,
            &quot;created_at&quot;: 1677779927,
            &quot;client&quot;: {
                &quot;id&quot;: &quot;xvey7lp3gzxew408zr9n&quot;,
                &quot;name&quot;: &quot;Cremin LLC&quot;,
                &quot;max_emails&quot;: 3,
                &quot;created_at&quot;: 1677779927
            }
        },
        {
            &quot;id&quot;: &quot;9zk26m1el250lpj8xvr3&quot;,
            &quot;name&quot;: &quot;Brekke LLC 4&quot;,
            &quot;timezone&quot;: &quot;US/Mountain&quot;,
            &quot;status&quot;: &quot;DRAFT&quot;,
            &quot;created_at&quot;: 1677779927,
            &quot;client&quot;: {
                &quot;id&quot;: &quot;dxj2z7kno39yo9lyv5qp&quot;,
                &quot;name&quot;: &quot;Brekke LLC&quot;,
                &quot;max_emails&quot;: 3,
                &quot;created_at&quot;: 1677779927
            }
        }
    ]
}</code>
 </pre>
    </span>
<span id="execution-results-GETapi-v1-campaigns" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-v1-campaigns"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-v1-campaigns" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-v1-campaigns" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-v1-campaigns"></code></pre>
</span>
<form id="form-GETapi-v1-campaigns" data-method="GET"
      data-path="api/v1/campaigns"
      data-authed="1"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-v1-campaigns', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/v1/campaigns</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Authorization</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Authorization" class="auth-value"               data-endpoint="GETapi-v1-campaigns"
               value="Bearer {token}"
               data-component="header">
    <br>
<p>Example: <code>Bearer {token}</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Content-Type"                data-endpoint="GETapi-v1-campaigns"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Accept"                data-endpoint="GETapi-v1-campaigns"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                            <h4 class="fancy-heading-panel"><b>Query Parameters</b></h4>
                                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>client_id</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
<i>optional</i> &nbsp;
                <input type="text" style="display: none"
               name="client_id"                data-endpoint="GETapi-v1-campaigns"
               value="ah83445df46as5432mga"
               data-component="query">
    <br>
<p>The id of a client. Example: <code>ah83445df46as5432mga</code></p>
            </div>
                </form>

                    <h2 id="campaigns-POSTapi-v1-campaigns">Create a new campaign</h2>

<p>
<small class="badge badge-darkred">requires authentication</small>
</p>

<p>Create a new campaign for a client.</p>

<span id="example-requests-POSTapi-v1-campaigns">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request POST \
    "https://app.wavo.co/api/v1/campaigns" \
    --header "Authorization: Bearer {token}" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json" \
    --data "{
    \"name\": \"aut\",
    \"client_id\": \"4a83445df46as5432mga\",
    \"timezone\": \"US\\/Mountain\"
}"
</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://app.wavo.co/api/v1/campaigns"
);

const headers = {
    "Authorization": "Bearer {token}",
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "name": "aut",
    "client_id": "4a83445df46as5432mga",
    "timezone": "US\/Mountain"
};

fetch(url, {
    method: "POST",
    headers,
    body: JSON.stringify(body),
}).then(response =&gt; response.json());</code></pre></div>


<div class="php-example">
    <pre><code class="language-php">$client = new \GuzzleHttp\Client();
$response = $client-&gt;post(
    'https://app.wavo.co/api/v1/campaigns',
    [
        'headers' =&gt; [
            'Authorization' =&gt; 'Bearer {token}',
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
        'json' =&gt; [
            'name' =&gt; 'aut',
            'client_id' =&gt; '4a83445df46as5432mga',
            'timezone' =&gt; 'US/Mountain',
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre></div>


<div class="python-example">
    <pre><code class="language-python">import requests
import json

url = 'https://app.wavo.co/api/v1/campaigns'
payload = {
    "name": "aut",
    "client_id": "4a83445df46as5432mga",
    "timezone": "US\/Mountain"
}
headers = {
  'Authorization': 'Bearer {token}',
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}

response = requests.request('POST', url, headers=headers, json=payload)
response.json()</code></pre></div>

</span>

<span id="example-responses-POSTapi-v1-campaigns">
            <blockquote>
            <p>Example response (200):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;data&quot;: {
        &quot;id&quot;: &quot;8rp5dxyqljmol4mv0goe&quot;,
        &quot;name&quot;: &quot;Satterfield and Sons 11&quot;,
        &quot;timezone&quot;: &quot;US/Mountain&quot;,
        &quot;status&quot;: &quot;DRAFT&quot;,
        &quot;created_at&quot;: 1677779927,
        &quot;client&quot;: {
            &quot;id&quot;: &quot;26mzne14wynrokl5djpq&quot;,
            &quot;name&quot;: &quot;Satterfield and Sons&quot;,
            &quot;max_emails&quot;: 3,
            &quot;created_at&quot;: 1677779927
        }
    }
}</code>
 </pre>
    </span>
<span id="execution-results-POSTapi-v1-campaigns" hidden>
    <blockquote>Received response<span
                id="execution-response-status-POSTapi-v1-campaigns"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-POSTapi-v1-campaigns" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-POSTapi-v1-campaigns" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-POSTapi-v1-campaigns"></code></pre>
</span>
<form id="form-POSTapi-v1-campaigns" data-method="POST"
      data-path="api/v1/campaigns"
      data-authed="1"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('POSTapi-v1-campaigns', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
            </h3>
            <p>
            <small class="badge badge-black">POST</small>
            <b><code>api/v1/campaigns</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Authorization</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Authorization" class="auth-value"               data-endpoint="POSTapi-v1-campaigns"
               value="Bearer {token}"
               data-component="header">
    <br>
<p>Example: <code>Bearer {token}</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Content-Type"                data-endpoint="POSTapi-v1-campaigns"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Accept"                data-endpoint="POSTapi-v1-campaigns"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <h4 class="fancy-heading-panel"><b>Body Parameters</b></h4>
        <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>name</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="name"                data-endpoint="POSTapi-v1-campaigns"
               value="aut"
               data-component="body">
    <br>
<p>The campaign name. Example: <code>aut</code></p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>client_id</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="client_id"                data-endpoint="POSTapi-v1-campaigns"
               value="4a83445df46as5432mga"
               data-component="body">
    <br>
<p>The id of the client. Example: <code>4a83445df46as5432mga</code></p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>timezone</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="timezone"                data-endpoint="POSTapi-v1-campaigns"
               value="US/Mountain"
               data-component="body">
    <br>
<p>The timezone that the campaign's schedule should use. Example: <code>US/Mountain</code></p>
        </div>
        </form>

                    <h2 id="campaigns-GETapi-v1-campaigns--campaign_id--daily-stats">Fetch the daily campaign stats of a campaign</h2>

<p>
<small class="badge badge-darkred">requires authentication</small>
</p>

<p>Return all daily campaign stats of a campaign.
To limit results, a query parameter <code>start</code> can be sent, so that the response contains only stats for days after a specific timestamp.</p>

<span id="example-requests-GETapi-v1-campaigns--campaign_id--daily-stats">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "https://app.wavo.co/api/v1/campaigns/1/daily-stats?start=1662940800" \
    --header "Authorization: Bearer {token}" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://app.wavo.co/api/v1/campaigns/1/daily-stats"
);

const params = {
    "start": "1662940800",
};
Object.keys(params)
    .forEach(key =&gt; url.searchParams.append(key, params[key]));

const headers = {
    "Authorization": "Bearer {token}",
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>


<div class="php-example">
    <pre><code class="language-php">$client = new \GuzzleHttp\Client();
$response = $client-&gt;get(
    'https://app.wavo.co/api/v1/campaigns/1/daily-stats',
    [
        'headers' =&gt; [
            'Authorization' =&gt; 'Bearer {token}',
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
        'query' =&gt; [
            'start' =&gt; '1662940800',
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre></div>


<div class="python-example">
    <pre><code class="language-python">import requests
import json

url = 'https://app.wavo.co/api/v1/campaigns/1/daily-stats'
params = {
  'start': '1662940800',
}
headers = {
  'Authorization': 'Bearer {token}',
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}

response = requests.request('GET', url, headers=headers, params=params)
response.json()</code></pre></div>

</span>

<span id="example-responses-GETapi-v1-campaigns--campaign_id--daily-stats">
            <blockquote>
            <p>Example response (200):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;data&quot;: [
        {
            &quot;day&quot;: &quot;2022-09-12T00:00:00.000000Z&quot;,
            &quot;contacted&quot;: 11,
            &quot;replied&quot;: 1,
            &quot;autoreplied&quot;: 0,
            &quot;bounced&quot;: 4,
            &quot;positive&quot;: 1,
            &quot;neutral&quot;: 0,
            &quot;negative&quot;: 4,
            &quot;messages_clicked&quot;: 5,
            &quot;messages_opened&quot;: 0,
            &quot;messages_sent&quot;: 15
        },
        {
            &quot;day&quot;: &quot;2022-09-13T00:00:00.000000Z&quot;,
            &quot;contacted&quot;: 15,
            &quot;replied&quot;: 3,
            &quot;autoreplied&quot;: 1,
            &quot;bounced&quot;: 2,
            &quot;positive&quot;: 2,
            &quot;neutral&quot;: 1,
            &quot;negative&quot;: 2,
            &quot;messages_clicked&quot;: 6,
            &quot;messages_opened&quot;: 0,
            &quot;messages_sent&quot;: 17
        }
    ]
}</code>
 </pre>
    </span>
<span id="execution-results-GETapi-v1-campaigns--campaign_id--daily-stats" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-v1-campaigns--campaign_id--daily-stats"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-v1-campaigns--campaign_id--daily-stats" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-v1-campaigns--campaign_id--daily-stats" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-v1-campaigns--campaign_id--daily-stats"></code></pre>
</span>
<form id="form-GETapi-v1-campaigns--campaign_id--daily-stats" data-method="GET"
      data-path="api/v1/campaigns/{campaign_id}/daily-stats"
      data-authed="1"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-v1-campaigns--campaign_id--daily-stats', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/v1/campaigns/{campaign_id}/daily-stats</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Authorization</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Authorization" class="auth-value"               data-endpoint="GETapi-v1-campaigns--campaign_id--daily-stats"
               value="Bearer {token}"
               data-component="header">
    <br>
<p>Example: <code>Bearer {token}</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Content-Type"                data-endpoint="GETapi-v1-campaigns--campaign_id--daily-stats"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Accept"                data-endpoint="GETapi-v1-campaigns--campaign_id--daily-stats"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        <h4 class="fancy-heading-panel"><b>URL Parameters</b></h4>
                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>campaign_id</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
                <input type="number" style="display: none"
               name="campaign_id"                data-endpoint="GETapi-v1-campaigns--campaign_id--daily-stats"
               value="1"
               data-component="url">
    <br>
<p>The ID of the campaign. Example: <code>1</code></p>
            </div>
                        <h4 class="fancy-heading-panel"><b>Query Parameters</b></h4>
                                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>start</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
<i>optional</i> &nbsp;
                <input type="text" style="display: none"
               name="start"                data-endpoint="GETapi-v1-campaigns--campaign_id--daily-stats"
               value="1662940800"
               data-component="query">
    <br>
<p>The timestamp after which to show stats. Example: <code>1662940800</code></p>
            </div>
                </form>

                    <h2 id="campaigns-GETapi-v1-campaigns--campaign_id--stats">Fetch the total stats of a campaign</h2>

<p>
<small class="badge badge-darkred">requires authentication</small>
</p>



<span id="example-requests-GETapi-v1-campaigns--campaign_id--stats">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "https://app.wavo.co/api/v1/campaigns/1/stats" \
    --header "Authorization: Bearer {token}" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://app.wavo.co/api/v1/campaigns/1/stats"
);

const headers = {
    "Authorization": "Bearer {token}",
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>


<div class="php-example">
    <pre><code class="language-php">$client = new \GuzzleHttp\Client();
$response = $client-&gt;get(
    'https://app.wavo.co/api/v1/campaigns/1/stats',
    [
        'headers' =&gt; [
            'Authorization' =&gt; 'Bearer {token}',
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre></div>


<div class="python-example">
    <pre><code class="language-python">import requests
import json

url = 'https://app.wavo.co/api/v1/campaigns/1/stats'
headers = {
  'Authorization': 'Bearer {token}',
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}

response = requests.request('GET', url, headers=headers)
response.json()</code></pre></div>

</span>

<span id="example-responses-GETapi-v1-campaigns--campaign_id--stats">
            <blockquote>
            <p>Example response (200):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;data&quot;: {
        &quot;contacted&quot;: 5,
        &quot;replied&quot;: 1,
        &quot;autoreplied&quot;: 0,
        &quot;bounced&quot;: 0,
        &quot;positive&quot;: 1,
        &quot;neutral&quot;: 0,
        &quot;negative&quot;: 0,
        &quot;messages_clicked&quot;: 0,
        &quot;messages_opened&quot;: 0,
        &quot;messages_sent&quot;: 5,
        &quot;campaign_steps&quot;: [
            {
                &quot;number&quot;: 1,
                &quot;sent&quot;: 5,
                &quot;queued&quot;: 0,
                &quot;contacted&quot;: 5,
                &quot;replied&quot;: 1,
                &quot;positive&quot;: 1,
                &quot;bounced&quot;: 0,
                &quot;messages_clicked&quot;: 0,
                &quot;messages_opened&quot;: 0
            },
            {
                &quot;number&quot;: 2,
                &quot;sent&quot;: 0,
                &quot;queued&quot;: 4,
                &quot;contacted&quot;: 0,
                &quot;replied&quot;: 0,
                &quot;positive&quot;: 0,
                &quot;bounced&quot;: 0,
                &quot;messages_clicked&quot;: 0,
                &quot;messages_opened&quot;: 0
            }
        ]
    }
}</code>
 </pre>
    </span>
<span id="execution-results-GETapi-v1-campaigns--campaign_id--stats" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-v1-campaigns--campaign_id--stats"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-v1-campaigns--campaign_id--stats" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-v1-campaigns--campaign_id--stats" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-v1-campaigns--campaign_id--stats"></code></pre>
</span>
<form id="form-GETapi-v1-campaigns--campaign_id--stats" data-method="GET"
      data-path="api/v1/campaigns/{campaign_id}/stats"
      data-authed="1"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-v1-campaigns--campaign_id--stats', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/v1/campaigns/{campaign_id}/stats</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Authorization</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Authorization" class="auth-value"               data-endpoint="GETapi-v1-campaigns--campaign_id--stats"
               value="Bearer {token}"
               data-component="header">
    <br>
<p>Example: <code>Bearer {token}</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Content-Type"                data-endpoint="GETapi-v1-campaigns--campaign_id--stats"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Accept"                data-endpoint="GETapi-v1-campaigns--campaign_id--stats"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        <h4 class="fancy-heading-panel"><b>URL Parameters</b></h4>
                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>campaign_id</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
                <input type="number" style="display: none"
               name="campaign_id"                data-endpoint="GETapi-v1-campaigns--campaign_id--stats"
               value="1"
               data-component="url">
    <br>
<p>The ID of the campaign. Example: <code>1</code></p>
            </div>
                    </form>

                <h1 id="clients">Clients</h1>

    <p>APIs for managing clients.</p>

                                <h2 id="clients-GETapi-v1-clients">Display a user&#039;s accessible clients</h2>

<p>
<small class="badge badge-darkred">requires authentication</small>
</p>

<p>This will return an array of 1 or more clients (for users on white-label dashboard subscription).</p>

<span id="example-requests-GETapi-v1-clients">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "https://app.wavo.co/api/v1/clients" \
    --header "Authorization: Bearer {token}" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://app.wavo.co/api/v1/clients"
);

const headers = {
    "Authorization": "Bearer {token}",
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>


<div class="php-example">
    <pre><code class="language-php">$client = new \GuzzleHttp\Client();
$response = $client-&gt;get(
    'https://app.wavo.co/api/v1/clients',
    [
        'headers' =&gt; [
            'Authorization' =&gt; 'Bearer {token}',
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre></div>


<div class="python-example">
    <pre><code class="language-python">import requests
import json

url = 'https://app.wavo.co/api/v1/clients'
headers = {
  'Authorization': 'Bearer {token}',
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}

response = requests.request('GET', url, headers=headers)
response.json()</code></pre></div>

</span>

<span id="example-responses-GETapi-v1-clients">
            <blockquote>
            <p>Example response (200):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;data&quot;: [
        {
            &quot;id&quot;: &quot;kn9x5qyro086w4z6pd37&quot;,
            &quot;name&quot;: &quot;Roberts PLC&quot;,
            &quot;max_emails&quot;: 3,
            &quot;created_at&quot;: 1677779927
        },
        {
            &quot;id&quot;: &quot;41z52kqnwjepoxplrv80&quot;,
            &quot;name&quot;: &quot;Feeney LLC&quot;,
            &quot;max_emails&quot;: 3,
            &quot;created_at&quot;: 1677779927
        }
    ]
}</code>
 </pre>
    </span>
<span id="execution-results-GETapi-v1-clients" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-v1-clients"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-v1-clients" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-v1-clients" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-v1-clients"></code></pre>
</span>
<form id="form-GETapi-v1-clients" data-method="GET"
      data-path="api/v1/clients"
      data-authed="1"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-v1-clients', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/v1/clients</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Authorization</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Authorization" class="auth-value"               data-endpoint="GETapi-v1-clients"
               value="Bearer {token}"
               data-component="header">
    <br>
<p>Example: <code>Bearer {token}</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Content-Type"                data-endpoint="GETapi-v1-clients"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Accept"                data-endpoint="GETapi-v1-clients"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        </form>

                    <h2 id="clients-POSTapi-v1-clients">Create a new client</h2>

<p>
<small class="badge badge-darkred">requires authentication</small>
</p>

<p>Add a new client to the user's agency</p>

<span id="example-requests-POSTapi-v1-clients">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request POST \
    "https://app.wavo.co/api/v1/clients" \
    --header "Authorization: Bearer {token}" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json" \
    --data "{
    \"name\": \"Thompson LLC\",
    \"max_emails\": 3
}"
</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://app.wavo.co/api/v1/clients"
);

const headers = {
    "Authorization": "Bearer {token}",
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "name": "Thompson LLC",
    "max_emails": 3
};

fetch(url, {
    method: "POST",
    headers,
    body: JSON.stringify(body),
}).then(response =&gt; response.json());</code></pre></div>


<div class="php-example">
    <pre><code class="language-php">$client = new \GuzzleHttp\Client();
$response = $client-&gt;post(
    'https://app.wavo.co/api/v1/clients',
    [
        'headers' =&gt; [
            'Authorization' =&gt; 'Bearer {token}',
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
        'json' =&gt; [
            'name' =&gt; 'Thompson LLC',
            'max_emails' =&gt; 3,
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre></div>


<div class="python-example">
    <pre><code class="language-python">import requests
import json

url = 'https://app.wavo.co/api/v1/clients'
payload = {
    "name": "Thompson LLC",
    "max_emails": 3
}
headers = {
  'Authorization': 'Bearer {token}',
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}

response = requests.request('POST', url, headers=headers, json=payload)
response.json()</code></pre></div>

</span>

<span id="example-responses-POSTapi-v1-clients">
            <blockquote>
            <p>Example response (201):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;data&quot;: {
        &quot;id&quot;: &quot;l8re092o9qv7w745jmpk&quot;,
        &quot;name&quot;: &quot;Thompson LLC&quot;,
        &quot;max_emails&quot;: 3,
        &quot;created_at&quot;: 1580411327
    }
}</code>
 </pre>
    </span>
<span id="execution-results-POSTapi-v1-clients" hidden>
    <blockquote>Received response<span
                id="execution-response-status-POSTapi-v1-clients"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-POSTapi-v1-clients" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-POSTapi-v1-clients" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-POSTapi-v1-clients"></code></pre>
</span>
<form id="form-POSTapi-v1-clients" data-method="POST"
      data-path="api/v1/clients"
      data-authed="1"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('POSTapi-v1-clients', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
            </h3>
            <p>
            <small class="badge badge-black">POST</small>
            <b><code>api/v1/clients</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Authorization</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Authorization" class="auth-value"               data-endpoint="POSTapi-v1-clients"
               value="Bearer {token}"
               data-component="header">
    <br>
<p>Example: <code>Bearer {token}</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Content-Type"                data-endpoint="POSTapi-v1-clients"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Accept"                data-endpoint="POSTapi-v1-clients"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <h4 class="fancy-heading-panel"><b>Body Parameters</b></h4>
        <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>name</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="name"                data-endpoint="POSTapi-v1-clients"
               value="Thompson LLC"
               data-component="body">
    <br>
<p>The client name or title. Example: <code>Thompson LLC</code></p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>max_emails</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
                <input type="number" style="display: none"
               name="max_emails"                data-endpoint="POSTapi-v1-clients"
               value="3"
               data-component="body">
    <br>
<p>The amount of max emails the client can add manually. Example: <code>3</code></p>
        </div>
        </form>

                    <h2 id="clients-GETapi-v1-clients--client_id--users">Display a client&#039;s users</h2>

<p>
<small class="badge badge-darkred">requires authentication</small>
</p>

<p>Show all active users of a client as well as pending user invitations, with their assigned roles.</p>

<span id="example-requests-GETapi-v1-clients--client_id--users">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "https://app.wavo.co/api/v1/clients/a83445df46as5432mga/users" \
    --header "Authorization: Bearer {token}" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://app.wavo.co/api/v1/clients/a83445df46as5432mga/users"
);

const headers = {
    "Authorization": "Bearer {token}",
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>


<div class="php-example">
    <pre><code class="language-php">$client = new \GuzzleHttp\Client();
$response = $client-&gt;get(
    'https://app.wavo.co/api/v1/clients/a83445df46as5432mga/users',
    [
        'headers' =&gt; [
            'Authorization' =&gt; 'Bearer {token}',
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre></div>


<div class="python-example">
    <pre><code class="language-python">import requests
import json

url = 'https://app.wavo.co/api/v1/clients/a83445df46as5432mga/users'
headers = {
  'Authorization': 'Bearer {token}',
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}

response = requests.request('GET', url, headers=headers)
response.json()</code></pre></div>

</span>

<span id="example-responses-GETapi-v1-clients--client_id--users">
            <blockquote>
            <p>Example response (200):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;data&quot;: {
        &quot;users&quot;: [
            {
                &quot;name&quot;: &quot;Mike Jones&quot;,
                &quot;email&quot;: &quot;<EMAIL>&quot;,
                &quot;created_at&quot;: 1580412998,
                &quot;roles&quot;: [
                    &quot;view_campaigns&quot;
                ]
            },
            {
                &quot;name&quot;: &quot;Bill Scott&quot;,
                &quot;email&quot;: &quot;<EMAIL>&quot;,
                &quot;created_at&quot;: 1580412998,
                &quot;roles&quot;: [
                    &quot;view_campaigns&quot;,
                    &quot;create_campaigns&quot;
                ]
            }
        ],
        &quot;invitations&quot;: [
            {
                &quot;email&quot;: &quot;<EMAIL>&quot;,
                &quot;created_at&quot;: 1580412998,
                &quot;roles&quot;: [
                    &quot;create_campaigns&quot;,
                    &quot;export_data&quot;,
                    &quot;view_campaigns&quot;
                ]
            }
        ]
    }
}</code>
 </pre>
    </span>
<span id="execution-results-GETapi-v1-clients--client_id--users" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-v1-clients--client_id--users"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-v1-clients--client_id--users" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-v1-clients--client_id--users" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-v1-clients--client_id--users"></code></pre>
</span>
<form id="form-GETapi-v1-clients--client_id--users" data-method="GET"
      data-path="api/v1/clients/{client_id}/users"
      data-authed="1"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-v1-clients--client_id--users', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/v1/clients/{client_id}/users</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Authorization</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Authorization" class="auth-value"               data-endpoint="GETapi-v1-clients--client_id--users"
               value="Bearer {token}"
               data-component="header">
    <br>
<p>Example: <code>Bearer {token}</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Content-Type"                data-endpoint="GETapi-v1-clients--client_id--users"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Accept"                data-endpoint="GETapi-v1-clients--client_id--users"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        <h4 class="fancy-heading-panel"><b>URL Parameters</b></h4>
                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>client_id</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="client_id"                data-endpoint="GETapi-v1-clients--client_id--users"
               value="a83445df46as5432mga"
               data-component="url">
    <br>
<p>The id of a client. Example: <code>a83445df46as5432mga</code></p>
            </div>
                    </form>

                    <h2 id="clients-POSTapi-v1-clients--client_id--users">Add a new client user</h2>

<p>
<small class="badge badge-darkred">requires authentication</small>
</p>

<p>Add a new user to a client. Specify the user's email address and the roles he should have.
Available roles are:</p>
<ul>
<li>view_campaigns</li>
<li>create_campaigns</li>
<li>export_campaigns</li>
</ul>

<span id="example-requests-POSTapi-v1-clients--client_id--users">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request POST \
    "https://app.wavo.co/api/v1/clients/a83445df46as5432mga/users" \
    --header "Authorization: Bearer {token}" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json" \
    --data "{
    \"email\": \"<EMAIL>\",
    \"view_campaigns\": true,
    \"create_campaigns\": true,
    \"export_campaigns\": true
}"
</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://app.wavo.co/api/v1/clients/a83445df46as5432mga/users"
);

const headers = {
    "Authorization": "Bearer {token}",
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "email": "<EMAIL>",
    "view_campaigns": true,
    "create_campaigns": true,
    "export_campaigns": true
};

fetch(url, {
    method: "POST",
    headers,
    body: JSON.stringify(body),
}).then(response =&gt; response.json());</code></pre></div>


<div class="php-example">
    <pre><code class="language-php">$client = new \GuzzleHttp\Client();
$response = $client-&gt;post(
    'https://app.wavo.co/api/v1/clients/a83445df46as5432mga/users',
    [
        'headers' =&gt; [
            'Authorization' =&gt; 'Bearer {token}',
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
        'json' =&gt; [
            'email' =&gt; '<EMAIL>',
            'view_campaigns' =&gt; true,
            'create_campaigns' =&gt; true,
            'export_campaigns' =&gt; true,
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre></div>


<div class="python-example">
    <pre><code class="language-python">import requests
import json

url = 'https://app.wavo.co/api/v1/clients/a83445df46as5432mga/users'
payload = {
    "email": "<EMAIL>",
    "view_campaigns": true,
    "create_campaigns": true,
    "export_campaigns": true
}
headers = {
  'Authorization': 'Bearer {token}',
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}

response = requests.request('POST', url, headers=headers, json=payload)
response.json()</code></pre></div>

</span>

<span id="example-responses-POSTapi-v1-clients--client_id--users">
            <blockquote>
            <p>Example response (200):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;message&quot;: &quot;An invitation was <NAME_EMAIL>&quot;
}</code>
 </pre>
    </span>
<span id="execution-results-POSTapi-v1-clients--client_id--users" hidden>
    <blockquote>Received response<span
                id="execution-response-status-POSTapi-v1-clients--client_id--users"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-POSTapi-v1-clients--client_id--users" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-POSTapi-v1-clients--client_id--users" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-POSTapi-v1-clients--client_id--users"></code></pre>
</span>
<form id="form-POSTapi-v1-clients--client_id--users" data-method="POST"
      data-path="api/v1/clients/{client_id}/users"
      data-authed="1"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('POSTapi-v1-clients--client_id--users', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
            </h3>
            <p>
            <small class="badge badge-black">POST</small>
            <b><code>api/v1/clients/{client_id}/users</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Authorization</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Authorization" class="auth-value"               data-endpoint="POSTapi-v1-clients--client_id--users"
               value="Bearer {token}"
               data-component="header">
    <br>
<p>Example: <code>Bearer {token}</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Content-Type"                data-endpoint="POSTapi-v1-clients--client_id--users"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Accept"                data-endpoint="POSTapi-v1-clients--client_id--users"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        <h4 class="fancy-heading-panel"><b>URL Parameters</b></h4>
                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>client_id</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="client_id"                data-endpoint="POSTapi-v1-clients--client_id--users"
               value="a83445df46as5432mga"
               data-component="url">
    <br>
<p>The id of a client. Example: <code>a83445df46as5432mga</code></p>
            </div>
                            <h4 class="fancy-heading-panel"><b>Body Parameters</b></h4>
        <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>email</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="email"                data-endpoint="POSTapi-v1-clients--client_id--users"
               value="<EMAIL>"
               data-component="body">
    <br>
<p>The email address of the user. Example: <code><EMAIL></code></p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>view_campaigns</code></b>&nbsp;&nbsp;
<small>boolean</small>&nbsp;
<i>optional</i> &nbsp;
                <label data-endpoint="POSTapi-v1-clients--client_id--users" style="display: none">
            <input type="radio" name="view_campaigns"
                   value="true"
                   data-endpoint="POSTapi-v1-clients--client_id--users"
                   data-component="body"             >
            <code>true</code>
        </label>
        <label data-endpoint="POSTapi-v1-clients--client_id--users" style="display: none">
            <input type="radio" name="view_campaigns"
                   value="false"
                   data-endpoint="POSTapi-v1-clients--client_id--users"
                   data-component="body"             >
            <code>false</code>
        </label>
    <br>
<p>Whether the user will be able to view campaigns. Example: <code>true</code></p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>create_campaigns</code></b>&nbsp;&nbsp;
<small>boolean</small>&nbsp;
<i>optional</i> &nbsp;
                <label data-endpoint="POSTapi-v1-clients--client_id--users" style="display: none">
            <input type="radio" name="create_campaigns"
                   value="true"
                   data-endpoint="POSTapi-v1-clients--client_id--users"
                   data-component="body"             >
            <code>true</code>
        </label>
        <label data-endpoint="POSTapi-v1-clients--client_id--users" style="display: none">
            <input type="radio" name="create_campaigns"
                   value="false"
                   data-endpoint="POSTapi-v1-clients--client_id--users"
                   data-component="body"             >
            <code>false</code>
        </label>
    <br>
<p>Whether the user will be able to create new campaigns. Example: <code>true</code></p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>export_campaigns</code></b>&nbsp;&nbsp;
<small>boolean</small>&nbsp;
<i>optional</i> &nbsp;
                <label data-endpoint="POSTapi-v1-clients--client_id--users" style="display: none">
            <input type="radio" name="export_campaigns"
                   value="true"
                   data-endpoint="POSTapi-v1-clients--client_id--users"
                   data-component="body"             >
            <code>true</code>
        </label>
        <label data-endpoint="POSTapi-v1-clients--client_id--users" style="display: none">
            <input type="radio" name="export_campaigns"
                   value="false"
                   data-endpoint="POSTapi-v1-clients--client_id--users"
                   data-component="body"             >
            <code>false</code>
        </label>
    <br>
<p>Whether the user will be able export campaign contacts. Example: <code>true</code></p>
        </div>
        </form>

                    <h2 id="clients-PUTapi-v1-clients--client_id--users">Update a client user</h2>

<p>
<small class="badge badge-darkred">requires authentication</small>
</p>

<p>Update a client user's roles. Can update roles of active users or user invitations.
Available roles are:</p>
<ul>
<li>view_campaigns</li>
<li>create_campaigns</li>
<li>export_campaigns</li>
</ul>

<span id="example-requests-PUTapi-v1-clients--client_id--users">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request PUT \
    "https://app.wavo.co/api/v1/clients/a83445df46as5432mga/users" \
    --header "Authorization: Bearer {token}" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json" \
    --data "{
    \"email\": \"<EMAIL>\",
    \"view_campaigns\": true,
    \"create_campaigns\": true,
    \"export_campaigns\": true
}"
</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://app.wavo.co/api/v1/clients/a83445df46as5432mga/users"
);

const headers = {
    "Authorization": "Bearer {token}",
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "email": "<EMAIL>",
    "view_campaigns": true,
    "create_campaigns": true,
    "export_campaigns": true
};

fetch(url, {
    method: "PUT",
    headers,
    body: JSON.stringify(body),
}).then(response =&gt; response.json());</code></pre></div>


<div class="php-example">
    <pre><code class="language-php">$client = new \GuzzleHttp\Client();
$response = $client-&gt;put(
    'https://app.wavo.co/api/v1/clients/a83445df46as5432mga/users',
    [
        'headers' =&gt; [
            'Authorization' =&gt; 'Bearer {token}',
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
        'json' =&gt; [
            'email' =&gt; '<EMAIL>',
            'view_campaigns' =&gt; true,
            'create_campaigns' =&gt; true,
            'export_campaigns' =&gt; true,
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre></div>


<div class="python-example">
    <pre><code class="language-python">import requests
import json

url = 'https://app.wavo.co/api/v1/clients/a83445df46as5432mga/users'
payload = {
    "email": "<EMAIL>",
    "view_campaigns": true,
    "create_campaigns": true,
    "export_campaigns": true
}
headers = {
  'Authorization': 'Bearer {token}',
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}

response = requests.request('PUT', url, headers=headers, json=payload)
response.json()</code></pre></div>

</span>

<span id="example-responses-PUTapi-v1-clients--client_id--users">
            <blockquote>
            <p>Example response (200):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;message&quot;: &quot;Success. Updated user roles.&quot;
}</code>
 </pre>
    </span>
<span id="execution-results-PUTapi-v1-clients--client_id--users" hidden>
    <blockquote>Received response<span
                id="execution-response-status-PUTapi-v1-clients--client_id--users"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-PUTapi-v1-clients--client_id--users" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-PUTapi-v1-clients--client_id--users" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-PUTapi-v1-clients--client_id--users"></code></pre>
</span>
<form id="form-PUTapi-v1-clients--client_id--users" data-method="PUT"
      data-path="api/v1/clients/{client_id}/users"
      data-authed="1"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('PUTapi-v1-clients--client_id--users', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
            </h3>
            <p>
            <small class="badge badge-darkblue">PUT</small>
            <b><code>api/v1/clients/{client_id}/users</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Authorization</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Authorization" class="auth-value"               data-endpoint="PUTapi-v1-clients--client_id--users"
               value="Bearer {token}"
               data-component="header">
    <br>
<p>Example: <code>Bearer {token}</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Content-Type"                data-endpoint="PUTapi-v1-clients--client_id--users"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Accept"                data-endpoint="PUTapi-v1-clients--client_id--users"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        <h4 class="fancy-heading-panel"><b>URL Parameters</b></h4>
                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>client_id</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="client_id"                data-endpoint="PUTapi-v1-clients--client_id--users"
               value="a83445df46as5432mga"
               data-component="url">
    <br>
<p>The id of a client. Example: <code>a83445df46as5432mga</code></p>
            </div>
                            <h4 class="fancy-heading-panel"><b>Body Parameters</b></h4>
        <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>email</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="email"                data-endpoint="PUTapi-v1-clients--client_id--users"
               value="<EMAIL>"
               data-component="body">
    <br>
<p>The email address of the user. Example: <code><EMAIL></code></p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>view_campaigns</code></b>&nbsp;&nbsp;
<small>boolean</small>&nbsp;
<i>optional</i> &nbsp;
                <label data-endpoint="PUTapi-v1-clients--client_id--users" style="display: none">
            <input type="radio" name="view_campaigns"
                   value="true"
                   data-endpoint="PUTapi-v1-clients--client_id--users"
                   data-component="body"             >
            <code>true</code>
        </label>
        <label data-endpoint="PUTapi-v1-clients--client_id--users" style="display: none">
            <input type="radio" name="view_campaigns"
                   value="false"
                   data-endpoint="PUTapi-v1-clients--client_id--users"
                   data-component="body"             >
            <code>false</code>
        </label>
    <br>
<p>Whether the user will be able to view campaigns. Example: <code>true</code></p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>create_campaigns</code></b>&nbsp;&nbsp;
<small>boolean</small>&nbsp;
<i>optional</i> &nbsp;
                <label data-endpoint="PUTapi-v1-clients--client_id--users" style="display: none">
            <input type="radio" name="create_campaigns"
                   value="true"
                   data-endpoint="PUTapi-v1-clients--client_id--users"
                   data-component="body"             >
            <code>true</code>
        </label>
        <label data-endpoint="PUTapi-v1-clients--client_id--users" style="display: none">
            <input type="radio" name="create_campaigns"
                   value="false"
                   data-endpoint="PUTapi-v1-clients--client_id--users"
                   data-component="body"             >
            <code>false</code>
        </label>
    <br>
<p>Whether the user will be able to create new campaigns. Example: <code>true</code></p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>export_campaigns</code></b>&nbsp;&nbsp;
<small>boolean</small>&nbsp;
<i>optional</i> &nbsp;
                <label data-endpoint="PUTapi-v1-clients--client_id--users" style="display: none">
            <input type="radio" name="export_campaigns"
                   value="true"
                   data-endpoint="PUTapi-v1-clients--client_id--users"
                   data-component="body"             >
            <code>true</code>
        </label>
        <label data-endpoint="PUTapi-v1-clients--client_id--users" style="display: none">
            <input type="radio" name="export_campaigns"
                   value="false"
                   data-endpoint="PUTapi-v1-clients--client_id--users"
                   data-component="body"             >
            <code>false</code>
        </label>
    <br>
<p>Whether the user will be able export campaign contacts. Example: <code>true</code></p>
        </div>
        </form>

                    <h2 id="clients-DELETEapi-v1-clients--client_id--users">Delete a client user</h2>

<p>
<small class="badge badge-darkred">requires authentication</small>
</p>

<p>Remove a user from a client.</p>

<span id="example-requests-DELETEapi-v1-clients--client_id--users">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request DELETE \
    "https://app.wavo.co/api/v1/clients/a83445df46as5432mga/users?email=mike%40wavo.co" \
    --header "Authorization: Bearer {token}" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json" \
    --data "{
    \"email\": \"<EMAIL>\"
}"
</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://app.wavo.co/api/v1/clients/a83445df46as5432mga/users"
);

const params = {
    "email": "<EMAIL>",
};
Object.keys(params)
    .forEach(key =&gt; url.searchParams.append(key, params[key]));

const headers = {
    "Authorization": "Bearer {token}",
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "email": "<EMAIL>"
};

fetch(url, {
    method: "DELETE",
    headers,
    body: JSON.stringify(body),
}).then(response =&gt; response.json());</code></pre></div>


<div class="php-example">
    <pre><code class="language-php">$client = new \GuzzleHttp\Client();
$response = $client-&gt;delete(
    'https://app.wavo.co/api/v1/clients/a83445df46as5432mga/users',
    [
        'headers' =&gt; [
            'Authorization' =&gt; 'Bearer {token}',
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
        'query' =&gt; [
            'email' =&gt; '<EMAIL>',
        ],
        'json' =&gt; [
            'email' =&gt; '<EMAIL>',
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre></div>


<div class="python-example">
    <pre><code class="language-python">import requests
import json

url = 'https://app.wavo.co/api/v1/clients/a83445df46as5432mga/users'
payload = {
    "email": "<EMAIL>"
}
params = {
  'email': '<EMAIL>',
}
headers = {
  'Authorization': 'Bearer {token}',
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}

response = requests.request('DELETE', url, headers=headers, json=payload, params=params)
response.json()</code></pre></div>

</span>

<span id="example-responses-DELETEapi-v1-clients--client_id--users">
            <blockquote>
            <p>Example response (204):</p>
        </blockquote>
                <pre>
<code>[Empty response]</code>
 </pre>
    </span>
<span id="execution-results-DELETEapi-v1-clients--client_id--users" hidden>
    <blockquote>Received response<span
                id="execution-response-status-DELETEapi-v1-clients--client_id--users"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-DELETEapi-v1-clients--client_id--users" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-DELETEapi-v1-clients--client_id--users" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-DELETEapi-v1-clients--client_id--users"></code></pre>
</span>
<form id="form-DELETEapi-v1-clients--client_id--users" data-method="DELETE"
      data-path="api/v1/clients/{client_id}/users"
      data-authed="1"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('DELETEapi-v1-clients--client_id--users', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
            </h3>
            <p>
            <small class="badge badge-red">DELETE</small>
            <b><code>api/v1/clients/{client_id}/users</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Authorization</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Authorization" class="auth-value"               data-endpoint="DELETEapi-v1-clients--client_id--users"
               value="Bearer {token}"
               data-component="header">
    <br>
<p>Example: <code>Bearer {token}</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Content-Type"                data-endpoint="DELETEapi-v1-clients--client_id--users"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Accept"                data-endpoint="DELETEapi-v1-clients--client_id--users"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        <h4 class="fancy-heading-panel"><b>URL Parameters</b></h4>
                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>client_id</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="client_id"                data-endpoint="DELETEapi-v1-clients--client_id--users"
               value="a83445df46as5432mga"
               data-component="url">
    <br>
<p>The id of a client. Example: <code>a83445df46as5432mga</code></p>
            </div>
                        <h4 class="fancy-heading-panel"><b>Query Parameters</b></h4>
                                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>email</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="email"                data-endpoint="DELETEapi-v1-clients--client_id--users"
               value="<EMAIL>"
               data-component="query">
    <br>
<p>The email address of the user. Example: <code><EMAIL></code></p>
            </div>
                        <h4 class="fancy-heading-panel"><b>Body Parameters</b></h4>
        <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>email</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="email"                data-endpoint="DELETEapi-v1-clients--client_id--users"
               value="<EMAIL>"
               data-component="body">
    <br>
<p>Must be a valid email address. Must not be greater than 255 characters. Example: <code><EMAIL></code></p>
        </div>
        </form>

                <h1 id="contacts">Contacts</h1>

    <p>APIs for managing contacts.</p>

                                <h2 id="contacts-POSTapi-v1-contacts">Create a new contact</h2>

<p>
<small class="badge badge-darkred">requires authentication</small>
</p>

<p>A new contact can be attached to an existing campaign. As such the campaign_id is required</p>

<span id="example-requests-POSTapi-v1-contacts">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request POST \
    "https://app.wavo.co/api/v1/contacts" \
    --header "Authorization: Bearer {token}" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json" \
    --data "{
    \"campaign_id\": \"fkjtf4390kfgu8903nsk\",
    \"email\": \"<EMAIL>\",
    \"first_name\": \"aut\",
    \"last_name\": \"aut\",
    \"company\": \"aut\",
    \"industry\": \"aut\",
    \"website\": \"aut\",
    \"title\": \"aut\",
    \"phone\": \"aut\",
    \"address\": \"aut\",
    \"city\": \"aut\",
    \"state\": \"aut\",
    \"country\": \"aut\",
    \"custom_merge_fields[]\": [
        \"aut\"
    ]
}"
</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://app.wavo.co/api/v1/contacts"
);

const headers = {
    "Authorization": "Bearer {token}",
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "campaign_id": "fkjtf4390kfgu8903nsk",
    "email": "<EMAIL>",
    "first_name": "aut",
    "last_name": "aut",
    "company": "aut",
    "industry": "aut",
    "website": "aut",
    "title": "aut",
    "phone": "aut",
    "address": "aut",
    "city": "aut",
    "state": "aut",
    "country": "aut",
    "custom_merge_fields[]": [
        "aut"
    ]
};

fetch(url, {
    method: "POST",
    headers,
    body: JSON.stringify(body),
}).then(response =&gt; response.json());</code></pre></div>


<div class="php-example">
    <pre><code class="language-php">$client = new \GuzzleHttp\Client();
$response = $client-&gt;post(
    'https://app.wavo.co/api/v1/contacts',
    [
        'headers' =&gt; [
            'Authorization' =&gt; 'Bearer {token}',
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
        'json' =&gt; [
            'campaign_id' =&gt; 'fkjtf4390kfgu8903nsk',
            'email' =&gt; '<EMAIL>',
            'first_name' =&gt; 'aut',
            'last_name' =&gt; 'aut',
            'company' =&gt; 'aut',
            'industry' =&gt; 'aut',
            'website' =&gt; 'aut',
            'title' =&gt; 'aut',
            'phone' =&gt; 'aut',
            'address' =&gt; 'aut',
            'city' =&gt; 'aut',
            'state' =&gt; 'aut',
            'country' =&gt; 'aut',
            'custom_merge_fields[]' =&gt; [
                'aut',
            ],
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre></div>


<div class="python-example">
    <pre><code class="language-python">import requests
import json

url = 'https://app.wavo.co/api/v1/contacts'
payload = {
    "campaign_id": "fkjtf4390kfgu8903nsk",
    "email": "<EMAIL>",
    "first_name": "aut",
    "last_name": "aut",
    "company": "aut",
    "industry": "aut",
    "website": "aut",
    "title": "aut",
    "phone": "aut",
    "address": "aut",
    "city": "aut",
    "state": "aut",
    "country": "aut",
    "custom_merge_fields[]": [
        "aut"
    ]
}
headers = {
  'Authorization': 'Bearer {token}',
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}

response = requests.request('POST', url, headers=headers, json=payload)
response.json()</code></pre></div>

</span>

<span id="example-responses-POSTapi-v1-contacts">
            <blockquote>
            <p>Example response (201):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;data&quot;: [
        {
            &quot;id&quot;: &quot;we52o4rv130mz1qxyzk8pmljd&quot;,
            &quot;email&quot;: &quot;<EMAIL>&quot;,
            &quot;first_name&quot;: &quot;Aditya&quot;,
            &quot;last_name&quot;: &quot;Kohler&quot;,
            &quot;company&quot;: &quot;Gerlach, Ziemann and Reilly&quot;,
            &quot;industry&quot;: &quot;Nonprofit Organization Management&quot;,
            &quot;website&quot;: &quot;kunde.com&quot;,
            &quot;title&quot;: &quot;Ms.&quot;,
            &quot;phone&quot;: &quot;************&quot;,
            &quot;address&quot;: &quot;41164 Osvaldo Row&quot;,
            &quot;city&quot;: &quot;Baileyborough&quot;,
            &quot;state&quot;: &quot;New Hampshire&quot;,
            &quot;country&quot;: &quot;US&quot;,
            &quot;custom_merge_fields&quot;: {
                &quot;fav color&quot;: &quot;purple&quot;,
                &quot;sport&quot;: &quot;running&quot;,
                &quot;os&quot;: &quot;Windows NT 5.0&quot;
            },
            &quot;timezone&quot;: &quot;US/Mountain&quot;,
            &quot;status&quot;: &quot;OK&quot;,
            &quot;interest&quot;: null,
            &quot;emails_sent&quot;: 0,
            &quot;completed_steps&quot;: 0,
            &quot;is_missing_data&quot;: false,
            &quot;is_suppressed&quot;: false,
            &quot;created_at&quot;: 1579888600,
            &quot;campaign&quot;: {
                &quot;id&quot;: &quot;fkjtf4390kfgu8903nsk&quot;,
                &quot;name&quot;: &quot;Grady-Runolfsson 16&quot;,
                &quot;timezone&quot;: &quot;US/Mountain&quot;,
                &quot;status&quot;: &quot;DRAFT&quot;,
                &quot;created_at&quot;: 1579888600,
                &quot;client&quot;: {
                    &quot;id&quot;: &quot;a83445df46as5432mga&quot;,
                    &quot;name&quot;: &quot;Grady-Runolfsson&quot;,
                    &quot;created_at&quot;: 1579888600
                }
            }
        }
    ]
}</code>
 </pre>
    </span>
<span id="execution-results-POSTapi-v1-contacts" hidden>
    <blockquote>Received response<span
                id="execution-response-status-POSTapi-v1-contacts"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-POSTapi-v1-contacts" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-POSTapi-v1-contacts" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-POSTapi-v1-contacts"></code></pre>
</span>
<form id="form-POSTapi-v1-contacts" data-method="POST"
      data-path="api/v1/contacts"
      data-authed="1"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('POSTapi-v1-contacts', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
            </h3>
            <p>
            <small class="badge badge-black">POST</small>
            <b><code>api/v1/contacts</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Authorization</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Authorization" class="auth-value"               data-endpoint="POSTapi-v1-contacts"
               value="Bearer {token}"
               data-component="header">
    <br>
<p>Example: <code>Bearer {token}</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Content-Type"                data-endpoint="POSTapi-v1-contacts"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Accept"                data-endpoint="POSTapi-v1-contacts"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <h4 class="fancy-heading-panel"><b>Body Parameters</b></h4>
        <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>campaign_id</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="campaign_id"                data-endpoint="POSTapi-v1-contacts"
               value="fkjtf4390kfgu8903nsk"
               data-component="body">
    <br>
<p>The id of the campaign. Example: <code>fkjtf4390kfgu8903nsk</code></p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>email</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="email"                data-endpoint="POSTapi-v1-contacts"
               value="<EMAIL>"
               data-component="body">
    <br>
<p>The email address of the contact. Example: <code><EMAIL></code></p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>custom_merge_fields</code></b>&nbsp;&nbsp;
<small>object</small>&nbsp;
<i>optional</i> &nbsp;
                <input type="text" style="display: none"
               name="custom_merge_fields"                data-endpoint="POSTapi-v1-contacts"
               value=""
               data-component="body">
    <br>

        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>first_name</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
<i>optional</i> &nbsp;
                <input type="text" style="display: none"
               name="first_name"                data-endpoint="POSTapi-v1-contacts"
               value="aut"
               data-component="body">
    <br>
<p>The first name of the contact. Example: <code>aut</code></p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>last_name</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
<i>optional</i> &nbsp;
                <input type="text" style="display: none"
               name="last_name"                data-endpoint="POSTapi-v1-contacts"
               value="aut"
               data-component="body">
    <br>
<p>The last name of contact. Example: <code>aut</code></p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>company</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
<i>optional</i> &nbsp;
                <input type="text" style="display: none"
               name="company"                data-endpoint="POSTapi-v1-contacts"
               value="aut"
               data-component="body">
    <br>
<p>The company of the contact. Example: <code>aut</code></p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>industry</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
<i>optional</i> &nbsp;
                <input type="text" style="display: none"
               name="industry"                data-endpoint="POSTapi-v1-contacts"
               value="aut"
               data-component="body">
    <br>
<p>The industry of the contact. Example: <code>aut</code></p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>website</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
<i>optional</i> &nbsp;
                <input type="text" style="display: none"
               name="website"                data-endpoint="POSTapi-v1-contacts"
               value="aut"
               data-component="body">
    <br>
<p>The website of the contact. Example: <code>aut</code></p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>title</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
<i>optional</i> &nbsp;
                <input type="text" style="display: none"
               name="title"                data-endpoint="POSTapi-v1-contacts"
               value="aut"
               data-component="body">
    <br>
<p>The job title of the contact. Example: <code>aut</code></p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>phone</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
<i>optional</i> &nbsp;
                <input type="text" style="display: none"
               name="phone"                data-endpoint="POSTapi-v1-contacts"
               value="aut"
               data-component="body">
    <br>
<p>The phone of the contact. Example: <code>aut</code></p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>address</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
<i>optional</i> &nbsp;
                <input type="text" style="display: none"
               name="address"                data-endpoint="POSTapi-v1-contacts"
               value="aut"
               data-component="body">
    <br>
<p>The address of the contact. Example: <code>aut</code></p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>city</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
<i>optional</i> &nbsp;
                <input type="text" style="display: none"
               name="city"                data-endpoint="POSTapi-v1-contacts"
               value="aut"
               data-component="body">
    <br>
<p>The city of the contact. Example: <code>aut</code></p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>state</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
<i>optional</i> &nbsp;
                <input type="text" style="display: none"
               name="state"                data-endpoint="POSTapi-v1-contacts"
               value="aut"
               data-component="body">
    <br>
<p>The state of the contact. Example: <code>aut</code></p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>country</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
<i>optional</i> &nbsp;
                <input type="text" style="display: none"
               name="country"                data-endpoint="POSTapi-v1-contacts"
               value="aut"
               data-component="body">
    <br>
<p>The country of the contact. Example: <code>aut</code></p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>custom_merge_fields[]</code></b>&nbsp;&nbsp;
<small>string[]</small>&nbsp;
<i>optional</i> &nbsp;
                <input type="text" style="display: none"
               name="custom_merge_fields.0[0]"                data-endpoint="POSTapi-v1-contacts"
               data-component="body">
        <input type="text" style="display: none"
               name="custom_merge_fields.0[1]"                data-endpoint="POSTapi-v1-contacts"
               data-component="body">
    <br>
<p>An array of merge data to be used in email messages.</p>
        </div>
        </form>

                    <h2 id="contacts-GETapi-v1-contacts--contact_id-">Display a contact</h2>

<p>
<small class="badge badge-darkred">requires authentication</small>
</p>

<p>Find a contact by id and display its details.</p>

<span id="example-requests-GETapi-v1-contacts--contact_id-">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "https://app.wavo.co/api/v1/contacts/we52o4rv130mz1qxyzk8pmljd" \
    --header "Authorization: Bearer {token}" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://app.wavo.co/api/v1/contacts/we52o4rv130mz1qxyzk8pmljd"
);

const headers = {
    "Authorization": "Bearer {token}",
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>


<div class="php-example">
    <pre><code class="language-php">$client = new \GuzzleHttp\Client();
$response = $client-&gt;get(
    'https://app.wavo.co/api/v1/contacts/we52o4rv130mz1qxyzk8pmljd',
    [
        'headers' =&gt; [
            'Authorization' =&gt; 'Bearer {token}',
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre></div>


<div class="python-example">
    <pre><code class="language-python">import requests
import json

url = 'https://app.wavo.co/api/v1/contacts/we52o4rv130mz1qxyzk8pmljd'
headers = {
  'Authorization': 'Bearer {token}',
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}

response = requests.request('GET', url, headers=headers)
response.json()</code></pre></div>

</span>

<span id="example-responses-GETapi-v1-contacts--contact_id-">
            <blockquote>
            <p>Example response (200):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;data&quot;: {
        &quot;id&quot;: &quot;we52o4rv130mz1qxyzk8pmljd&quot;,
        &quot;email&quot;: &quot;<EMAIL>&quot;,
        &quot;first_name&quot;: &quot;Aditya&quot;,
        &quot;last_name&quot;: &quot;Kohler&quot;,
        &quot;company&quot;: &quot;Gerlach, Ziemann and Reilly&quot;,
        &quot;industry&quot;: &quot;Nonprofit Organization Management&quot;,
        &quot;website&quot;: &quot;kunde.com&quot;,
        &quot;title&quot;: &quot;Ms.&quot;,
        &quot;phone&quot;: &quot;************&quot;,
        &quot;address&quot;: &quot;41164 Osvaldo Row&quot;,
        &quot;city&quot;: &quot;Baileyborough&quot;,
        &quot;state&quot;: &quot;New Hampshire&quot;,
        &quot;country&quot;: &quot;US&quot;,
        &quot;custom_merge_fields&quot;: {
            &quot;fav color&quot;: &quot;purple&quot;,
            &quot;sport&quot;: &quot;running&quot;,
            &quot;os&quot;: &quot;Windows NT 5.0&quot;
        },
        &quot;timezone&quot;: &quot;US/Mountain&quot;,
        &quot;status&quot;: &quot;OK&quot;,
        &quot;interest&quot;: null,
        &quot;emails_sent&quot;: 0,
        &quot;completed_steps&quot;: 0,
        &quot;is_missing_data&quot;: false,
        &quot;is_suppressed&quot;: false,
        &quot;created_at&quot;: 1579888600,
        &quot;campaign&quot;: {
            &quot;id&quot;: &quot;fkjtf4390kfgu8903nsk&quot;,
            &quot;name&quot;: &quot;Grady-Runolfsson 16&quot;,
            &quot;timezone&quot;: &quot;US/Mountain&quot;,
            &quot;status&quot;: &quot;DRAFT&quot;,
            &quot;created_at&quot;: 1579888600,
            &quot;client&quot;: {
                &quot;id&quot;: &quot;a83445df46as5432mga&quot;,
                &quot;name&quot;: &quot;Grady-Runolfsson&quot;,
                &quot;created_at&quot;: 1579888600
            }
        }
    }
}</code>
 </pre>
    </span>
<span id="execution-results-GETapi-v1-contacts--contact_id-" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-v1-contacts--contact_id-"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-v1-contacts--contact_id-" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-v1-contacts--contact_id-" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-v1-contacts--contact_id-"></code></pre>
</span>
<form id="form-GETapi-v1-contacts--contact_id-" data-method="GET"
      data-path="api/v1/contacts/{contact_id}"
      data-authed="1"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-v1-contacts--contact_id-', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/v1/contacts/{contact_id}</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Authorization</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Authorization" class="auth-value"               data-endpoint="GETapi-v1-contacts--contact_id-"
               value="Bearer {token}"
               data-component="header">
    <br>
<p>Example: <code>Bearer {token}</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Content-Type"                data-endpoint="GETapi-v1-contacts--contact_id-"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Accept"                data-endpoint="GETapi-v1-contacts--contact_id-"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        <h4 class="fancy-heading-panel"><b>URL Parameters</b></h4>
                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>contact_id</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="contact_id"                data-endpoint="GETapi-v1-contacts--contact_id-"
               value="we52o4rv130mz1qxyzk8pmljd"
               data-component="url">
    <br>
<p>The id of the contact. Example: <code>we52o4rv130mz1qxyzk8pmljd</code></p>
            </div>
                    </form>

                    <h2 id="contacts-PUTapi-v1-contact-interest">Update contact interest</h2>

<p>
<small class="badge badge-darkred">requires authentication</small>
</p>

<p>Set a contact's interest to a value of POSITIVE, NEUTRAL, or NEGATIVE.</p>

<span id="example-requests-PUTapi-v1-contact-interest">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request PUT \
    "https://app.wavo.co/api/v1/contact-interest" \
    --header "Authorization: Bearer {token}" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json" \
    --data "{
    \"contact_id\": \"fkjtf4390kfgu8903nsk\",
    \"interest\": \"POSITIVE\"
}"
</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://app.wavo.co/api/v1/contact-interest"
);

const headers = {
    "Authorization": "Bearer {token}",
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "contact_id": "fkjtf4390kfgu8903nsk",
    "interest": "POSITIVE"
};

fetch(url, {
    method: "PUT",
    headers,
    body: JSON.stringify(body),
}).then(response =&gt; response.json());</code></pre></div>


<div class="php-example">
    <pre><code class="language-php">$client = new \GuzzleHttp\Client();
$response = $client-&gt;put(
    'https://app.wavo.co/api/v1/contact-interest',
    [
        'headers' =&gt; [
            'Authorization' =&gt; 'Bearer {token}',
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
        'json' =&gt; [
            'contact_id' =&gt; 'fkjtf4390kfgu8903nsk',
            'interest' =&gt; 'POSITIVE',
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre></div>


<div class="python-example">
    <pre><code class="language-python">import requests
import json

url = 'https://app.wavo.co/api/v1/contact-interest'
payload = {
    "contact_id": "fkjtf4390kfgu8903nsk",
    "interest": "POSITIVE"
}
headers = {
  'Authorization': 'Bearer {token}',
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}

response = requests.request('PUT', url, headers=headers, json=payload)
response.json()</code></pre></div>

</span>

<span id="example-responses-PUTapi-v1-contact-interest">
            <blockquote>
            <p>Example response (200):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;message&quot;: &quot;Interest updated.&quot;
}</code>
 </pre>
    </span>
<span id="execution-results-PUTapi-v1-contact-interest" hidden>
    <blockquote>Received response<span
                id="execution-response-status-PUTapi-v1-contact-interest"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-PUTapi-v1-contact-interest" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-PUTapi-v1-contact-interest" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-PUTapi-v1-contact-interest"></code></pre>
</span>
<form id="form-PUTapi-v1-contact-interest" data-method="PUT"
      data-path="api/v1/contact-interest"
      data-authed="1"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('PUTapi-v1-contact-interest', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
            </h3>
            <p>
            <small class="badge badge-darkblue">PUT</small>
            <b><code>api/v1/contact-interest</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Authorization</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Authorization" class="auth-value"               data-endpoint="PUTapi-v1-contact-interest"
               value="Bearer {token}"
               data-component="header">
    <br>
<p>Example: <code>Bearer {token}</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Content-Type"                data-endpoint="PUTapi-v1-contact-interest"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Accept"                data-endpoint="PUTapi-v1-contact-interest"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <h4 class="fancy-heading-panel"><b>Body Parameters</b></h4>
        <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>contact_id</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="contact_id"                data-endpoint="PUTapi-v1-contact-interest"
               value="fkjtf4390kfgu8903nsk"
               data-component="body">
    <br>
<p>The contact ID. Example: <code>fkjtf4390kfgu8903nsk</code></p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>interest</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="interest"                data-endpoint="PUTapi-v1-contact-interest"
               value="POSITIVE"
               data-component="body">
    <br>
<p>The interest to set. Example: <code>POSITIVE</code></p>
        </div>
        </form>

                    <h2 id="contacts-DELETEapi-v1-contact-interest">Remove contact interest</h2>

<p>
<small class="badge badge-darkred">requires authentication</small>
</p>

<p>Set a contact's interest to UNMARKED.</p>

<span id="example-requests-DELETEapi-v1-contact-interest">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request DELETE \
    "https://app.wavo.co/api/v1/contact-interest" \
    --header "Authorization: Bearer {token}" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json" \
    --data "{
    \"contact_id\": \"fkjtf4390kfgu8903nsk\"
}"
</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://app.wavo.co/api/v1/contact-interest"
);

const headers = {
    "Authorization": "Bearer {token}",
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "contact_id": "fkjtf4390kfgu8903nsk"
};

fetch(url, {
    method: "DELETE",
    headers,
    body: JSON.stringify(body),
}).then(response =&gt; response.json());</code></pre></div>


<div class="php-example">
    <pre><code class="language-php">$client = new \GuzzleHttp\Client();
$response = $client-&gt;delete(
    'https://app.wavo.co/api/v1/contact-interest',
    [
        'headers' =&gt; [
            'Authorization' =&gt; 'Bearer {token}',
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
        'json' =&gt; [
            'contact_id' =&gt; 'fkjtf4390kfgu8903nsk',
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre></div>


<div class="python-example">
    <pre><code class="language-python">import requests
import json

url = 'https://app.wavo.co/api/v1/contact-interest'
payload = {
    "contact_id": "fkjtf4390kfgu8903nsk"
}
headers = {
  'Authorization': 'Bearer {token}',
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}

response = requests.request('DELETE', url, headers=headers, json=payload)
response.json()</code></pre></div>

</span>

<span id="example-responses-DELETEapi-v1-contact-interest">
            <blockquote>
            <p>Example response (200):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;message&quot;: &quot;Interest UNMARKED.&quot;
}</code>
 </pre>
    </span>
<span id="execution-results-DELETEapi-v1-contact-interest" hidden>
    <blockquote>Received response<span
                id="execution-response-status-DELETEapi-v1-contact-interest"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-DELETEapi-v1-contact-interest" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-DELETEapi-v1-contact-interest" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-DELETEapi-v1-contact-interest"></code></pre>
</span>
<form id="form-DELETEapi-v1-contact-interest" data-method="DELETE"
      data-path="api/v1/contact-interest"
      data-authed="1"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('DELETEapi-v1-contact-interest', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
            </h3>
            <p>
            <small class="badge badge-red">DELETE</small>
            <b><code>api/v1/contact-interest</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Authorization</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Authorization" class="auth-value"               data-endpoint="DELETEapi-v1-contact-interest"
               value="Bearer {token}"
               data-component="header">
    <br>
<p>Example: <code>Bearer {token}</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Content-Type"                data-endpoint="DELETEapi-v1-contact-interest"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Accept"                data-endpoint="DELETEapi-v1-contact-interest"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <h4 class="fancy-heading-panel"><b>Body Parameters</b></h4>
        <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>contact_id</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="contact_id"                data-endpoint="DELETEapi-v1-contact-interest"
               value="fkjtf4390kfgu8903nsk"
               data-component="body">
    <br>
<p>The contact ID. Example: <code>fkjtf4390kfgu8903nsk</code></p>
        </div>
        </form>

                    <h2 id="contacts-GETapi-v1-campaigns--campaign_id--contacts">Display a listing of contacts</h2>

<p>
<small class="badge badge-darkred">requires authentication</small>
</p>

<p>Return all contacts of a campaign.</p>

<span id="example-requests-GETapi-v1-campaigns--campaign_id--contacts">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "https://app.wavo.co/api/v1/campaigns/fkjtf4390kfgu8903nsk/contacts?status=replied&amp;is_missing_data=0&amp;is_suppressed=1&amp;interest=positive&amp;emails_sent=3&amp;limit=2&amp;offset=0" \
    --header "Authorization: Bearer {token}" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://app.wavo.co/api/v1/campaigns/fkjtf4390kfgu8903nsk/contacts"
);

const params = {
    "status": "replied",
    "is_missing_data": "0",
    "is_suppressed": "1",
    "interest": "positive",
    "emails_sent": "3",
    "limit": "2",
    "offset": "0",
};
Object.keys(params)
    .forEach(key =&gt; url.searchParams.append(key, params[key]));

const headers = {
    "Authorization": "Bearer {token}",
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>


<div class="php-example">
    <pre><code class="language-php">$client = new \GuzzleHttp\Client();
$response = $client-&gt;get(
    'https://app.wavo.co/api/v1/campaigns/fkjtf4390kfgu8903nsk/contacts',
    [
        'headers' =&gt; [
            'Authorization' =&gt; 'Bearer {token}',
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
        'query' =&gt; [
            'status' =&gt; 'replied',
            'is_missing_data' =&gt; '0',
            'is_suppressed' =&gt; '1',
            'interest' =&gt; 'positive',
            'emails_sent' =&gt; '3',
            'limit' =&gt; '2',
            'offset' =&gt; '0',
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre></div>


<div class="python-example">
    <pre><code class="language-python">import requests
import json

url = 'https://app.wavo.co/api/v1/campaigns/fkjtf4390kfgu8903nsk/contacts'
params = {
  'status': 'replied',
  'is_missing_data': '0',
  'is_suppressed': '1',
  'interest': 'positive',
  'emails_sent': '3',
  'limit': '2',
  'offset': '0',
}
headers = {
  'Authorization': 'Bearer {token}',
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}

response = requests.request('GET', url, headers=headers, params=params)
response.json()</code></pre></div>

</span>

<span id="example-responses-GETapi-v1-campaigns--campaign_id--contacts">
            <blockquote>
            <p>Example response (200):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;data&quot;: [
        {
            &quot;id&quot;: &quot;j5ropgvndm6l1v0z7lwe9481y&quot;,
            &quot;email&quot;: &quot;<EMAIL>&quot;,
            &quot;first_name&quot;: &quot;Vincent&quot;,
            &quot;last_name&quot;: &quot;Kemmer&quot;,
            &quot;company&quot;: &quot;Hartmann, Lockman and Thompson&quot;,
            &quot;industry&quot;: &quot;Luxury Goods &amp; Jewelry&quot;,
            &quot;website&quot;: &quot;mosciski.com&quot;,
            &quot;title&quot;: &quot;Mr.&quot;,
            &quot;phone&quot;: &quot;**************&quot;,
            &quot;address&quot;: &quot;6166 Jakubowski Cliff Suite 605&quot;,
            &quot;city&quot;: &quot;Port Reidville&quot;,
            &quot;state&quot;: &quot;North Carolina&quot;,
            &quot;country&quot;: &quot;US&quot;,
            &quot;custom_merge_fields&quot;: {
                &quot;fav color&quot;: &quot;teal&quot;,
                &quot;sport&quot;: &quot;tennis&quot;,
                &quot;os&quot;: &quot;Windows NT 6.2&quot;
            },
            &quot;timezone&quot;: &quot;US/Mountain&quot;,
            &quot;status&quot;: &quot;OK&quot;,
            &quot;interest&quot;: null,
            &quot;emails_sent&quot;: 1,
            &quot;completed_steps&quot;: 1,
            &quot;is_missing_data&quot;: 1,
            &quot;is_suppressed&quot;: 0,
            &quot;created_at&quot;: 1579888424
        },
        {
            &quot;id&quot;: &quot;ny4j2vd8k56248q1wzgeo9r3p&quot;,
            &quot;email&quot;: &quot;<EMAIL>&quot;,
            &quot;first_name&quot;: &quot;Jacquelyn&quot;,
            &quot;last_name&quot;: &quot;Okuneva&quot;,
            &quot;company&quot;: &quot;Armstrong, Ferry and Nolan&quot;,
            &quot;industry&quot;: &quot;Design&quot;,
            &quot;website&quot;: &quot;sanford.com&quot;,
            &quot;title&quot;: &quot;Prof.&quot;,
            &quot;phone&quot;: &quot;(************* x05539&quot;,
            &quot;address&quot;: &quot;1971 Colten Ways&quot;,
            &quot;city&quot;: &quot;Batzland&quot;,
            &quot;state&quot;: &quot;Kansas&quot;,
            &quot;country&quot;: &quot;US&quot;,
            &quot;custom_merge_fields&quot;: {
                &quot;fav color&quot;: &quot;silver&quot;,
                &quot;sport&quot;: &quot;tennis&quot;,
                &quot;os&quot;: &quot;Windows 95&quot;
            },
            &quot;timezone&quot;: &quot;US/Mountain&quot;,
            &quot;status&quot;: &quot;REPLIED&quot;,
            &quot;interest&quot;: &quot;POSITIVE&quot;,
            &quot;emails_sent&quot;: 2,
            &quot;completed_steps&quot;: 2,
            &quot;is_missing_data&quot;: 1,
            &quot;is_suppressed&quot;: 0,
            &quot;created_at&quot;: 1579888424
        }
    ]
}</code>
 </pre>
    </span>
<span id="execution-results-GETapi-v1-campaigns--campaign_id--contacts" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-v1-campaigns--campaign_id--contacts"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-v1-campaigns--campaign_id--contacts" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-v1-campaigns--campaign_id--contacts" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-v1-campaigns--campaign_id--contacts"></code></pre>
</span>
<form id="form-GETapi-v1-campaigns--campaign_id--contacts" data-method="GET"
      data-path="api/v1/campaigns/{campaign_id}/contacts"
      data-authed="1"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-v1-campaigns--campaign_id--contacts', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/v1/campaigns/{campaign_id}/contacts</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Authorization</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Authorization" class="auth-value"               data-endpoint="GETapi-v1-campaigns--campaign_id--contacts"
               value="Bearer {token}"
               data-component="header">
    <br>
<p>Example: <code>Bearer {token}</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Content-Type"                data-endpoint="GETapi-v1-campaigns--campaign_id--contacts"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Accept"                data-endpoint="GETapi-v1-campaigns--campaign_id--contacts"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        <h4 class="fancy-heading-panel"><b>URL Parameters</b></h4>
                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>campaign_id</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="campaign_id"                data-endpoint="GETapi-v1-campaigns--campaign_id--contacts"
               value="fkjtf4390kfgu8903nsk"
               data-component="url">
    <br>
<p>The id of a campaign. Example: <code>fkjtf4390kfgu8903nsk</code></p>
            </div>
                        <h4 class="fancy-heading-panel"><b>Query Parameters</b></h4>
                                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>status</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
<i>optional</i> &nbsp;
                <input type="text" style="display: none"
               name="status"                data-endpoint="GETapi-v1-campaigns--campaign_id--contacts"
               value="replied"
               data-component="query">
    <br>
<p>Get contacts of a specific status. Can be one of: ok, replied, unsubscribed, bounced, autoreplied, stopped. Example: <code>replied</code></p>
            </div>
                                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>is_missing_data</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
<i>optional</i> &nbsp;
                <input type="text" style="display: none"
               name="is_missing_data"                data-endpoint="GETapi-v1-campaigns--campaign_id--contacts"
               value="0"
               data-component="query">
    <br>
<p>Get contacts with missing data. Example: <code>0</code></p>
            </div>
                                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>is_suppressed</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
<i>optional</i> &nbsp;
                <input type="text" style="display: none"
               name="is_suppressed"                data-endpoint="GETapi-v1-campaigns--campaign_id--contacts"
               value="1"
               data-component="query">
    <br>
<p>Get contacts that are suppressed. Example: <code>1</code></p>
            </div>
                                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>interest</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
<i>optional</i> &nbsp;
                <input type="text" style="display: none"
               name="interest"                data-endpoint="GETapi-v1-campaigns--campaign_id--contacts"
               value="positive"
               data-component="query">
    <br>
<p>Get contacts that have a specific interest level. Can be one of: unmarked, positive, neutral, negative. Example: <code>positive</code></p>
            </div>
                                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>emails_sent</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
<i>optional</i> &nbsp;
                <input type="number" style="display: none"
               name="emails_sent"                data-endpoint="GETapi-v1-campaigns--campaign_id--contacts"
               value="3"
               data-component="query">
    <br>
<p>Get contacts that have been contacted a specific number of times. Example: <code>3</code></p>
            </div>
                                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>limit</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
<i>optional</i> &nbsp;
                <input type="number" style="display: none"
               name="limit"                data-endpoint="GETapi-v1-campaigns--campaign_id--contacts"
               value="2"
               data-component="query">
    <br>
<p>The number of objects to return. Defaults to 100. Maximum 500. Example: <code>2</code></p>
            </div>
                                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>offset</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
<i>optional</i> &nbsp;
                <input type="text" style="display: none"
               name="offset"                data-endpoint="GETapi-v1-campaigns--campaign_id--contacts"
               value="0"
               data-component="query">
    <br>
<p>The zero-based offset for the default object sorting. Example: <code>0</code></p>
            </div>
                </form>

                    <h2 id="contacts-GETapi-v1-contacts">Search for contacts</h2>

<p>
<small class="badge badge-darkred">requires authentication</small>
</p>

<p>Find contacts of a specific email address.</p>

<span id="example-requests-GETapi-v1-contacts">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "https://app.wavo.co/api/v1/contacts?email=aditya.kohler%40example.org&amp;client_id=a83445df46as5432mga&amp;campaign_id=fkjtf4390kfgu8903nsk" \
    --header "Authorization: Bearer {token}" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json" \
    --data "{
    \"email\": \"<EMAIL>\"
}"
</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://app.wavo.co/api/v1/contacts"
);

const params = {
    "email": "<EMAIL>",
    "client_id": "a83445df46as5432mga",
    "campaign_id": "fkjtf4390kfgu8903nsk",
};
Object.keys(params)
    .forEach(key =&gt; url.searchParams.append(key, params[key]));

const headers = {
    "Authorization": "Bearer {token}",
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "email": "<EMAIL>"
};

fetch(url, {
    method: "GET",
    headers,
    body: JSON.stringify(body),
}).then(response =&gt; response.json());</code></pre></div>


<div class="php-example">
    <pre><code class="language-php">$client = new \GuzzleHttp\Client();
$response = $client-&gt;get(
    'https://app.wavo.co/api/v1/contacts',
    [
        'headers' =&gt; [
            'Authorization' =&gt; 'Bearer {token}',
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
        'query' =&gt; [
            'email' =&gt; '<EMAIL>',
            'client_id' =&gt; 'a83445df46as5432mga',
            'campaign_id' =&gt; 'fkjtf4390kfgu8903nsk',
        ],
        'json' =&gt; [
            'email' =&gt; '<EMAIL>',
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre></div>


<div class="python-example">
    <pre><code class="language-python">import requests
import json

url = 'https://app.wavo.co/api/v1/contacts'
payload = {
    "email": "<EMAIL>"
}
params = {
  'email': '<EMAIL>',
  'client_id': 'a83445df46as5432mga',
  'campaign_id': 'fkjtf4390kfgu8903nsk',
}
headers = {
  'Authorization': 'Bearer {token}',
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}

response = requests.request('GET', url, headers=headers, json=payload, params=params)
response.json()</code></pre></div>

</span>

<span id="example-responses-GETapi-v1-contacts">
            <blockquote>
            <p>Example response (200):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;data&quot;: [
        {
            &quot;id&quot;: &quot;we52o4rv130mz1qxyzk8pmljd&quot;,
            &quot;email&quot;: &quot;<EMAIL>&quot;,
            &quot;first_name&quot;: &quot;Aditya&quot;,
            &quot;last_name&quot;: &quot;Kohler&quot;,
            &quot;company&quot;: &quot;Gerlach, Ziemann and Reilly&quot;,
            &quot;industry&quot;: &quot;Nonprofit Organization Management&quot;,
            &quot;website&quot;: &quot;kunde.com&quot;,
            &quot;title&quot;: &quot;Ms.&quot;,
            &quot;phone&quot;: &quot;************&quot;,
            &quot;address&quot;: &quot;41164 Osvaldo Row&quot;,
            &quot;city&quot;: &quot;Baileyborough&quot;,
            &quot;state&quot;: &quot;New Hampshire&quot;,
            &quot;country&quot;: &quot;US&quot;,
            &quot;custom_merge_fields&quot;: {
                &quot;fav color&quot;: &quot;purple&quot;,
                &quot;sport&quot;: &quot;running&quot;,
                &quot;os&quot;: &quot;Windows NT 5.0&quot;
            },
            &quot;timezone&quot;: &quot;US/Mountain&quot;,
            &quot;status&quot;: &quot;OK&quot;,
            &quot;interest&quot;: null,
            &quot;emails_sent&quot;: 0,
            &quot;completed_steps&quot;: 0,
            &quot;is_missing_data&quot;: 0,
            &quot;is_suppressed&quot;: 0,
            &quot;created_at&quot;: 1579888600,
            &quot;campaign&quot;: {
                &quot;id&quot;: &quot;fkjtf4390kfgu8903nsk&quot;,
                &quot;name&quot;: &quot;Grady-Runolfsson 16&quot;,
                &quot;timezone&quot;: &quot;US/Mountain&quot;,
                &quot;status&quot;: &quot;DRAFT&quot;,
                &quot;created_at&quot;: 1579888600,
                &quot;client&quot;: {
                    &quot;id&quot;: &quot;a83445df46as5432mga&quot;,
                    &quot;name&quot;: &quot;Grady-Runolfsson&quot;,
                    &quot;created_at&quot;: 1579888600
                }
            }
        }
    ]
}</code>
 </pre>
    </span>
<span id="execution-results-GETapi-v1-contacts" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-v1-contacts"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-v1-contacts" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-v1-contacts" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-v1-contacts"></code></pre>
</span>
<form id="form-GETapi-v1-contacts" data-method="GET"
      data-path="api/v1/contacts"
      data-authed="1"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-v1-contacts', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/v1/contacts</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Authorization</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Authorization" class="auth-value"               data-endpoint="GETapi-v1-contacts"
               value="Bearer {token}"
               data-component="header">
    <br>
<p>Example: <code>Bearer {token}</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Content-Type"                data-endpoint="GETapi-v1-contacts"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Accept"                data-endpoint="GETapi-v1-contacts"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                            <h4 class="fancy-heading-panel"><b>Query Parameters</b></h4>
                                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>email</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="email"                data-endpoint="GETapi-v1-contacts"
               value="<EMAIL>"
               data-component="query">
    <br>
<p>The email address we are searching for. Example: <code><EMAIL></code></p>
            </div>
                                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>client_id</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
<i>optional</i> &nbsp;
                <input type="text" style="display: none"
               name="client_id"                data-endpoint="GETapi-v1-contacts"
               value="a83445df46as5432mga"
               data-component="query">
    <br>
<p>The id of a client. Example: <code>a83445df46as5432mga</code></p>
            </div>
                                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>campaign_id</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
<i>optional</i> &nbsp;
                <input type="text" style="display: none"
               name="campaign_id"                data-endpoint="GETapi-v1-contacts"
               value="fkjtf4390kfgu8903nsk"
               data-component="query">
    <br>
<p>The id of a campaign. Example: <code>fkjtf4390kfgu8903nsk</code></p>
            </div>
                        <h4 class="fancy-heading-panel"><b>Body Parameters</b></h4>
        <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>email</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="email"                data-endpoint="GETapi-v1-contacts"
               value="<EMAIL>"
               data-component="body">
    <br>
<p>Must be a valid email address. Must not be greater than 255 characters. Example: <code><EMAIL></code></p>
        </div>
        </form>

                <h1 id="email-messages">Email Messages</h1>

    <p>APIs for managing email messages.</p>

                                <h2 id="email-messages-GETapi-v1-campaigns--campaign_id--replies">Display a listing of a campaign&#039;s replies.</h2>

<p>
<small class="badge badge-darkred">requires authentication</small>
</p>

<p>Return all email message replies of a campaign.</p>

<span id="example-requests-GETapi-v1-campaigns--campaign_id--replies">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "https://app.wavo.co/api/v1/campaigns/fkjtf4390kfgu8903nsk/replies?limit=2&amp;offset=0" \
    --header "Authorization: Bearer {token}" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://app.wavo.co/api/v1/campaigns/fkjtf4390kfgu8903nsk/replies"
);

const params = {
    "limit": "2",
    "offset": "0",
};
Object.keys(params)
    .forEach(key =&gt; url.searchParams.append(key, params[key]));

const headers = {
    "Authorization": "Bearer {token}",
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>


<div class="php-example">
    <pre><code class="language-php">$client = new \GuzzleHttp\Client();
$response = $client-&gt;get(
    'https://app.wavo.co/api/v1/campaigns/fkjtf4390kfgu8903nsk/replies',
    [
        'headers' =&gt; [
            'Authorization' =&gt; 'Bearer {token}',
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
        'query' =&gt; [
            'limit' =&gt; '2',
            'offset' =&gt; '0',
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre></div>


<div class="python-example">
    <pre><code class="language-python">import requests
import json

url = 'https://app.wavo.co/api/v1/campaigns/fkjtf4390kfgu8903nsk/replies'
params = {
  'limit': '2',
  'offset': '0',
}
headers = {
  'Authorization': 'Bearer {token}',
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}

response = requests.request('GET', url, headers=headers, params=params)
response.json()</code></pre></div>

</span>

<span id="example-responses-GETapi-v1-campaigns--campaign_id--replies">
            <blockquote>
            <p>Example response (200):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;data&quot;: [
        {
            &quot;id&quot;: &quot;2dpkq21yvjn4r530y56gzwlm3e08ox&quot;,
            &quot;thread_id&quot;: &quot;l12zpqxdn36mwrog08e5mwrkj&quot;,
            &quot;contact_id&quot;: &quot;83jm7zlgve0nlzq4d15orxywn&quot;,
            &quot;from_email&quot;: &quot;<EMAIL>&quot;,
            &quot;to_email&quot;: &quot;<EMAIL>&quot;,
            &quot;from_name&quot;: &quot;John Jackson&quot;,
            &quot;to_name&quot;: &quot;Corwin Velda&quot;,
            &quot;submitted_at&quot;: 1597066351,
            &quot;created_at&quot;: 1597066376,
            &quot;subject&quot;: &quot;Re: Test message subject&quot;,
            &quot;snippet&quot;: &quot;Short Message Body...&quot;,
            &quot;message_body&quot;: &quot;Message body text...&quot;,
            &quot;message_raw&quot;: &quot;Raw Message text with html tags...&quot;
        },
        {
            &quot;id&quot;: &quot;66lgem812pz0w7y6g9yoj3nr4kvqdx&quot;,
            &quot;thread_id&quot;: &quot;rdle34wnjy94qj6m178p0gz5v&quot;,
            &quot;contact_id&quot;: &quot;8j5orm17lnqgpkqvg9pk4z3ew&quot;,
            &quot;from_email&quot;: &quot;<EMAIL>&quot;,
            &quot;to_email&quot;: &quot;<EMAIL>&quot;,
            &quot;from_name&quot;: &quot;Ella Jones&quot;,
            &quot;to_name&quot;: &quot;Corwin Velda&quot;,
            &quot;submitted_at&quot;: 1597066351,
            &quot;created_at&quot;: 1597066376,
            &quot;subject&quot;: &quot;Re: Test message subject&quot;,
            &quot;snippet&quot;: &quot;Short Message Body...&quot;,
            &quot;message_body&quot;: &quot;Message body text...&quot;,
            &quot;message_raw&quot;: &quot;Raw Message text with html tags...&quot;
        }
    ]
}</code>
 </pre>
    </span>
<span id="execution-results-GETapi-v1-campaigns--campaign_id--replies" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-v1-campaigns--campaign_id--replies"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-v1-campaigns--campaign_id--replies" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-v1-campaigns--campaign_id--replies" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-v1-campaigns--campaign_id--replies"></code></pre>
</span>
<form id="form-GETapi-v1-campaigns--campaign_id--replies" data-method="GET"
      data-path="api/v1/campaigns/{campaign_id}/replies"
      data-authed="1"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-v1-campaigns--campaign_id--replies', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/v1/campaigns/{campaign_id}/replies</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Authorization</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Authorization" class="auth-value"               data-endpoint="GETapi-v1-campaigns--campaign_id--replies"
               value="Bearer {token}"
               data-component="header">
    <br>
<p>Example: <code>Bearer {token}</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Content-Type"                data-endpoint="GETapi-v1-campaigns--campaign_id--replies"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Accept"                data-endpoint="GETapi-v1-campaigns--campaign_id--replies"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        <h4 class="fancy-heading-panel"><b>URL Parameters</b></h4>
                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>campaign_id</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="campaign_id"                data-endpoint="GETapi-v1-campaigns--campaign_id--replies"
               value="fkjtf4390kfgu8903nsk"
               data-component="url">
    <br>
<p>The id of a campaign. Example: <code>fkjtf4390kfgu8903nsk</code></p>
            </div>
                        <h4 class="fancy-heading-panel"><b>Query Parameters</b></h4>
                                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>limit</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
<i>optional</i> &nbsp;
                <input type="number" style="display: none"
               name="limit"                data-endpoint="GETapi-v1-campaigns--campaign_id--replies"
               value="2"
               data-component="query">
    <br>
<p>The number of objects to return. Defaults to 10. Maximum 20. Example: <code>2</code></p>
            </div>
                                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>offset</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
<i>optional</i> &nbsp;
                <input type="text" style="display: none"
               name="offset"                data-endpoint="GETapi-v1-campaigns--campaign_id--replies"
               value="0"
               data-component="query">
    <br>
<p>The zero-based offset for the default object sorting. Example: <code>0</code></p>
            </div>
                </form>

                    <h2 id="email-messages-GETapi-v1-contacts--contact_id--replies">Display a listing of a contact&#039;s replies.</h2>

<p>
<small class="badge badge-darkred">requires authentication</small>
</p>

<p>Return all email message replies of a contact.</p>

<span id="example-requests-GETapi-v1-contacts--contact_id--replies">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "https://app.wavo.co/api/v1/contacts/83jm7zlgve0nlzq4d15orxywn/replies" \
    --header "Authorization: Bearer {token}" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://app.wavo.co/api/v1/contacts/83jm7zlgve0nlzq4d15orxywn/replies"
);

const headers = {
    "Authorization": "Bearer {token}",
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>


<div class="php-example">
    <pre><code class="language-php">$client = new \GuzzleHttp\Client();
$response = $client-&gt;get(
    'https://app.wavo.co/api/v1/contacts/83jm7zlgve0nlzq4d15orxywn/replies',
    [
        'headers' =&gt; [
            'Authorization' =&gt; 'Bearer {token}',
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre></div>


<div class="python-example">
    <pre><code class="language-python">import requests
import json

url = 'https://app.wavo.co/api/v1/contacts/83jm7zlgve0nlzq4d15orxywn/replies'
headers = {
  'Authorization': 'Bearer {token}',
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}

response = requests.request('GET', url, headers=headers)
response.json()</code></pre></div>

</span>

<span id="example-responses-GETapi-v1-contacts--contact_id--replies">
            <blockquote>
            <p>Example response (200):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;data&quot;: [
        {
            &quot;id&quot;: &quot;2dpkq21yvjn4r530y56gzwlm3e08ox&quot;,
            &quot;thread_id&quot;: &quot;l12zpqxdn36mwrog08e5mwrkj&quot;,
            &quot;contact_id&quot;: &quot;83jm7zlgve0nlzq4d15orxywn&quot;,
            &quot;from_email&quot;: &quot;<EMAIL>&quot;,
            &quot;to_email&quot;: &quot;<EMAIL>&quot;,
            &quot;from_name&quot;: &quot;John Jackson&quot;,
            &quot;to_name&quot;: &quot;Corwin Velda&quot;,
            &quot;submitted_at&quot;: 1597066351,
            &quot;created_at&quot;: 1597066376,
            &quot;subject&quot;: &quot;Re: Test message subject&quot;,
            &quot;snippet&quot;: &quot;Short Message Body...&quot;,
            &quot;message_body&quot;: &quot;Message body text...&quot;,
            &quot;message_raw&quot;: &quot;Raw Message text with html tags...&quot;
        },
        {
            &quot;id&quot;: &quot;rgr8yd4lkzvp05dw29nxe1w62m3qoj&quot;,
            &quot;thread_id&quot;: &quot;l12zpqxdn36mwrog08e5mwrkj&quot;,
            &quot;contact_id&quot;: &quot;83jm7zlgve0nlzq4d15orxywn&quot;,
            &quot;from_email&quot;: &quot;<EMAIL>&quot;,
            &quot;to_email&quot;: &quot;<EMAIL>&quot;,
            &quot;from_name&quot;: &quot;John Jackson&quot;,
            &quot;to_name&quot;: &quot;Corwin Velda&quot;,
            &quot;submitted_at&quot;: 1597066499,
            &quot;created_at&quot;: 1597066528,
            &quot;subject&quot;: &quot;Re: Test message subject&quot;,
            &quot;snippet&quot;: &quot;Short Message Body...&quot;,
            &quot;message_body&quot;: &quot;Message body text...&quot;,
            &quot;message_raw&quot;: &quot;Raw Message text with html tags...&quot;
        }
    ]
}</code>
 </pre>
    </span>
<span id="execution-results-GETapi-v1-contacts--contact_id--replies" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-v1-contacts--contact_id--replies"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-v1-contacts--contact_id--replies" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-v1-contacts--contact_id--replies" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-v1-contacts--contact_id--replies"></code></pre>
</span>
<form id="form-GETapi-v1-contacts--contact_id--replies" data-method="GET"
      data-path="api/v1/contacts/{contact_id}/replies"
      data-authed="1"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-v1-contacts--contact_id--replies', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/v1/contacts/{contact_id}/replies</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Authorization</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Authorization" class="auth-value"               data-endpoint="GETapi-v1-contacts--contact_id--replies"
               value="Bearer {token}"
               data-component="header">
    <br>
<p>Example: <code>Bearer {token}</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Content-Type"                data-endpoint="GETapi-v1-contacts--contact_id--replies"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Accept"                data-endpoint="GETapi-v1-contacts--contact_id--replies"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        <h4 class="fancy-heading-panel"><b>URL Parameters</b></h4>
                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>contact_id</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="contact_id"                data-endpoint="GETapi-v1-contacts--contact_id--replies"
               value="83jm7zlgve0nlzq4d15orxywn"
               data-component="url">
    <br>
<p>The id of a contact. Example: <code>83jm7zlgve0nlzq4d15orxywn</code></p>
            </div>
                    </form>

                    <h2 id="email-messages-GETapi-v1-email-threads--email_thread_id-">Display an email thread.</h2>

<p>
<small class="badge badge-darkred">requires authentication</small>
</p>

<p>Return an email thread with all its messages.</p>

<span id="example-requests-GETapi-v1-email-threads--email_thread_id-">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "https://app.wavo.co/api/v1/email-threads/rdle34wnjy94qj6m178p0gz5v" \
    --header "Authorization: Bearer {token}" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://app.wavo.co/api/v1/email-threads/rdle34wnjy94qj6m178p0gz5v"
);

const headers = {
    "Authorization": "Bearer {token}",
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>


<div class="php-example">
    <pre><code class="language-php">$client = new \GuzzleHttp\Client();
$response = $client-&gt;get(
    'https://app.wavo.co/api/v1/email-threads/rdle34wnjy94qj6m178p0gz5v',
    [
        'headers' =&gt; [
            'Authorization' =&gt; 'Bearer {token}',
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre></div>


<div class="python-example">
    <pre><code class="language-python">import requests
import json

url = 'https://app.wavo.co/api/v1/email-threads/rdle34wnjy94qj6m178p0gz5v'
headers = {
  'Authorization': 'Bearer {token}',
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}

response = requests.request('GET', url, headers=headers)
response.json()</code></pre></div>

</span>

<span id="example-responses-GETapi-v1-email-threads--email_thread_id-">
            <blockquote>
            <p>Example response (200):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;data&quot;: {
        &quot;id&quot;: &quot;rdle34wnjy94qj6m178p0gz5v&quot;,
        &quot;campaign_id&quot;: &quot;jxv8qkeml4ml1g69y3w5&quot;,
        &quot;contact_id&quot;: &quot;8j5orm17lnqgpkqvg9pk4z3ew&quot;,
        &quot;email_messages&quot;: [
            {
                &quot;id&quot;: &quot;2dpkq21yvjn4r530y56gzwlm3e08ox&quot;,
                &quot;thread_id&quot;: &quot;l12zpqxdn36mwrog08e5mwrkj&quot;,
                &quot;contact_id&quot;: &quot;83jm7zlgve0nlzq4d15orxywn&quot;,
                &quot;from_email&quot;: &quot;<EMAIL>&quot;,
                &quot;to_email&quot;: &quot;<EMAIL>&quot;,
                &quot;from_name&quot;: &quot;Corwin Velda&quot;,
                &quot;to_name&quot;: &quot;Ella Jones&quot;,
                &quot;submitted_at&quot;: 1597066219,
                &quot;created_at&quot;: 1597066208,
                &quot;subject&quot;: &quot;Test message subject&quot;,
                &quot;snippet&quot;: &quot;Short Message Body...&quot;,
                &quot;message_body&quot;: &quot;Message body text...&quot;,
                &quot;message_raw&quot;: &quot;Raw Message text with html tags...&quot;
            },
            {
                &quot;id&quot;: &quot;66lgem812pz0w7y6g9yoj3nr4kvqdx&quot;,
                &quot;thread_id&quot;: &quot;rdle34wnjy94qj6m178p0gz5v&quot;,
                &quot;contact_id&quot;: &quot;8j5orm17lnqgpkqvg9pk4z3ew&quot;,
                &quot;from_email&quot;: &quot;<EMAIL>&quot;,
                &quot;to_email&quot;: &quot;<EMAIL>&quot;,
                &quot;from_name&quot;: &quot;Ella Jones&quot;,
                &quot;to_name&quot;: &quot;Corwin Velda&quot;,
                &quot;submitted_at&quot;: 1597066351,
                &quot;created_at&quot;: 1597066376,
                &quot;subject&quot;: &quot;Re: Test message subject&quot;,
                &quot;snippet&quot;: &quot;Short Message Body...&quot;,
                &quot;message_body&quot;: &quot;Message body text...&quot;,
                &quot;message_raw&quot;: &quot;Raw Message text with html tags...&quot;
            }
        ]
    }
}</code>
 </pre>
    </span>
<span id="execution-results-GETapi-v1-email-threads--email_thread_id-" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-v1-email-threads--email_thread_id-"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-v1-email-threads--email_thread_id-" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-v1-email-threads--email_thread_id-" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-v1-email-threads--email_thread_id-"></code></pre>
</span>
<form id="form-GETapi-v1-email-threads--email_thread_id-" data-method="GET"
      data-path="api/v1/email-threads/{email_thread_id}"
      data-authed="1"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-v1-email-threads--email_thread_id-', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/v1/email-threads/{email_thread_id}</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Authorization</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Authorization" class="auth-value"               data-endpoint="GETapi-v1-email-threads--email_thread_id-"
               value="Bearer {token}"
               data-component="header">
    <br>
<p>Example: <code>Bearer {token}</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Content-Type"                data-endpoint="GETapi-v1-email-threads--email_thread_id-"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Accept"                data-endpoint="GETapi-v1-email-threads--email_thread_id-"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        <h4 class="fancy-heading-panel"><b>URL Parameters</b></h4>
                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>email_thread_id</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="email_thread_id"                data-endpoint="GETapi-v1-email-threads--email_thread_id-"
               value="rdle34wnjy94qj6m178p0gz5v"
               data-component="url">
    <br>
<p>The id of the email thread. Example: <code>rdle34wnjy94qj6m178p0gz5v</code></p>
            </div>
                    </form>

                <h1 id="suppression">Suppression</h1>

    <p>APIs for managing suppression list.</p>

                                <h2 id="suppression-POSTapi-v1-suppressions-emails">Add new email address to suppression list</h2>

<p>
<small class="badge badge-darkred">requires authentication</small>
</p>

<p>This will add new email addresses to a client's suppression list.
Any contacts matching these emails, will be set as unsubscribed.</p>

<span id="example-requests-POSTapi-v1-suppressions-emails">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request POST \
    "https://app.wavo.co/api/v1/suppressions/emails" \
    --header "Authorization: Bearer {token}" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json" \
    --data "{
    \"client_id\": \"a83445df46as5432mga\",
    \"emails\": [
        \"<EMAIL>\",
        \"<EMAIL>\"
    ]
}"
</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://app.wavo.co/api/v1/suppressions/emails"
);

const headers = {
    "Authorization": "Bearer {token}",
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "client_id": "a83445df46as5432mga",
    "emails": [
        "<EMAIL>",
        "<EMAIL>"
    ]
};

fetch(url, {
    method: "POST",
    headers,
    body: JSON.stringify(body),
}).then(response =&gt; response.json());</code></pre></div>


<div class="php-example">
    <pre><code class="language-php">$client = new \GuzzleHttp\Client();
$response = $client-&gt;post(
    'https://app.wavo.co/api/v1/suppressions/emails',
    [
        'headers' =&gt; [
            'Authorization' =&gt; 'Bearer {token}',
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
        'json' =&gt; [
            'client_id' =&gt; 'a83445df46as5432mga',
            'emails' =&gt; [
                '<EMAIL>',
                '<EMAIL>',
            ],
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre></div>


<div class="python-example">
    <pre><code class="language-python">import requests
import json

url = 'https://app.wavo.co/api/v1/suppressions/emails'
payload = {
    "client_id": "a83445df46as5432mga",
    "emails": [
        "<EMAIL>",
        "<EMAIL>"
    ]
}
headers = {
  'Authorization': 'Bearer {token}',
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}

response = requests.request('POST', url, headers=headers, json=payload)
response.json()</code></pre></div>

</span>

<span id="example-responses-POSTapi-v1-suppressions-emails">
            <blockquote>
            <p>Example response (201):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;data&quot;: [
        &quot;<EMAIL>&quot;,
        &quot;<EMAIL>&quot;
    ]
}</code>
 </pre>
    </span>
<span id="execution-results-POSTapi-v1-suppressions-emails" hidden>
    <blockquote>Received response<span
                id="execution-response-status-POSTapi-v1-suppressions-emails"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-POSTapi-v1-suppressions-emails" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-POSTapi-v1-suppressions-emails" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-POSTapi-v1-suppressions-emails"></code></pre>
</span>
<form id="form-POSTapi-v1-suppressions-emails" data-method="POST"
      data-path="api/v1/suppressions/emails"
      data-authed="1"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('POSTapi-v1-suppressions-emails', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
            </h3>
            <p>
            <small class="badge badge-black">POST</small>
            <b><code>api/v1/suppressions/emails</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Authorization</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Authorization" class="auth-value"               data-endpoint="POSTapi-v1-suppressions-emails"
               value="Bearer {token}"
               data-component="header">
    <br>
<p>Example: <code>Bearer {token}</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Content-Type"                data-endpoint="POSTapi-v1-suppressions-emails"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Accept"                data-endpoint="POSTapi-v1-suppressions-emails"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <h4 class="fancy-heading-panel"><b>Body Parameters</b></h4>
        <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>client_id</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="client_id"                data-endpoint="POSTapi-v1-suppressions-emails"
               value="a83445df46as5432mga"
               data-component="body">
    <br>
<p>The id of a client. Example: <code>a83445df46as5432mga</code></p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>emails</code></b>&nbsp;&nbsp;
<small>string[]</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="emails[0]"                data-endpoint="POSTapi-v1-suppressions-emails"
               data-component="body">
        <input type="text" style="display: none"
               name="emails[1]"                data-endpoint="POSTapi-v1-suppressions-emails"
               data-component="body">
    <br>
<p>List of emails that should be put on client's suppression list.</p>
        </div>
        </form>

                    <h2 id="suppression-POSTapi-v1-suppressions-domains">Add new domain address to suppression list</h2>

<p>
<small class="badge badge-darkred">requires authentication</small>
</p>

<p>This will add new domains to a client's suppression list.
Any contacts matching these domains, will be set as unsubscribed.</p>

<span id="example-requests-POSTapi-v1-suppressions-domains">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request POST \
    "https://app.wavo.co/api/v1/suppressions/domains" \
    --header "Authorization: Bearer {token}" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json" \
    --data "{
    \"client_id\": \"a83445df46as5432mga\",
    \"domains\": [
        \"domain1.com\",
        \"domain2.com\"
    ]
}"
</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://app.wavo.co/api/v1/suppressions/domains"
);

const headers = {
    "Authorization": "Bearer {token}",
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "client_id": "a83445df46as5432mga",
    "domains": [
        "domain1.com",
        "domain2.com"
    ]
};

fetch(url, {
    method: "POST",
    headers,
    body: JSON.stringify(body),
}).then(response =&gt; response.json());</code></pre></div>


<div class="php-example">
    <pre><code class="language-php">$client = new \GuzzleHttp\Client();
$response = $client-&gt;post(
    'https://app.wavo.co/api/v1/suppressions/domains',
    [
        'headers' =&gt; [
            'Authorization' =&gt; 'Bearer {token}',
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
        'json' =&gt; [
            'client_id' =&gt; 'a83445df46as5432mga',
            'domains' =&gt; [
                'domain1.com',
                'domain2.com',
            ],
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre></div>


<div class="python-example">
    <pre><code class="language-python">import requests
import json

url = 'https://app.wavo.co/api/v1/suppressions/domains'
payload = {
    "client_id": "a83445df46as5432mga",
    "domains": [
        "domain1.com",
        "domain2.com"
    ]
}
headers = {
  'Authorization': 'Bearer {token}',
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}

response = requests.request('POST', url, headers=headers, json=payload)
response.json()</code></pre></div>

</span>

<span id="example-responses-POSTapi-v1-suppressions-domains">
            <blockquote>
            <p>Example response (201):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;data&quot;: [
        &quot;domain1.com&quot;,
        &quot;domain2.com&quot;
    ]
}</code>
 </pre>
    </span>
<span id="execution-results-POSTapi-v1-suppressions-domains" hidden>
    <blockquote>Received response<span
                id="execution-response-status-POSTapi-v1-suppressions-domains"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-POSTapi-v1-suppressions-domains" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-POSTapi-v1-suppressions-domains" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-POSTapi-v1-suppressions-domains"></code></pre>
</span>
<form id="form-POSTapi-v1-suppressions-domains" data-method="POST"
      data-path="api/v1/suppressions/domains"
      data-authed="1"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('POSTapi-v1-suppressions-domains', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
            </h3>
            <p>
            <small class="badge badge-black">POST</small>
            <b><code>api/v1/suppressions/domains</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Authorization</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Authorization" class="auth-value"               data-endpoint="POSTapi-v1-suppressions-domains"
               value="Bearer {token}"
               data-component="header">
    <br>
<p>Example: <code>Bearer {token}</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Content-Type"                data-endpoint="POSTapi-v1-suppressions-domains"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="Accept"                data-endpoint="POSTapi-v1-suppressions-domains"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <h4 class="fancy-heading-panel"><b>Body Parameters</b></h4>
        <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>client_id</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="client_id"                data-endpoint="POSTapi-v1-suppressions-domains"
               value="a83445df46as5432mga"
               data-component="body">
    <br>
<p>The id of a client. Example: <code>a83445df46as5432mga</code></p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>domains</code></b>&nbsp;&nbsp;
<small>string[]</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
               name="domains[0]"                data-endpoint="POSTapi-v1-suppressions-domains"
               data-component="body">
        <input type="text" style="display: none"
               name="domains[1]"                data-endpoint="POSTapi-v1-suppressions-domains"
               data-component="body">
    <br>
<p>List of domains that should be put on client's suppression list.</p>
        </div>
        </form>

            

        
    </div>
    <div class="dark-box">
                    <div class="lang-selector">
                                                        <button type="button" class="lang-button" data-language-name="bash">bash</button>
                                                        <button type="button" class="lang-button" data-language-name="javascript">javascript</button>
                                                        <button type="button" class="lang-button" data-language-name="php">php</button>
                                                        <button type="button" class="lang-button" data-language-name="python">python</button>
                            </div>
            </div>
</div>
</body>
</html>
