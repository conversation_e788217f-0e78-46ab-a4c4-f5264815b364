@extends('layouts.app')

@section('page-title', 'Edit Email Account')

@section('page-styles')
    <style>
		.datepicker.datepicker-dropdown {
			z-index: 10000 !important;
		}
		.blocked-helper-text {
			font-size: 12px;
			line-height: 14px;
			padding-top: 10px;
			display: block;
		}
		.blocked-box-footer {
			padding: 0px 65px 0 0;
			position: relative;
		}
		.blocked-box-footer .btn {
			position: absolute;
			top: 0px;
			right: 0px;
		}

		#scheduleFreezeModal .select2.select2-container {
			width: 100% !important;
		}

		#scheduleFreezeTooltip {
			position: absolute;
		    left: 140px;
		    font-size: 13px;
		    top: 12px;
		}

		#aliasTooltip {
			margin-left: 6px;
			font-size: 13px;
		}

		#campaignSignatureEditor .toolbar li[title="align"],
	    #campaignSignatureEditor .toolbar li[title="list"],
	    #campaignSignatureEditor .toolbar li[title="table"] {
	    	display: none !important;
	    }

		#delivery-warmupinbox .checkbox-custom {
			margin-top: 0;
		}
		#delivery-warmupinbox .checkbox-custom label::before,
		#delivery-warmupinbox .checkbox-custom label::after {
			top: 4px;
		}

        .warmup-timezone-box .select2-container {
            width: 100% !important;
        }

		@media (max-width: 1265px) {
			.blocked-helper-text {
				padding-top: 5px;
			}
		}
    </style>
@endsection

@section('page-scripts')
    <script src="//rawcdn.githack.com/RickStrahl/jquery-resizable/master/dist/jquery-resizable.min.js"></script>
@endsection

@section('content-layout')
	<nylasemail-edit
        :emailaccount="{{ $account }}"
        :warmup="{{ json_encode($account->emailWarmup) }}"
        :team_blocks="{{ json_encode($team_blocks) }}"
		:disable_alias_edit="{{ json_encode($account->campaigns()->notArchived()->exists()) }}"
        team_block_count="{{ $team_block_count }}"
        inline-template
    >
		<div class="page">
	        <div class="page-header page-header-bordered">
	            <h1 class="page-title">
					Edit <span class="hidden-sm-down">Email Account</span>
	            </h1>
				<p class="page-description m-0">
					{{ $account->email_address }}
					<span class="hidden-sm-down">
						({{ $account->email_server_type_nice_name }})
					</span>
				</p>
	            <div class="page-header-actions">
	            	<a href="{{ route('email-accounts.reauth', $account) }}" class="btn btn-outline btn-danger btn-round" >
						<span class="hidden-md-down">Re-authenticate</span>
						<i class="icon wb-lock" aria-hidden="true"></i>
					</a>
	            	<a href="{{ route('email-accounts.show', $account) }}" class="btn btn-outline btn-primary btn-round">
						<span class="hidden-md-down">Messages</span>
						<i class="icon wb-chat-text" aria-hidden="true"></i>
					</a>
					@if($account->agency->has_warmup)
            @if ($account->sends_warmup_messages)
              <a href="{{ route('email-accounts.warmup-inbox', $account) }}" class="btn btn-outline btn-primary btn-round">
                <span class="hidden-md-down">Warmup</span>
                <i class="icon wb-envelope-open" aria-hidden="true"></i>
              </a>
            @else
              <a href="#" class="btn btn-outline btn-default btn-round disabled" disabled="disabled">
                <span class="hidden-md-down">Warmup</span>
                <i class="icon wb-envelope-open" aria-hidden="true"></i>
              </a>
            @endif
          @endif
	            	<a href="{{ route('email-accounts.index') }}" class="btn btn-outline btn-default btn-round" >
						<span class="hidden-md-down">Back to Email Accounts</span>
						<i class="icon wb-chevron-left" aria-hidden="true"></i>
					</a>
				</div>
	        </div>

	        <div class="page-content container-fluid">
                @include('inbox.edit.edit-alerts')

                <div class="row">
                    <div class="col-md-12">
                        <div>
                            <div class="tab-pane" role="tabpanel" id="sectionTwoTab">
                                <div class="panel nav-tabs-horizontal">
                                    <div class="panel-heading panel-heading-tab spark-settings-tabs">
                                        <ul class="nav nav-tabs nav-tabs-solid spark-settings-stacked-tabs" id="settingsIntegrationTab" role="tablist">
                                            <li class="nav-item" role="presentation">
                                                <a class="nav-link active"
													data-toggle="tab"
													href="#email_account_form"
													aria-controls="email_account_form"
													role="tab"
													aria-selected="true"
												>
                                                    <span class="hidden-md-down">Email</span> Account
                                                </a>
                                            </li>
                                            @if (config('app.emailWarmupEnable'))
                                                <li class="nav-item" role="presentation">
                                                    <a class="nav-link"
														data-toggle="tab"
														href="#email_account_sending"
														aria-controls="email_account_sending"
														role="tab"
														aria-selected="false"
													>
                                                        Sending <span class="hidden-md-down">Configuration</span>
                                                    </a>
                                                </li>
                                            @endif
                                            @if($account->agency->has_warmup)
                                            <li class="nav-item" role="presentation">
                                                <a class="nav-link"
													data-toggle="tab"
													href="#email_inbox_warmup"
													aria-controls="email_inbox_warmup"
													role="tab"
													aria-selected="false"
												>
                                                    <span class="hidden-md-down">Inbox</span> Warmup <span class="hidden-md-down">Configuration</span>
                                                </a>
                                            </li>
                                            @endif
                                            <li class="nav-item" role="presentation">
                                                <a class="nav-link"
													data-toggle="tab"
													href="#email_account_freeze"
													aria-controls="email_account_freeze"
													role="tab"
													aria-selected="false"
												>
                                                    Freeze <span class="hidden-md-down">Periods</span>
                                                </a>
                                            </li>
                                            <li class="nav-item" role="presentation">
                                                <a class="nav-link"
													data-toggle="tab"
													href="#email_account_suppression"
													aria-controls="email_account_suppression"
													role="tab"
													aria-selected="false"
												>
                                                    Suppression <span class="hidden-md-down">List</span>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>

                                    <div class="" data-plugin="tabs">
                                        <div class="tab-content">
                                            <div class="tab-pane pt-20 active" id="email_account_form" role="tabpanel">
                                                @include('inbox.edit.edit-form')
                                            </div>
                                            @if (config('app.emailWarmupEnable'))
                                                <div class="tab-pane pt-20" id="email_account_sending" role="tabpanel">
                                                    @include('inbox.edit.edit-warmup')
                                                </div>
                                            @endif
                                            @if($account->agency->has_warmup)
                                            <div class="tab-pane pt-20" id="email_inbox_warmup" role="tabpanel">
                                                @include('inbox.edit.edit-warmup-inbox')
                                            </div>
                                            @endif
                                            <div class="tab-pane pt-20" id="email_account_freeze" role="tabpanel">
                                                @include('inbox.edit.edit-freezeperiod')
                                            </div>
                                            <div class="tab-pane pt-20" id="email_account_suppression" role="tabpanel">
                                                @include('inbox.edit.edit-suppression')
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {{--
			    <div class="row">
			    	<div class="col-xl-6 mb-40">
						<div class="row">
							<div class="col-md-12">
                                @include('inbox.edit.edit-form')
							</div>
						</div>
			    	</div>

			    	<div class="col-xl-6">
			    		@if (config('app.emailWarmupEnable'))
			    			@include('inbox.edit.edit-warmup')
			    		@endif

						@if($account->agency->has_warmup)
							@include('inbox.edit.edit-warmup-inbox')
						@endif

			    		@include('inbox.edit.edit-freezeperiod')
			    		@include('inbox.edit.edit-suppression')

			    	</div>
			    </div>
                --}}
			</div>

		</div>
	</nylasemail-edit>
@endsection
