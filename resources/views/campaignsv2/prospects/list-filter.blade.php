<!-- ACTIONS -->
<div class="float-right my-20 hidden-sm-down">
    <div>
        <label class="font13">&nbsp;</label>
    </div>

    @if($campaign->status == 'RUNNING')
        <div v-if="arrSelectedIds.length">
            <button type="button" class="btn btn-primary btn-icon px-20" @click="openBulkEditModal">
                <i class="icon wb-edit" v-if="!isDeleting"></i>
                <span class="hidden-lg-down" v-if="!moreFilters">Bulk Edit</span>
            </button>

            @if (Auth::user()->can('prospect.export') || Auth::user()->can('agency-admin'))
                <form target="_blank" id="exportFormIds"
                    action="{{ route('prospects.export.index', $campaign) }}"
                    method="POST" class="d-inline-block"
                    v-if="!isSelectedAll"
                >
                    @csrf
                    <input type="hidden" name="export_type" value="selected">
                    <input type="hidden" class="prospectExportIds" name="prospect_ids">
                    <input type="hidden" class="prospectExportFilters" name="prospect_filters">
                    <button class="btn btn-info px-20" type="button" @click.prevent="exportProspectCsv('exportFormIds')">
                        <i class="icon wb-download"></i>
                        <span class="hidden-md-down" v-if="!moreFilters">
                            Export<span class="hidden-lg-down"> Selected</span>
                            (@{{arrSelectedIds.length}})
                        </span>
                    </button>
                </form>

                <form target="_blank" id="exportFormAll"
                    action="{{ route('prospects.export.index', $campaign) }}"
                    method="POST" class="d-inline-block"
                    v-if="isSelectedAll"
                >
                    @csrf
                    <input type="hidden" name="export_type" value="all">
                    <input type="hidden" class="prospectExportFilters" name="prospect_filters">
                    <input type="hidden" class="prospectExportIds" name="prospect_ids">
                    <button class="btn btn-info px-20" type="button" @click.prevent="exportProspectCsv('exportFormAll')">
                        <i class="icon wb-download"></i>
                        <span class="hidden-md-down" v-if="!moreFilters">
                            Export<span class="hidden-lg-down"> Selected</span>
                            (@{{colProspects.total}})
                        </span>
                    </button>
                </form>
            @endif

            <button type="button"
                v-if="!isSelectedAll"
                class="btn btn-danger btn-icon px-20"
                :disabled="isDeleting"
                @click="cancelEdit('delete')"
            >
                <i class="icon wb-trash" v-if="!isDeleting"></i>
                <i class="fa fa-spinner fa-spin" v-if="isDeleting"></i>
                <span class="hidden-md-down" v-if="!moreFilters">
                    Delete <span class="hidden-lg-down">Selected</span>
                    (@{{arrSelectedIds.length}})
                </span>
            </button>

            <button type="button"
                v-if="isSelectedAll"
                class="btn btn-danger btn-icon px-20"
                :disabled="isDeleting"
                @click="cancelEdit('delete')"
            >
                <i class="icon wb-trash" v-if="!isDeleting"></i>
                <i class="fa fa-spinner fa-spin" v-if="isDeleting"></i>
                <span class="hidden-md-down" v-if="!moreFilters">
                    Delete <span class="hidden-lg-down">Selected</span>
                    (@{{colProspects.total}})
                </span>
            </button>

            <button type="button"
                v-if="!isSelectedAll"
                class="btn btn-default btn-icon px-20 ml-20"
                :disabled="isDeleting"
                @click="selectAllProspects"
            >
                <i class="icon wb-align-justify"></i>
                <span class="hidden-lg-down">Select All</span>
            </button>
            <button type="button"
                v-if="isSelectedAll"
                class="btn btn-default btn-icon px-20 ml-20"
                :disabled="isDeleting"
                @click="deleteProspectCancel"
            >
                <i class="icon wb-close"></i>
                <span class="hidden-lg-down">Unselect All</span>
            </button>
        </div>

        <!-- @click="cancelEdit('add')" -->
        <div v-else="arrSelectedIds.length">
            <button type="button"
                    @click="openManualModal"
                    class="btn btn-icon btn-primary px-20"
                    :disabled="onFreePlan"
            >
                <i class="icon wb-plus" aria-hidden="true"></i>
                <span class="hidden-lg-down" v-if="!moreFilters">Add Single Contact</span>
            </button>

            <!-- @click="cancelEdit('upload')" -->
            <button type="button"
                    @click="openCsvImport"
                    class="btn btn-icon btn-primary px-20"
                    :disabled="onFreePlan"
            >
                <i class="icon wb-upload" aria-hidden="true"></i>
                <span class="hidden-lg-down" v-if="!moreFilters">Upload CSV</span>
            </button>
        </div>
    @else
        <div v-if="arrSelectedIds.length">
            @if(!$campaign->cleaned_at)
            <button type="button" class="btn btn-primary btn-icon px-20" @click="openBulkEditModal">
                <i class="icon wb-edit" v-if="!isDeleting"></i>
                <span class="hidden-lg-down" v-if="!moreFilters">Bulk Edit</span>
            </button>
            @endif

            @if (Auth::user()->can('prospect.export') || Auth::user()->can('agency-admin'))
                <form target="_blank" id="exportFormIds"
                    action="{{ route('prospects.export.index', $campaign) }}"
                    method="POST" class="d-inline-block"
                    v-if="!isSelectedAll"
                >
                    @csrf
                    <input type="hidden" name="export_type" value="selected">
                    <input type="hidden" class="prospectExportIds" name="prospect_ids">
                    <input type="hidden" class="prospectExportFilters" name="prospect_filters">
                    <button class="btn btn-info " type="button" @click.prevent="exportProspectCsv('exportFormIds')">
                        <i class="icon wb-download"></i>
                        <span class="hidden-md-down" v-if="!moreFilters">
                            Export<span class="hidden-lg-down d-none"> Selected</span>
                            (@{{arrSelectedIds.length}})
                        </span>
                    </button>
                </form>

                <form target="_blank" id="exportFormAll"
                    action="{{ route('prospects.export.index', $campaign) }}"
                    method="POST" class="d-inline-block"
                    v-if="isSelectedAll"
                >
                    @csrf
                    <input type="hidden" name="export_type" value="all">
                    <input type="hidden" class="prospectExportFilters" name="prospect_filters">
                    <input type="hidden" class="prospectExportIds" name="prospect_ids">
                    <button class="btn btn-info" type="button"
                        @click.prevent="exportProspectCsv('exportFormAll')"
                    >
                        <i class="icon wb-download"></i>
                        <span class="hidden-md-down" v-if="!moreFilters">
                            Export<span class="hidden-lg-down d-none"> Selected</span>
                            (@{{colProspects.total}})
                        </span>
                    </button>
                </form>
            @endif


            @if(!$campaign->cleaned_at)
            <button type="button"
                v-if="!isSelectedAll"
                class="btn  btn-danger btn-icon px-20"
                :disabled="isDeleting"
                @click="deleteProspects(false)"
            >
                <i class="icon wb-trash" v-if="!isDeleting"></i>
                <i class="fa fa-spinner fa-spin" v-if="isDeleting"></i>
                <span class="hidden-md-down" v-if="!moreFilters">
                    Delete <span class="hidden-lg-down d-none">Selected</span>
                    (@{{arrSelectedIds.length}})
                </span>
            </button>

            <button type="button"
                v-if="isSelectedAll"
                class="btn  btn-danger btn-icon px-20"
                :disabled="isDeleting"
                @click="deleteProspects(true)"
            >
                <i class="icon wb-trash" v-if="!isDeleting"></i>
                <i class="fa fa-spinner fa-spin" v-if="isDeleting"></i>
                <span class="hidden-md-down" v-if="!moreFilters">
                    Delete <span class="hidden-lg-down d-none">Selected</span>
                    (@{{colProspects.total}})
                </span>
            </button>
            @endif

            <button type="button"
                v-if="!isSelectedAll"
                class="btn  btn-default btn-icon px-20 ml-20"
                :disabled="isDeleting"
                @click="selectAllProspects"
            >
                <i class="icon wb-align-justify"></i>
                <span class="hidden-lg-down">Select All</span>
            </button>
            <button type="button"
                v-if="isSelectedAll"
                class="btn  btn-default btn-icon px-20 ml-20"
                :disabled="isDeleting"
                @click="deleteProspectCancel"
            >
                <i class="icon wb-close"></i>
                <span class="hidden-lg-down">Unselect All</span>
            </button>
        </div>
        <div v-else="arrSelectedIds.length">
            @if(!$campaign->cleaned_at)
                <button type="button"
                        @click="openManualModal"
                        class="btn btn-icon btn-primary px-20"
                        :disabled="onFreePlan"
                >
                    <i class="icon wb-plus" aria-hidden="true"></i>
                    <span class="hidden-lg-down" v-if="!moreFilters">Add Single Contact</span>
                </button>

                <button type="button"
                        @click="openCsvImport"
                        class="btn btn-icon btn-primary px-20"
                        :disabled="onFreePlan"
                >
                    <i class="icon wb-upload" aria-hidden="true"></i>
                    <span class="hidden-lg-down" v-if="!moreFilters">Upload CSV</span>
                </button>
            @else
                <button type="button" class="btn btn-icon btn-primary px-20 cd" disabled>
                    <i class="icon wb-plus" aria-hidden="true"></i>
                    <span class="hidden-lg-down" v-if="!moreFilters">Add Single Contact</span>
                </button>

                <button type="button" disabled  class="btn btn-icon btn-primary px-20 cd">
                    <i class="icon wb-upload" aria-hidden="true"></i>
                    <span class="hidden-lg-down" v-if="!moreFilters">Upload CSV</span>
                </button>
            @endif
        </div>
    @endif
</div>



<!-- FILTERS -->
<div class="mt-20" :class="{'header-delete-mode-filters': arrSelectedIds.length}">
    <div class="d-inline-block w-180 mb-20">
        <label class="font13"><span>Search By </span>Keywords</label>
        <input type="text"
            v-model="filters.keywords"
            placeholder="Search"
            class="form-control input-outline-default px-10"
            @keyup="filterByKeywords()"
        >
    </div>
    <div class="d-inline-block w-120 mb-20">
        <label class="font13"><span>Filter By </span>Status</label>
        <select v-model="filters.status"
            data-plugin="selectpicker"
            data-style=" btn-outline btn-default"
            class="form-control select-filter "
            @change="getProspects(1)"
        >
            <option value="all">ALL</option>
            @foreach(App\Prospect::STATUSES as $status)
                <option value="{{$status}}">{{$status}}</option>
            @endforeach
        </select>
    </div>
    <div class="d-inline-block w-120 mb-20">
        <label class="font13"><span>Filter By </span>Interest</label>
        <select v-model="filters.interested"
            class="form-control select-filter "
            @change="getProspects(1)"
            data-plugin="selectpicker"
            data-style=" btn-outline btn-default "
        >
            <option value="all">ALL</option>
            @foreach(App\Prospect::INTEREST_LEVELS as $interestLevel)
                <option value="{{$interestLevel}}">{{$interestLevel}}</option>
            @endforeach
        </select>
    </div>
    <div class="d-inline-block w-120 mb-20 hidden-filters-md">
        <label class="font13">Missing Data</label>
        <select v-model="filters.is_missing_data"
                class="form-control select-filter"
                @change="getProspects(1)"
                data-plugin="selectpicker"
                data-style=" btn-outline btn-default"
        >
            <option value="">ALL</option>
            <option value="0">NO</option>
            <option value="1">YES</option>
        </select>
    </div>
    <div class="d-inline-block w-120 mb-20 hidden-filters-md">
        <label class="font13">Suppressed</label>
        <select v-model="filters.is_suppressed"
                class="form-control select-filter"
                @change="getProspects(1)"
                data-plugin="selectpicker"
                data-style=" btn-outline btn-default"
        >
            <option value="">ALL</option>
            <option value="0">NO</option>
            <option value="1">YES</option>
        </select>
    </div>

    <div class="d-inline-block w-120 mb-20 hidden-filters-md" v-if="moreFilters">
        <label class="font13">Steps <span>Completed</span></label>
        <select v-model="filters.completed_steps"
            class="form-control select-filter"
            @change="getProspects(1)"
            data-plugin="selectpicker"
            data-style=" btn-outline btn-default"
        >
            <option value="all">ALL</option>
            <option value="0">0</option>
            <option v-for="(stage, stageIndex) in campaign.campaign_stages" :value="stage.number">
                @{{stage.number}}
            </option>
        </select>
    </div>

    <div class="d-inline-block w-120 mb-20 hidden-filters-md" v-if="moreFilters">
        <label class="font13"><span>Filter </span>By Import</label>
        <select v-model="filters.import_file"
            class="form-control select-filter"
            @change="getProspects(1)"
            data-plugin="selectpicker"
            data-style=" btn-outline btn-default"
        >
            <option value="all">ALL</option>
            <option v-for="(file, index) in importFiles" :value="file.import_file">
                @{{getImportFileName(file)}} (@{{file.created_at}})
            </option>
            <option value="manual">Manually Added</option>
        </select>
    </div>

    <div class="d-inline-block w-120 mb-20 hidden-filters-md filter-item-perpage" v-if="moreFilters">
        <label class="font13"><span>Show </span>Per Page</label>
        <select v-model="filters.per_page"
            class="form-control select-filter"
            @change="filterPerPage"
            data-plugin="selectpicker"
            data-style=" btn-outline btn-default"
        >
            <option value="20">20 Contacts</option>
            <option value="50">50 Contacts</option>
            <option value="100">100 Contacts</option>
            <option value="150">150 Contacts</option>
            <option value="200">200 Contacts</option>
            <option value="350">350 Contacts</option>
            <option value="500">500 Contacts</option>
        </select>
    </div>

    <div class="d-inline-block w-120 mb-20 hidden-filters-md" v-if="!moreFilters">
        <label class="font13">&nbsp;</label>
        <button type="button" class="btn btn-default btn-outline btn-icon px-10" @click="moreFilters = true">
            <i class="icon fa fa-angle-double-right"></i>
        </button>
    </div>
    <div class="d-inline-block w-120 my-20 hidden-filters-md" v-else>
        <label class="font13">&nbsp;</label>
        <button type="button" class="btn btn-default btn-outline btn-icon px-10" @click="moreFilters = false">
            <i class="icon fa fa-angle-double-left"></i>
        </button>
    </div>
</div>
