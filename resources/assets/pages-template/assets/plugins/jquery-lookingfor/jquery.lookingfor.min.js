/**
 * jquery.lookingfor
 * <AUTHOR> http://burtsev.me, 2014
 * @license MIT
 */
!function(a){"function"==typeof define&&define.amd?define(["jquery"],a):a(jQuery)}(function(a){"use strict";function b(b,c){a.extend(this,{_input:null,_items:null,_container:a(b),value:"",cache:[],queryCharLimit:3,queryTimer:null,queryDelay:50,highlight:!1,highlightClass:"lfitem_match",highlightColor:"#ffde00",hiddenListClass:"lflist_hidden",hiddenItemAttr:"data-lfhidden",hiddenCount:0},c||{}),this._items=this.items?a(this.items,b):this._container.children(),this._input=a(this.input),this._items.length&&(this._input.length&&this._input.on("keyup change",this._debounce(function(){var b=(this._input.val()||"").toLowerCase();b=a.trim(b),b!==this.value&&(this.value=b,b.length>=this.queryCharLimit?this.query():this.showAll())},this.queryDelay,this)),this.addStyles(),this.indexing())}a.fn.lookingfor=function(a){return this.each(function(){new b(this,a)})},b.prototype={addStyles:function(){var b,c=a("head"),d=a("<style>").get(0),e=[["."+this.hiddenListClass+" ["+this.hiddenItemAttr+"]","display: none"],["."+this.highlightClass,"background: "+this.highlightColor]];c.append(d),b=d.sheet||document.styleSheets[0];for(var f,g,h=0;h<e.length;h++)f=e[h][0],g=e[h][1],b.insertRule?b.insertRule(f+"{"+g+"}",0):b.addRule&&b.addRule(f,g,0)},indexing:function(){var b=this;this._items.each(function(){var c=a(this);b.cache.push({node:this,html:this.innerHTML,text:(c.text()||"").toLowerCase(),hidden:!1})})},query:function(b){b=b||this.value,this.hiddenCount=0;for(var c,d=new RegExp(b,"ig"),e=a.proxy(this._paint,this),f=0,g=this.cache.length;g>f;f++)c=this.cache[f],-1===c.text.indexOf(b)?(c.hidden||(c.hidden=!0,c.node.setAttribute(this.hiddenItemAttr,"")),this.hiddenCount+=1):c.hidden&&(c.hidden=!1,c.node.removeAttribute(this.hiddenItemAttr)),this.highlight&&(c.matched&&(c.matched=!1,c.node.innerHTML=c.html),c.hidden||(c.matched=!0,c.node.innerHTML=c.html.replace(d,e)));this._container.addClass(this.hiddenListClass)},showAll:function(){if(this.hiddenCount){for(var a,b=0,c=this.cache.length;c>b;b++)a=this.cache[b],a.hidden=!1,a.node.removeAttribute(this.hiddenItemAttr),a.matched&&(a.matched=!1,a.node.innerHTML=a.html);this._container.removeClass(this.hiddenListClass),this.hiddenCount=0}},_paint:function(a){return'<span class="'+this.highlightClass+'">'+a+"</span>"},_debounce:function(a,b,c){var d=null,e=this;return function(){var f=arguments;clearTimeout(d),d=setTimeout(function(){a.apply(c||e,f)},b)}},_profiler:function(a,b,c){var d=this;return function(){var e=new Date;a.call(c||d),console.log(b||"",(new Date).getTime()-e.getTime())}}}});