/**
 * Brazilian portuguese translation for bootstrap-wysihtml5
 */
(function($){
    $.fn.wysihtml5.locale["pt-BR"] = {
        font_styles: {
            normal: "Texto normal",
            h1: "Título 1",
            h2: "Título 2",
            h3: "T<PERSON><PERSON>lo 3",
            h4: "Título 4",
            h5: "Título 5",
            h6: "Título 6"
        },
        emphasis: {
            bold: "Negrito",
            italic: "Itálico",
            underline: "Sublinhado",
            small: "Pequeno"
        },
        lists: {
            unordered: "Lista",
            ordered: "Lista numerada",
            outdent: "Remover indentação",
            indent: "Indentar"
        },
        link: {
            insert: "Inserir link",
            cancel: "Cancelar",
            target: "Abrir link em um nova janela"
        },
        image: {
            insert: "Inserir imagem",
            cancel: "Cancelar"
        },
        html: {
            edit: "Editar HTML"
        },
        colours: {
            black: "Preto",
            silver: "Prata",
            gray: "<PERSON><PERSON><PERSON>",
            maroon: "<PERSON><PERSON>",
            red: "<PERSON>er<PERSON><PERSON>",
            purple: "Roxo",
            green: "Verde",
            olive: "<PERSON>liva",
            navy: "Marin<PERSON>",
            blue: "Azul",
            orange: "Laranja"
        }
    };
}(j<PERSON><PERSON>y));