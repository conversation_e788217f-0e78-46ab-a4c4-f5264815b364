/**
 * Select2 Norwegian translation.
 *
 * Author: <PERSON><PERSON><PERSON> <<EMAIL>>
 */
(function ($) {
    "use strict";

    $.fn.select2.locales['no'] = {
        formatNoMatches: function () { return "Ingen treff"; },
        formatInputTooShort: function (input, min) { var n = min - input.length; return "Vennligst skriv inn " + n + (n>1 ? " flere tegn" : " tegn til"); },
        formatInputTooLong: function (input, max) { var n = input.length - max; return "Vennligst fjern " + n + " tegn"; },
        formatSelectionTooBig: function (limit) { return "Du kan velge maks " + limit + " elementer"; },
        formatLoadMore: function (pageNumber) { return "Laster flere resultater…"; },
        formatSearching: function () { return "<PERSON>øker…"; }
    };

    $.extend($.fn.select2.defaults, $.fn.select2.locales['no']);
})(jQuery);

