/*! iOSList - v2.0.0 -  * https://brianhadaway.github.io/iOSList
 * Copyright (c)  2014  <PERSON>; Licensed MIT */
/* Classes Edited By Pages - Revox Pvt Ltd */
! function (a) {
    var b = function (b, c) {
        this.$elem = a(b), this.$elem.data("instance", this), this.init(c)
    };
    b.prototype = {
        defaults: {
            classes: {
                animated: "list-view-animated",
                container: "list-view-wrapper",
                hidden: "list-view-hidden",
                stationaryHeader: "list-view-fake-header"
            },
            selectors: {
                groupContainer: ".list-view-group-container",
                groupHeader: ".list-view-group-header",
                stationaryHeader: "h2"
            }
        },
        init: function (b) {
            var c = this,
                d = navigator.userAgent.match(/ipad|iphone|ipod/gi) ? !0 : !1;
            this.options = a.extend(!0, {}, this.defaults, b || {}), this.elems = [], this.$elem.addClass("list-view"), this.$elem.children().wrapAll(["<div class='", this.options.classes.container, "' data-ios='", d, "'></div>"].join("")), this.$elem.prepend(["<", this.options.selectors.stationaryHeader, "/>"].join("")), this.$listWrapper = this.$elem.find("." + this.options.classes.container), this.$fakeHeader = this.$elem.find(this.options.selectors.stationaryHeader).eq(0), this.$fakeHeader.addClass(this.options.classes.stationaryHeader), this.$elem.find(this.options.selectors.groupContainer).each(function (a) {
                var b = c.$elem.find(c.options.selectors.groupContainer).eq(a),
                    d = b.find(c.options.selectors.groupHeader).eq(0),
                    e = b.height(),
                    f = b.position().top;
                c.elems.push({
                    list: b,
                    header: d,
                    listHeight: e,
                    headerText: d.text(),
                    headerHeight: d.outerHeight(),
                    listOffset: f,
                    listBottom: e + f
                })
            }), this.$fakeHeader.text(this.elems[0].headerText), this.$listWrapper.scroll(function () {
                c.testPosition()
            })
        },
        testPosition: function () {
            for (var b, c, d, e = this.$listWrapper.scrollTop(), f = 0; this.elems[f].listOffset - e <= 0 && (b = this.elems[f], d = b.listBottom - e, d < -b.headerHeight && (c = b), f++, !(f >= this.elems.length)););
            0 > d && d > -b.headerHeight ? (this.$fakeHeader.addClass(this.options.classes.hidden), a(b.list).addClass(this.options.classes.animated)) : (this.$fakeHeader.removeClass(this.options.classes.hidden), b && a(b.list).removeClass(this.options.classes.animated)), b && this.$fakeHeader.text(b.headerText)
        }
    }, a.fn.ioslist = function (c, d) {
        return this.each("string" == typeof c ? function () {
            a(this).data("instance")[c](d)
        } : function () {
            new b(this, c)
        })
    }
}(jQuery, window, document);