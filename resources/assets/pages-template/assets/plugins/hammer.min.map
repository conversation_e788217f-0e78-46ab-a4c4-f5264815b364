{"version": 3, "file": "hammer.min.js", "sources": ["hammer.js"], "names": ["window", "document", "exportName", "undefined", "setTimeoutContext", "fn", "timeout", "context", "setTimeout", "bindFn", "invokeArrayArg", "arg", "Array", "isArray", "each", "obj", "iterator", "i", "for<PERSON>ach", "length", "call", "hasOwnProperty", "extend", "dest", "src", "merge", "keys", "Object", "inherit", "child", "base", "properties", "childP", "baseP", "prototype", "create", "constructor", "_super", "apply", "arguments", "boolOrFn", "val", "args", "TYPE_FUNCTION", "ifUndefined", "val1", "val2", "addEventListeners", "target", "types", "handler", "splitStr", "type", "addEventListener", "removeEventListeners", "removeEventListener", "hasParent", "node", "parent", "parentNode", "inStr", "str", "find", "indexOf", "trim", "split", "inArray", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "toArray", "slice", "uniqueArray", "key", "sort", "results", "values", "push", "a", "b", "prefixed", "property", "prefix", "prop", "camelProp", "toUpperCase", "VENDOR_PREFIXES", "uniqueId", "_uniqueId", "getWindowForElement", "element", "doc", "ownerDocument", "defaultView", "parentWindow", "Input", "manager", "callback", "self", "this", "options", "inputTarget", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ev", "enable", "init", "createInputInstance", "Type", "inputClass", "SUPPORT_POINTER_EVENTS", "PointerEventInput", "SUPPORT_ONLY_TOUCH", "TouchInput", "SUPPORT_TOUCH", "TouchMouseInput", "MouseInput", "inputHandler", "eventType", "input", "pointersLen", "pointers", "changedPointersLen", "changedPointers", "<PERSON><PERSON><PERSON><PERSON>", "INPUT_START", "isFinal", "INPUT_END", "INPUT_CANCEL", "session", "computeInputData", "emit", "recognize", "prevInput", "pointers<PERSON><PERSON><PERSON>", "firstInput", "simpleCloneInputData", "firstMultiple", "offsetCenter", "center", "getCenter", "timeStamp", "now", "deltaTime", "angle", "getAngle", "distance", "getDistance", "computeDeltaXY", "offsetDirection", "getDirection", "deltaX", "deltaY", "scale", "getScale", "rotation", "getRotation", "computeIntervalInputData", "srcEvent", "offset", "offsetDelta", "prevDel<PERSON>", "x", "y", "velocity", "velocityX", "velocityY", "direction", "last", "lastInterval", "COMPUTE_INTERVAL", "v", "getVelocity", "abs", "clientX", "round", "clientY", "DIRECTION_NONE", "DIRECTION_LEFT", "DIRECTION_RIGHT", "DIRECTION_UP", "DIRECTION_DOWN", "p1", "p2", "props", "PROPS_XY", "Math", "sqrt", "atan2", "PI", "start", "end", "PROPS_CLIENT_XY", "evEl", "MOUSE_ELEMENT_EVENTS", "evWin", "MOUSE_WINDOW_EVENTS", "allow", "pressed", "POINTER_ELEMENT_EVENTS", "POINTER_WINDOW_EVENTS", "store", "pointerEvents", "SingleTouchInput", "ev<PERSON><PERSON><PERSON>", "SINGLE_TOUCH_TARGET_EVENTS", "SINGLE_TOUCH_WINDOW_EVENTS", "started", "normalizeSingleTouches", "all", "touches", "changed", "changedTouches", "concat", "TOUCH_TARGET_EVENTS", "targetIds", "getTouches", "allTouches", "INPUT_MOVE", "identifier", "targetTouches", "changedTargetTouches", "filter", "touch", "mouse", "TouchAction", "value", "set", "cleanTouchActions", "actions", "TOUCH_ACTION_NONE", "hasPanX", "TOUCH_ACTION_PAN_X", "hasPanY", "TOUCH_ACTION_PAN_Y", "TOUCH_ACTION_MANIPULATION", "TOUCH_ACTION_AUTO", "Recognizer", "id", "defaults", "state", "STATE_POSSIBLE", "simultaneous", "requireFail", "stateStr", "STATE_CANCELLED", "STATE_ENDED", "STATE_CHANGED", "STATE_BEGAN", "directionStr", "getRecognizerByNameIfManager", "otherRecognizer", "recognizer", "get", "AttrRecognizer", "PanRecognizer", "pX", "pY", "PinchRecognizer", "PressRecognizer", "_timer", "_input", "RotateRecognizer", "SwipeRecognizer", "TapRecognizer", "pTime", "pCenter", "count", "Hammer", "recognizers", "preset", "Manager", "handlers", "touchAction", "toggleCssProps", "item", "add", "recognizeWith", "requireFailure", "cssProps", "name", "style", "triggerDomEvent", "event", "data", "gestureEvent", "createEvent", "initEvent", "gesture", "dispatchEvent", "TEST_ELEMENT", "createElement", "Date", "MOBILE_REGEX", "test", "navigator", "userAgent", "INPUT_TYPE_TOUCH", "INPUT_TYPE_PEN", "INPUT_TYPE_MOUSE", "INPUT_TYPE_KINECT", "DIRECTION_HORIZONTAL", "DIRECTION_VERTICAL", "DIRECTION_ALL", "destroy", "MOUSE_INPUT_MAP", "mousedown", "mousemove", "mouseup", "button", "which", "pointerType", "POINTER_INPUT_MAP", "pointerdown", "pointermove", "pointerup", "pointercancel", "pointerout", "IE10_POINTER_TYPE_ENUM", 2, 3, 4, 5, "MSPointerEvent", "removePointer", "eventTypeNormalized", "toLowerCase", "replace", "is<PERSON><PERSON>ch", "storeIndex", "pointerId", "splice", "SINGLE_TOUCH_INPUT_MAP", "touchstart", "touchmove", "touchend", "touchcancel", "TOUCH_INPUT_MAP", "inputEvent", "inputData", "isMouse", "PREFIXED_TOUCH_ACTION", "NATIVE_TOUCH_ACTION", "TOUCH_ACTION_COMPUTE", "compute", "update", "getTouchAction", "join", "preventDefaults", "prevented", "preventDefault", "hasNone", "preventSrc", "STATE_RECOGNIZED", "STATE_FAILED", "dropRecognizeWith", "dropRequireFailure", "index", "hasRequireFailures", "canRecognizeWith", "withState", "tryEmit", "canEmit", "inputDataClone", "process", "reset", "attrTest", "optionPointers", "isRecognized", "<PERSON><PERSON><PERSON><PERSON>", "threshold", "directionTest", "hasMoved", "inOut", "time", "validPointers", "validMovement", "validTime", "clearTimeout", "taps", "interval", "pos<PERSON><PERSON><PERSON><PERSON>", "validTouchTime", "failTimeout", "validInterval", "validMultiTap", "tapCount", "VERSION", "domEvents", "userSelect", "touchSelect", "touchCallout", "contentZooming", "userDrag", "tapHighlightColor", "STOP", "FORCED_STOP", "stop", "force", "stopped", "cur<PERSON><PERSON><PERSON><PERSON><PERSON>", "existing", "remove", "on", "events", "off", "Tap", "Pan", "Swipe", "Pinch", "Rotate", "Press", "define", "amd", "module", "exports"], "mappings": "CAAA,SAAUA,EAAQC,EAAUC,EAAYC,GACtC,YAkBF,SAASC,GAAkBC,EAAIC,EAASC,GACpC,MAAOC,YAAWC,EAAOJ,EAAIE,GAAUD,GAY3C,QAASI,GAAeC,EAAKN,EAAIE,GAC7B,MAAIK,OAAMC,QAAQF,IACdG,EAAKH,EAAKJ,EAAQF,GAAKE,IAChB,IAEJ,EASX,QAASO,GAAKC,EAAKC,EAAUT,GACzB,GAAIU,EAEJ,IAAKF,EAIL,GAAIA,EAAIG,QACJH,EAAIG,QAAQF,EAAUT,OACnB,IAAIQ,EAAII,SAAWhB,EAEtB,IADAc,EAAI,EACGA,EAAIF,EAAII,QACXH,EAASI,KAAKb,EAASQ,EAAIE,GAAIA,EAAGF,GAClCE,QAGJ,KAAKA,IAAKF,GACNA,EAAIM,eAAeJ,IAAMD,EAASI,KAAKb,EAASQ,EAAIE,GAAIA,EAAGF,GAavE,QAASO,GAAOC,EAAMC,EAAKC,GAGvB,IAFA,GAAIC,GAAOC,OAAOD,KAAKF,GACnBP,EAAI,EACDA,EAAIS,EAAKP,UACPM,GAAUA,GAASF,EAAKG,EAAKT,MAAQd,KACtCoB,EAAKG,EAAKT,IAAMO,EAAIE,EAAKT,KAE7BA,GAEJ,OAAOM,GAUX,QAASE,GAAMF,EAAMC,GACjB,MAAOF,GAAOC,EAAMC,GAAK,GAS7B,QAASI,GAAQC,EAAOC,EAAMC,GAC1B,GACIC,GADAC,EAAQH,EAAKI,SAGjBF,GAASH,EAAMK,UAAYP,OAAOQ,OAAOF,GACzCD,EAAOI,YAAcP,EACrBG,EAAOK,OAASJ,EAEZF,GACAT,EAAOU,EAAQD,GAUvB,QAAStB,GAAOJ,EAAIE,GAChB,MAAO,YACH,MAAOF,GAAGiC,MAAM/B,EAASgC,YAWjC,QAASC,GAASC,EAAKC,GACnB,aAAWD,IAAOE,GACPF,EAAIH,MAAMI,EAAOA,EAAK,IAAMvC,EAAYA,EAAWuC,GAEvDD,EASX,QAASG,GAAYC,EAAMC,GACvB,MAAQD,KAAS1C,EAAa2C,EAAOD,EASzC,QAASE,GAAkBC,EAAQC,EAAOC,GACtCpC,EAAKqC,EAASF,GAAQ,SAASG,GAC3BJ,EAAOK,iBAAiBD,EAAMF,GAAS,KAU/C,QAASI,GAAqBN,EAAQC,EAAOC,GACzCpC,EAAKqC,EAASF,GAAQ,SAASG,GAC3BJ,EAAOO,oBAAoBH,EAAMF,GAAS,KAWlD,QAASM,GAAUC,EAAMC,GACrB,KAAOD,GAAM,CACT,GAAIA,GAAQC,EACR,OAAO,CAEXD,GAAOA,EAAKE,WAEhB,OAAO,EASX,QAASC,GAAMC,EAAKC,GAChB,MAAOD,GAAIE,QAAQD,GAAQ,GAQ/B,QAASX,GAASU,GACd,MAAOA,GAAIG,OAAOC,MAAM,QAU5B,QAASC,GAAQ1C,EAAKsC,EAAMK,GACxB,GAAI3C,EAAIuC,UAAYI,EAChB,MAAO3C,GAAIuC,QAAQD,EAGnB,KADA,GAAI7C,GAAI,EACDA,EAAIO,EAAIL,QAAQ,CACnB,GAAKgD,GAAa3C,EAAIP,GAAGkD,IAAcL,IAAWK,GAAa3C,EAAIP,KAAO6C,EACtE,MAAO7C,EAEXA,KAEJ,MAAO,GASf,QAASmD,GAAQrD,GACb,MAAOH,OAAMsB,UAAUmC,MAAMjD,KAAKL,EAAK,GAU3C,QAASuD,GAAY9C,EAAK+C,EAAKC,GAK3B,IAJA,GAAIC,MACAC,KACAzD,EAAI,EAEDA,EAAIO,EAAIL,QAAQ,CACnB,GAAIsB,GAAM8B,EAAM/C,EAAIP,GAAGsD,GAAO/C,EAAIP,EAC9BiD,GAAQQ,EAAQjC,GAAO,GACvBgC,EAAQE,KAAKnD,EAAIP,IAErByD,EAAOzD,GAAKwB,EACZxB,IAaJ,MAVIuD,KAIIC,EAHCF,EAGSE,EAAQD,KAAK,SAAyBI,EAAGC,GAC/C,MAAOD,GAAEL,GAAOM,EAAEN,KAHZE,EAAQD,QAQnBC,EASX,QAASK,GAAS/D,EAAKgE,GAKnB,IAJA,GAAIC,GAAQC,EACRC,EAAYH,EAAS,GAAGI,cAAgBJ,EAASV,MAAM,GAEvDpD,EAAI,EACDA,EAAImE,GAAgBjE,QAAQ,CAI/B,GAHA6D,EAASI,GAAgBnE,GACzBgE,EAAO,EAAWD,EAASE,EAAYH,EAEnCE,IAAQlE,GACR,MAAOkE,EAEXhE,KAEJ,MAAOd,GAQX,QAASkF,KACL,MAAOC,MAQX,QAASC,GAAoBC,GACzB,GAAIC,GAAMD,EAAQE,aAClB,OAAQD,GAAIE,aAAeF,EAAIG,aAyCnC,QAASC,GAAMC,EAASC,GACpB,GAAIC,GAAOC,IACXA,MAAKH,QAAUA,EACfG,KAAKF,SAAWA,EAChBE,KAAKT,QAAUM,EAAQN,QACvBS,KAAKjD,OAAS8C,EAAQI,QAAQC,YAI9BF,KAAKG,WAAa,SAASC,GACnB7D,EAASsD,EAAQI,QAAQI,QAASR,KAClCE,EAAK9C,QAAQmD,IAIrBJ,KAAKM,OAoCT,QAASC,GAAoBV,GACzB,GAAIW,GACAC,EAAaZ,EAAQI,QAAQQ,UAajC,OAAO,KAVHD,EADAC,EACOA,EACAC,GACAC,EACAC,GACAC,EACCC,GAGDC,EAFAC,GAIOnB,EAASoB,GAS/B,QAASA,GAAapB,EAASqB,EAAWC,GACtC,GAAIC,GAAcD,EAAME,SAASnG,OAC7BoG,EAAqBH,EAAMI,gBAAgBrG,OAC3CsG,EAAWN,EAAYO,IAAgBL,EAAcE,IAAuB,EAC5EI,EAAWR,GAAaS,GAAYC,KAAkBR,EAAcE,IAAuB,CAE/FH,GAAMK,UAAYA,EAClBL,EAAMO,UAAYA,EAEdF,IACA3B,EAAQgC,YAKZV,EAAMD,UAAYA,EAGlBY,EAAiBjC,EAASsB,GAG1BtB,EAAQkC,KAAK,eAAgBZ,GAE7BtB,EAAQmC,UAAUb,GAClBtB,EAAQgC,QAAQI,UAAYd,EAQhC,QAASW,GAAiBjC,EAASsB,GAC/B,GAAIU,GAAUhC,EAAQgC,QAClBR,EAAWF,EAAME,SACjBa,EAAiBb,EAASnG,MAGzB2G,GAAQM,aACTN,EAAQM,WAAaC,EAAqBjB,IAI1Ce,EAAiB,IAAML,EAAQQ,cAC/BR,EAAQQ,cAAgBD,EAAqBjB,GACnB,IAAnBe,IACPL,EAAQQ,eAAgB,EAG5B,IAAIF,GAAaN,EAAQM,WACrBE,EAAgBR,EAAQQ,cACxBC,EAAeD,EAAgBA,EAAcE,OAASJ,EAAWI,OAEjEA,EAASpB,EAAMoB,OAASC,EAAUnB,EACtCF,GAAMsB,UAAYC,KAClBvB,EAAMwB,UAAYxB,EAAMsB,UAAYN,EAAWM,UAE/CtB,EAAMyB,MAAQC,EAASP,EAAcC,GACrCpB,EAAM2B,SAAWC,EAAYT,EAAcC,GAE3CS,EAAenB,EAASV,GACxBA,EAAM8B,gBAAkBC,EAAa/B,EAAMgC,OAAQhC,EAAMiC,QAEzDjC,EAAMkC,MAAQhB,EAAgBiB,EAASjB,EAAchB,SAAUA,GAAY,EAC3EF,EAAMoC,SAAWlB,EAAgBmB,EAAYnB,EAAchB,SAAUA,GAAY,EAEjFoC,EAAyB5B,EAASV,EAGlC,IAAIpE,GAAS8C,EAAQN,OACjBhC,GAAU4D,EAAMuC,SAAS3G,OAAQA,KACjCA,EAASoE,EAAMuC,SAAS3G,QAE5BoE,EAAMpE,OAASA,EAGnB,QAASiG,GAAenB,EAASV,GAC7B,GAAIoB,GAASpB,EAAMoB,OACfoB,EAAS9B,EAAQ+B,gBACjBC,EAAYhC,EAAQgC,cACpB5B,EAAYJ,EAAQI,eAEpBd,EAAMD,YAAcO,IAAeQ,EAAUf,YAAcS,MAC3DkC,EAAYhC,EAAQgC,WAChBC,EAAG7B,EAAUkB,QAAU,EACvBY,EAAG9B,EAAUmB,QAAU,GAG3BO,EAAS9B,EAAQ+B,aACbE,EAAGvB,EAAOuB,EACVC,EAAGxB,EAAOwB,IAIlB5C,EAAMgC,OAASU,EAAUC,GAAKvB,EAAOuB,EAAIH,EAAOG,GAChD3C,EAAMiC,OAASS,EAAUE,GAAKxB,EAAOwB,EAAIJ,EAAOI,GAQpD,QAASN,GAAyB5B,EAASV,GACvC,GAEI6C,GAAUC,EAAWC,EAAWC,EAFhCC,EAAOvC,EAAQwC,cAAgBlD,EAC/BwB,EAAYxB,EAAMsB,UAAY2B,EAAK3B,SAGvC,IAAItB,EAAMD,WAAaU,KAAiBe,EAAY2B,IAAoBF,EAAKJ,WAAa9J,GAAY,CAClG,GAAIiJ,GAASiB,EAAKjB,OAAShC,EAAMgC,OAC7BC,EAASgB,EAAKhB,OAASjC,EAAMiC,OAE7BmB,EAAIC,EAAY7B,EAAWQ,EAAQC,EACvCa,GAAYM,EAAET,EACdI,EAAYK,EAAER,EACdC,EAAYS,GAAIF,EAAET,GAAKW,GAAIF,EAAER,GAAMQ,EAAET,EAAIS,EAAER,EAC3CI,EAAYjB,EAAaC,EAAQC,GAEjCvB,EAAQwC,aAAelD,MAGvB6C,GAAWI,EAAKJ,SAChBC,EAAYG,EAAKH,UACjBC,EAAYE,EAAKF,UACjBC,EAAYC,EAAKD,SAGrBhD,GAAM6C,SAAWA,EACjB7C,EAAM8C,UAAYA,EAClB9C,EAAM+C,UAAYA,EAClB/C,EAAMgD,UAAYA,EAQtB,QAAS/B,GAAqBjB,GAK1B,IAFA,GAAIE,MACArG,EAAI,EACDA,EAAImG,EAAME,SAASnG,QACtBmG,EAASrG,IACL0J,QAASC,GAAMxD,EAAME,SAASrG,GAAG0J,SACjCE,QAASD,GAAMxD,EAAME,SAASrG,GAAG4J,UAErC5J,GAGJ,QACIyH,UAAWC,KACXrB,SAAUA,EACVkB,OAAQC,EAAUnB,GAClB8B,OAAQhC,EAAMgC,OACdC,OAAQjC,EAAMiC,QAStB,QAASZ,GAAUnB,GACf,GAAIa,GAAiBb,EAASnG,MAG9B,IAAuB,IAAnBgH,EACA,OACI4B,EAAGa,GAAMtD,EAAS,GAAGqD,SACrBX,EAAGY,GAAMtD,EAAS,GAAGuD,SAK7B,KADA,GAAId,GAAI,EAAGC,EAAI,EAAG/I,EAAI,EACXkH,EAAJlH,GACH8I,GAAKzC,EAASrG,GAAG0J,QACjBX,GAAK1C,EAASrG,GAAG4J,QACjB5J,GAGJ,QACI8I,EAAGa,GAAMb,EAAI5B,GACb6B,EAAGY,GAAMZ,EAAI7B,IAWrB,QAASsC,GAAY7B,EAAWmB,EAAGC,GAC/B,OACID,EAAGA,EAAInB,GAAa,EACpBoB,EAAGA,EAAIpB,GAAa,GAU5B,QAASO,GAAaY,EAAGC,GACrB,MAAID,KAAMC,EACCc,GAGPJ,GAAIX,IAAMW,GAAIV,GACPD,EAAI,EAAIgB,GAAiBC,GAE7BhB,EAAI,EAAIiB,GAAeC,GAUlC,QAASlC,GAAYmC,EAAIC,EAAIC,GACpBA,IACDA,EAAQC,GAEZ,IAAIvB,GAAIqB,EAAGC,EAAM,IAAMF,EAAGE,EAAM,IAC5BrB,EAAIoB,EAAGC,EAAM,IAAMF,EAAGE,EAAM,GAEhC,OAAOE,MAAKC,KAAMzB,EAAIA,EAAMC,EAAIA,GAUpC,QAASlB,GAASqC,EAAIC,EAAIC,GACjBA,IACDA,EAAQC,GAEZ,IAAIvB,GAAIqB,EAAGC,EAAM,IAAMF,EAAGE,EAAM,IAC5BrB,EAAIoB,EAAGC,EAAM,IAAMF,EAAGE,EAAM,GAChC,OAA0B,KAAnBE,KAAKE,MAAMzB,EAAGD,GAAWwB,KAAKG,GASzC,QAASjC,GAAYkC,EAAOC,GACxB,MAAO9C,GAAS8C,EAAI,GAAIA,EAAI,GAAIC,IAAmB/C,EAAS6C,EAAM,GAAIA,EAAM,GAAIE,IAUpF,QAAStC,GAASoC,EAAOC,GACrB,MAAO5C,GAAY4C,EAAI,GAAIA,EAAI,GAAIC,IAAmB7C,EAAY2C,EAAM,GAAIA,EAAM,GAAIE,IAiB1F,QAAS5E,KACLhB,KAAK6F,KAAOC,GACZ9F,KAAK+F,MAAQC,GAEbhG,KAAKiG,OAAQ,EACbjG,KAAKkG,SAAU,EAEftG,EAAMvD,MAAM2D,KAAM1D,WAoEtB,QAASqE,KACLX,KAAK6F,KAAOM,GACZnG,KAAK+F,MAAQK,GAEbxG,EAAMvD,MAAM2D,KAAM1D,WAElB0D,KAAKqG,MAASrG,KAAKH,QAAQgC,QAAQyE,iBAoEvC,QAASC,KACLvG,KAAKwG,SAAWC,GAChBzG,KAAK+F,MAAQW,GACb1G,KAAK2G,SAAU,EAEf/G,EAAMvD,MAAM2D,KAAM1D,WAsCtB,QAASsK,GAAuBxG,EAAIjD,GAChC,GAAI0J,GAAM1I,EAAQiC,EAAG0G,SACjBC,EAAU5I,EAAQiC,EAAG4G,eAMzB,OAJI7J,IAAQwE,GAAYC,MACpBiF,EAAMxI,EAAYwI,EAAII,OAAOF,GAAU,cAAc,KAGjDF,EAAKE,GAiBjB,QAASlG,KACLb,KAAKwG,SAAWU,GAChBlH,KAAKmH,aAELvH,EAAMvD,MAAM2D,KAAM1D,WA0BtB,QAAS8K,GAAWhH,EAAIjD,GACpB,GAAIkK,GAAalJ,EAAQiC,EAAG0G,SACxBK,EAAYnH,KAAKmH,SAGrB,IAAIhK,GAAQsE,GAAc6F,KAAqC,IAAtBD,EAAWnM,OAEhD,MADAiM,GAAUE,EAAW,GAAGE,aAAc,GAC9BF,EAAYA,EAGxB,IAAIrM,GACAwM,EACAR,EAAiB7I,EAAQiC,EAAG4G,gBAC5BS,KACA1K,EAASiD,KAAKjD,MAQlB,IALAyK,EAAgBH,EAAWK,OAAO,SAASC,GACvC,MAAOpK,GAAUoK,EAAM5K,OAAQA,KAI/BI,IAASsE,GAET,IADAzG,EAAI,EACGA,EAAIwM,EAActM,QACrBiM,EAAUK,EAAcxM,GAAGuM,aAAc,EACzCvM,GAMR,KADAA,EAAI,EACGA,EAAIgM,EAAe9L,QAClBiM,EAAUH,EAAehM,GAAGuM,aAC5BE,EAAqB/I,KAAKsI,EAAehM,IAIzCmC,GAAQwE,GAAYC,WACbuF,GAAUH,EAAehM,GAAGuM,YAEvCvM,GAGJ,OAAKyM,GAAqBvM,QAMtBmD,EAAYmJ,EAAcP,OAAOQ,GAAuB,cAAc,GACtEA,GAPJ,OAoBJ,QAAS1G,KACLnB,EAAMvD,MAAM2D,KAAM1D,UAElB,IAAIW,GAAUzC,EAAOwF,KAAK/C,QAAS+C,KACnCA,MAAK2H,MAAQ,GAAI9G,GAAWb,KAAKH,QAAS5C,GAC1C+C,KAAK4H,MAAQ,GAAI5G,GAAWhB,KAAKH,QAAS5C,GAyD9C,QAAS4K,GAAYhI,EAASiI,GAC1B9H,KAAKH,QAAUA,EACfG,KAAK+H,IAAID,GAuFb,QAASE,GAAkBC,GAEvB,GAAItK,EAAMsK,EAASC,IACf,MAAOA,GAGX,IAAIC,GAAUxK,EAAMsK,EAASG,IACzBC,EAAU1K,EAAMsK,EAASK,GAG7B,OAAIH,IAAWE,EACJD,GAAqB,IAAME,GAIlCH,GAAWE,EACJF,EAAUC,GAAqBE,GAItC3K,EAAMsK,EAASM,IACRA,GAGJC,GA4CX,QAASC,GAAWxI,GAChBD,KAAK0I,GAAKtJ,IAEVY,KAAKH,QAAU,KACfG,KAAKC,QAAUzE,EAAMyE,MAAeD,KAAK2I,UAGzC3I,KAAKC,QAAQI,OAAS1D,EAAYqD,KAAKC,QAAQI,QAAQ,GAEvDL,KAAK4I,MAAQC,GAEb7I,KAAK8I,gBACL9I,KAAK+I,eAiOT,QAASC,GAASJ,GACd,MAAIA,GAAQK,GACD,SACAL,EAAQM,GACR,MACAN,EAAQO,GACR,OACAP,EAAQQ,GACR,QAEJ,GAQX,QAASC,GAAalF,GAClB,MAAIA,IAAac,GACN,OACAd,GAAaa,GACb,KACAb,GAAaW,GACb,OACAX,GAAaY,GACb,QAEJ,GASX,QAASuE,GAA6BC,EAAiBC,GACnD,GAAI3J,GAAU2J,EAAW3J,OACzB,OAAIA,GACOA,EAAQ4J,IAAIF,GAEhBA,EAQX,QAASG,KACLjB,EAAWpM,MAAM2D,KAAM1D,WA6D3B,QAASqN,KACLD,EAAerN,MAAM2D,KAAM1D,WAE3B0D,KAAK4J,GAAK,KACV5J,KAAK6J,GAAK,KA2Ed,QAASC,KACLJ,EAAerN,MAAM2D,KAAM1D,WAsC/B,QAASyN,MACLtB,EAAWpM,MAAM2D,KAAM1D,WAEvB0D,KAAKgK,OAAS,KACdhK,KAAKiK,OAAS,KAmElB,QAASC,MACLR,EAAerN,MAAM2D,KAAM1D,WA8B/B,QAAS6N,MACLT,EAAerN,MAAM2D,KAAM1D,WA0D/B,QAAS8N,MACL3B,EAAWpM,MAAM2D,KAAM1D,WAIvB0D,KAAKqK,OAAQ,EACbrK,KAAKsK,SAAU,EAEftK,KAAKgK,OAAS,KACdhK,KAAKiK,OAAS,KACdjK,KAAKuK,MAAQ,EAqGjB,QAASC,IAAOjL,EAASU,GAGrB,MAFAA,GAAUA,MACVA,EAAQwK,YAAc9N,EAAYsD,EAAQwK,YAAaD,GAAO7B,SAAS+B,QAChE,GAAIC,IAAQpL,EAASU,GAiIhC,QAAS0K,IAAQpL,EAASU,GACtBA,EAAUA,MAEVD,KAAKC,QAAUzE,EAAMyE,EAASuK,GAAO7B,UACrC3I,KAAKC,QAAQC,YAAcF,KAAKC,QAAQC,aAAeX,EAEvDS,KAAK4K,YACL5K,KAAK6B,WACL7B,KAAKyK,eAELzK,KAAKT,QAAUA,EACfS,KAAKmB,MAAQZ,EAAoBP,MACjCA,KAAK6K,YAAc,GAAIhD,GAAY7H,KAAMA,KAAKC,QAAQ4K,aAEtDC,GAAe9K,MAAM,GAErBnF,EAAKoF,EAAQwK,YAAa,SAASM,GAC/B,GAAIvB,GAAaxJ,KAAKgL,IAAI,GAAKD,GAAK,GAAIA,EAAK,IAC7CA,GAAK,IAAMvB,EAAWyB,cAAcF,EAAK,IACzCA,EAAK,IAAMvB,EAAW0B,eAAeH,EAAK,KAC3C/K,MAyOP,QAAS8K,IAAejL,EAASmL,GAC7B,GAAIzL,GAAUM,EAAQN,OACtB1E,GAAKgF,EAAQI,QAAQkL,SAAU,SAASrD,EAAOsD,GAC3C7L,EAAQ8L,MAAMxM,EAASU,EAAQ8L,MAAOD,IAASJ,EAAMlD,EAAQ,KASrE,QAASwD,IAAgBC,EAAOC,GAC5B,GAAIC,GAAezR,EAAS0R,YAAY,QACxCD,GAAaE,UAAUJ,GAAO,GAAM,GACpCE,EAAaG,QAAUJ,EACvBA,EAAKzO,OAAO8O,cAAcJ,GAr1E9B,GAAItM,KAAmB,GAAI,SAAU,MAAO,KAAM,KAAM,KACpD2M,GAAe9R,EAAS+R,cAAc,OAEtCrP,GAAgB,WAEhBiI,GAAQW,KAAKX,MACbF,GAAMa,KAAKb,IACX/B,GAAMsJ,KAAKtJ,IAwSXrD,GAAY,EAeZ4M,GAAe,wCAEfnL,GAAiB,gBAAkB/G,GACnC2G,GAAyB7B,EAAS9E,EAAQ,kBAAoBG,EAC9D0G,GAAqBE,IAAiBmL,GAAaC,KAAKC,UAAUC,WAElEC,GAAmB,QACnBC,GAAiB,MACjBC,GAAmB,QACnBC,GAAoB,SAEpBlI,GAAmB,GAEnB7C,GAAc,EACd6F,GAAa,EACb3F,GAAY,EACZC,GAAe,EAEfiD,GAAiB,EACjBC,GAAiB,EACjBC,GAAkB,EAClBC,GAAe,EACfC,GAAiB,GAEjBwH,GAAuB3H,GAAiBC,GACxC2H,GAAqB1H,GAAeC,GACpC0H,GAAgBF,GAAuBC,GAEvCrH,IAAY,IAAK,KACjBO,IAAmB,UAAW,UA4BlChG,GAAM3D,WAKFgB,QAAS,aAKTqD,KAAM,WACFN,KAAK6F,MAAQ/I,EAAkBkD,KAAKT,QAASS,KAAK6F,KAAM7F,KAAKG,YAC7DH,KAAKwG,UAAY1J,EAAkBkD,KAAKjD,OAAQiD,KAAKwG,SAAUxG,KAAKG,YACpEH,KAAK+F,OAASjJ,EAAkBwC,EAAoBU,KAAKT,SAAUS,KAAK+F,MAAO/F,KAAKG,aAMxFyM,QAAS,WACL5M,KAAK6F,MAAQxI,EAAqB2C,KAAKT,QAASS,KAAK6F,KAAM7F,KAAKG,YAChEH,KAAKwG,UAAYnJ,EAAqB2C,KAAKjD,OAAQiD,KAAKwG,SAAUxG,KAAKG,YACvEH,KAAK+F,OAAS1I,EAAqBiC,EAAoBU,KAAKT,SAAUS,KAAK+F,MAAO/F,KAAKG,aAoT/F,IAAI0M,KACAC,UAAWrL,GACXsL,UAAWzF,GACX0F,QAASrL,IAGTmE,GAAuB,YACvBE,GAAsB,mBAiB1BrK,GAAQqF,EAAYpB,GAKhB3C,QAAS,SAAmBmD,GACxB,GAAIc,GAAY2L,GAAgBzM,EAAGjD,KAG/B+D,GAAYO,IAA6B,IAAdrB,EAAG6M,SAC9BjN,KAAKkG,SAAU,GAGfhF,EAAYoG,IAA2B,IAAblH,EAAG8M,QAC7BhM,EAAYS,IAIX3B,KAAKkG,SAAYlG,KAAKiG,QAIvB/E,EAAYS,KACZ3B,KAAKkG,SAAU,GAGnBlG,KAAKF,SAASE,KAAKH,QAASqB,GACxBG,UAAWjB,GACXmB,iBAAkBnB,GAClB+M,YAAaZ,GACb7I,SAAUtD,OAKtB,IAAIgN,KACAC,YAAa5L,GACb6L,YAAahG,GACbiG,UAAW5L,GACX6L,cAAe5L,GACf6L,WAAY7L,IAIZ8L,IACAC,EAAGtB,GACHuB,EAAGtB,GACHuB,EAAGtB,GACHuB,EAAGtB,IAGHrG,GAAyB,cACzBC,GAAwB,qCAGxBrM,GAAOgU,iBACP5H,GAAyB,gBACzBC,GAAwB,6CAiB5BzK,EAAQgF,EAAmBf,GAKvB3C,QAAS,SAAmBmD,GACxB,GAAIiG,GAAQrG,KAAKqG,MACb2H,GAAgB,EAEhBC,EAAsB7N,EAAGjD,KAAK+Q,cAAcC,QAAQ,KAAM,IAC1DjN,EAAYkM,GAAkBa,GAC9Bd,EAAcO,GAAuBtN,EAAG+M,cAAgB/M,EAAG+M,YAE3DiB,EAAWjB,GAAed,GAG1BgC,EAAapQ,EAAQoI,EAAOjG,EAAGkO,UAAW,YAG1CpN,GAAYO,KAA8B,IAAdrB,EAAG6M,QAAgBmB,GAC9B,EAAbC,IACAhI,EAAM3H,KAAK0B,GACXiO,EAAahI,EAAMnL,OAAS,GAEzBgG,GAAaS,GAAYC,MAChCoM,GAAgB,GAIH,EAAbK,IAKJhI,EAAMgI,GAAcjO,EAEpBJ,KAAKF,SAASE,KAAKH,QAASqB,GACxBG,SAAUgF,EACV9E,iBAAkBnB,GAClB+M,YAAaA,EACbzJ,SAAUtD,IAGV4N,GAEA3H,EAAMkI,OAAOF,EAAY,MAKrC,IAAIG,KACAC,WAAYhN,GACZiN,UAAWpH,GACXqH,SAAUhN,GACViN,YAAahN,IAGb6E,GAA6B,aAC7BC,GAA6B,2CAejC/K,GAAQ4K,EAAkB3G,GACtB3C,QAAS,SAAmBmD,GACxB,GAAIjD,GAAOqR,GAAuBpO,EAAGjD,KAOrC,IAJIA,IAASsE,KACTzB,KAAK2G,SAAU,GAGd3G,KAAK2G,QAAV,CAIA,GAAIG,GAAUF,EAAuBzL,KAAK6E,KAAMI,EAAIjD,EAGhDA,IAAQwE,GAAYC,KAAiBkF,EAAQ,GAAG5L,OAAS4L,EAAQ,GAAG5L,SAAW,IAC/E8E,KAAK2G,SAAU,GAGnB3G,KAAKF,SAASE,KAAKH,QAAS1C,GACxBkE,SAAUyF,EAAQ,GAClBvF,gBAAiBuF,EAAQ,GACzBqG,YAAad,GACb3I,SAAUtD,OAsBtB,IAAIyO,KACAJ,WAAYhN,GACZiN,UAAWpH,GACXqH,SAAUhN,GACViN,YAAahN,IAGbsF,GAAsB,2CAc1BvL,GAAQkF,EAAYjB,GAChB3C,QAAS,SAAoBmD,GACzB,GAAIjD,GAAO0R,GAAgBzO,EAAGjD,MAC1B2J,EAAUM,EAAWjM,KAAK6E,KAAMI,EAAIjD,EACnC2J,IAIL9G,KAAKF,SAASE,KAAKH,QAAS1C,GACxBkE,SAAUyF,EAAQ,GAClBvF,gBAAiBuF,EAAQ,GACzBqG,YAAad,GACb3I,SAAUtD,OAmFtBzE,EAAQoF,EAAiBnB,GAOrB3C,QAAS,SAAoB4C,EAASiP,EAAYC,GAC9C,GAAIX,GAAWW,EAAU5B,aAAed,GACpC2C,EAAWD,EAAU5B,aAAeZ,EAIxC,IAAI6B,EACApO,KAAK4H,MAAM3B,OAAQ,MAChB,IAAI+I,IAAYhP,KAAK4H,MAAM3B,MAC9B,MAIA6I,IAAcnN,GAAYC,MAC1B5B,KAAK4H,MAAM3B,OAAQ,GAGvBjG,KAAKF,SAASD,EAASiP,EAAYC,IAMvCnC,QAAS,WACL5M,KAAK2H,MAAMiF,UACX5M,KAAK4H,MAAMgF,YAInB,IAAIqC,IAAwBpQ,EAASiN,GAAaT,MAAO,eACrD6D,GAAsBD,KAA0B/U,EAGhDiV,GAAuB,UACvB3G,GAAoB,OACpBD,GAA4B,eAC5BL,GAAoB,OACpBE,GAAqB,QACrBE,GAAqB,OAczBT,GAAY5L,WAKR8L,IAAK,SAASD,GAENA,GAASqH,KACTrH,EAAQ9H,KAAKoP,WAGbF,KACAlP,KAAKH,QAAQN,QAAQ8L,MAAM4D,IAAyBnH,GAExD9H,KAAKiI,QAAUH,EAAMoG,cAAcnQ,QAMvCsR,OAAQ,WACJrP,KAAK+H,IAAI/H,KAAKH,QAAQI,QAAQ4K,cAOlCuE,QAAS,WACL,GAAInH,KAMJ,OALApN,GAAKmF,KAAKH,QAAQ4K,YAAa,SAASjB,GAChCjN,EAASiN,EAAWvJ,QAAQI,QAASmJ,MACrCvB,EAAUA,EAAQhB,OAAOuC,EAAW8F,qBAGrCtH,EAAkBC,EAAQsH,KAAK,OAO1CC,gBAAiB,SAASrO,GAEtB,IAAI+N,GAAJ,CAIA,GAAIxL,GAAWvC,EAAMuC,SACjBS,EAAYhD,EAAM8B,eAGtB,IAAIjD,KAAKH,QAAQgC,QAAQ4N,UAErB,WADA/L,GAASgM,gBAIb,IAAIzH,GAAUjI,KAAKiI,QACf0H,EAAUhS,EAAMsK,EAASC,IACzBG,EAAU1K,EAAMsK,EAASK,IACzBH,EAAUxK,EAAMsK,EAASG,GAE7B,OAAIuH,IACCtH,GAAWlE,EAAYsI,IACvBtE,GAAWhE,EAAYuI,GACjB1M,KAAK4P,WAAWlM,GAH3B,SAWJkM,WAAY,SAASlM,GACjB1D,KAAKH,QAAQgC,QAAQ4N,WAAY,EACjC/L,EAASgM,kBA+DjB,IAAI7G,IAAiB,EACjBO,GAAc,EACdD,GAAgB,EAChBD,GAAc,EACd2G,GAAmB3G,GACnBD,GAAkB,GAClB6G,GAAe,EAuBnBrH,GAAWxM,WAKP0M,YAOAZ,IAAK,SAAS9H,GAKV,MAJA5E,GAAO2E,KAAKC,QAASA,GAGrBD,KAAKH,SAAWG,KAAKH,QAAQgL,YAAYwE,SAClCrP,MAQXiL,cAAe,SAAS1B,GACpB,GAAI9O,EAAe8O,EAAiB,gBAAiBvJ,MACjD,MAAOA,KAGX,IAAI8I,GAAe9I,KAAK8I,YAMxB,OALAS,GAAkBD,EAA6BC,EAAiBvJ,MAC3D8I,EAAaS,EAAgBb,MAC9BI,EAAaS,EAAgBb,IAAMa,EACnCA,EAAgB0B,cAAcjL,OAE3BA,MAQX+P,kBAAmB,SAASxG,GACxB,MAAI9O,GAAe8O,EAAiB,oBAAqBvJ,MAC9CA,MAGXuJ,EAAkBD,EAA6BC,EAAiBvJ,YACzDA,MAAK8I,aAAaS,EAAgBb,IAClC1I,OAQXkL,eAAgB,SAAS3B,GACrB,GAAI9O,EAAe8O,EAAiB,iBAAkBvJ,MAClD,MAAOA,KAGX,IAAI+I,GAAc/I,KAAK+I,WAMvB,OALAQ,GAAkBD,EAA6BC,EAAiBvJ,MAClB,KAA1C/B,EAAQ8K,EAAaQ,KACrBR,EAAYrK,KAAK6K,GACjBA,EAAgB2B,eAAelL,OAE5BA,MAQXgQ,mBAAoB,SAASzG,GACzB,GAAI9O,EAAe8O,EAAiB,qBAAsBvJ,MACtD,MAAOA,KAGXuJ,GAAkBD,EAA6BC,EAAiBvJ,KAChE,IAAIiQ,GAAQhS,EAAQ+B,KAAK+I,YAAaQ,EAItC,OAHI0G,GAAQ,IACRjQ,KAAK+I,YAAYwF,OAAO0B,EAAO,GAE5BjQ,MAOXkQ,mBAAoB,WAChB,MAAOlQ,MAAK+I,YAAY7N,OAAS,GAQrCiV,iBAAkB,SAAS5G,GACvB,QAASvJ,KAAK8I,aAAaS,EAAgBb,KAQ/C3G,KAAM,SAASZ,GAIX,QAASY,GAAKqO,GACVrQ,EAAKF,QAAQkC,KAAKhC,EAAKE,QAAQsL,OAAS6E,EAAYpH,EAASJ,GAAS,IAAKzH,GAJ/E,GAAIpB,GAAOC,KACP4I,EAAQ5I,KAAK4I,KAOLM,IAARN,GACA7G,GAAK,GAGTA,IAGI6G,GAASM,IACTnH,GAAK,IAUbsO,QAAS,SAASlP,GACd,MAAInB,MAAKsQ,UACEtQ,KAAK+B,KAAKZ,QAGrBnB,KAAK4I,MAAQkH,KAOjBQ,QAAS,WAEL,IADA,GAAItV,GAAI,EACDA,EAAIgF,KAAK+I,YAAY7N,QAAQ,CAChC,KAAM8E,KAAK+I,YAAY/N,GAAG4N,OAASkH,GAAejH,KAC9C,OAAO,CAEX7N,KAEJ,OAAO,GAOXgH,UAAW,SAAS+M,GAGhB,GAAIwB,GAAiBlV,KAAW0T,EAGhC,OAAKxS,GAASyD,KAAKC,QAAQI,QAASL,KAAMuQ,KAOtCvQ,KAAK4I,OAASiH,GAAmB5G,GAAkB6G,MACnD9P,KAAK4I,MAAQC,IAGjB7I,KAAK4I,MAAQ5I,KAAKwQ,QAAQD,QAItBvQ,KAAK4I,OAASQ,GAAcD,GAAgBD,GAAcD,KAC1DjJ,KAAKqQ,QAAQE,MAfbvQ,KAAKyQ,aACLzQ,KAAK4I,MAAQkH,MAyBrBU,QAAS,aAOTlB,eAAgB,aAOhBmB,MAAO,cA8DX9U,EAAQ+N,EAAgBjB,GAKpBE,UAKItH,SAAU,GASdqP,SAAU,SAASvP,GACf,GAAIwP,GAAiB3Q,KAAKC,QAAQoB,QAClC,OAA0B,KAAnBsP,GAAwBxP,EAAME,SAASnG,SAAWyV,GAS7DH,QAAS,SAASrP,GACd,GAAIyH,GAAQ5I,KAAK4I,MACb1H,EAAYC,EAAMD,UAElB0P,EAAehI,GAASQ,GAAcD,IACtC0H,EAAU7Q,KAAK0Q,SAASvP,EAG5B,OAAIyP,KAAiB1P,EAAYU,KAAiBiP,GACvCjI,EAAQK,GACR2H,GAAgBC,EACnB3P,EAAYS,GACLiH,EAAQM,GACNN,EAAQQ,GAGdR,EAAQO,GAFJC,GAIR0G,MAiBfnU,EAAQgO,EAAeD,GAKnBf,UACI4C,MAAO,MACPuF,UAAW,GACXzP,SAAU,EACV8C,UAAWwI,IAGf2C,eAAgB,WACZ,GAAInL,GAAYnE,KAAKC,QAAQkE,UACzB8D,IAOJ,OANI9D,GAAYsI,IACZxE,EAAQvJ,KAAK4J,IAEbnE,EAAYuI,IACZzE,EAAQvJ,KAAK0J,IAEVH,GAGX8I,cAAe,SAAS5P,GACpB,GAAIlB,GAAUD,KAAKC,QACf+Q,GAAW,EACXlO,EAAW3B,EAAM2B,SACjBqB,EAAYhD,EAAMgD,UAClBL,EAAI3C,EAAMgC,OACVY,EAAI5C,EAAMiC,MAed,OAZMe,GAAYlE,EAAQkE,YAClBlE,EAAQkE,UAAYsI,IACpBtI,EAAmB,IAANL,EAAWe,GAAsB,EAAJf,EAASgB,GAAiBC,GACpEiM,EAAWlN,GAAK9D,KAAK4J,GACrB9G,EAAWwC,KAAKb,IAAItD,EAAMgC,UAE1BgB,EAAmB,IAANJ,EAAWc,GAAsB,EAAJd,EAASiB,GAAeC,GAClE+L,EAAWjN,GAAK/D,KAAK6J,GACrB/G,EAAWwC,KAAKb,IAAItD,EAAMiC,UAGlCjC,EAAMgD,UAAYA,EACX6M,GAAYlO,EAAW7C,EAAQ6Q,WAAa3M,EAAYlE,EAAQkE,WAG3EuM,SAAU,SAASvP,GACf,MAAOuI,GAAezN,UAAUyU,SAASvV,KAAK6E,KAAMmB,KAC/CnB,KAAK4I,MAAQQ,MAAkBpJ,KAAK4I,MAAQQ,KAAgBpJ,KAAK+Q,cAAc5P,KAGxFY,KAAM,SAASZ,GACXnB,KAAK4J,GAAKzI,EAAMgC,OAChBnD,KAAK6J,GAAK1I,EAAMiC,MAEhB,IAAIe,GAAYkF,EAAalI,EAAMgD,UAC/BA,IACAnE,KAAKH,QAAQkC,KAAK/B,KAAKC,QAAQsL,MAAQpH,EAAWhD,GAGtDnB,KAAK5D,OAAO2F,KAAK5G,KAAK6E,KAAMmB,MAcpCxF,EAAQmO,EAAiBJ,GAKrBf,UACI4C,MAAO,QACPuF,UAAW,EACXzP,SAAU,GAGdiO,eAAgB,WACZ,OAAQpH,KAGZwI,SAAU,SAASvP,GACf,MAAOnB,MAAK5D,OAAOsU,SAASvV,KAAK6E,KAAMmB,KAClCmE,KAAKb,IAAItD,EAAMkC,MAAQ,GAAKrD,KAAKC,QAAQ6Q,WAAa9Q,KAAK4I,MAAQQ,KAG5ErH,KAAM,SAASZ,GAEX,GADAnB,KAAK5D,OAAO2F,KAAK5G,KAAK6E,KAAMmB,GACR,IAAhBA,EAAMkC,MAAa,CACnB,GAAI4N,GAAQ9P,EAAMkC,MAAQ,EAAI,KAAO,KACrCrD,MAAKH,QAAQkC,KAAK/B,KAAKC,QAAQsL,MAAQ0F,EAAO9P,OAkB1DxF,EAAQoO,GAAiBtB,GAKrBE,UACI4C,MAAO,QACPlK,SAAU,EACV6P,KAAM,IACNJ,UAAW,GAGfxB,eAAgB,WACZ,OAAQ9G,KAGZgI,QAAS,SAASrP,GACd,GAAIlB,GAAUD,KAAKC,QACfkR,EAAgBhQ,EAAME,SAASnG,SAAW+E,EAAQoB,SAClD+P,EAAgBjQ,EAAM2B,SAAW7C,EAAQ6Q,UACzCO,EAAYlQ,EAAMwB,UAAY1C,EAAQiR,IAM1C,IAJAlR,KAAKiK,OAAS9I,GAITiQ,IAAkBD,GAAkBhQ,EAAMD,WAAaS,GAAYC,MAAkByP,EACtFrR,KAAKyQ,YACF,IAAItP,EAAMD,UAAYO,GACzBzB,KAAKyQ,QACLzQ,KAAKgK,OAAS7P,EAAkB,WAC5B6F,KAAK4I,MAAQiH,GACb7P,KAAKqQ,WACNpQ,EAAQiR,KAAMlR,UACd,IAAImB,EAAMD,UAAYS,GACzB,MAAOkO,GAEX,OAAOC,KAGXW,MAAO,WACHa,aAAatR,KAAKgK,SAGtBjI,KAAM,SAASZ,GACPnB,KAAK4I,QAAUiH,KAIf1O,GAAUA,EAAMD,UAAYS,GAC5B3B,KAAKH,QAAQkC,KAAK/B,KAAKC,QAAQsL,MAAQ,KAAMpK,IAE7CnB,KAAKiK,OAAOxH,UAAYC,KACxB1C,KAAKH,QAAQkC,KAAK/B,KAAKC,QAAQsL,MAAOvL,KAAKiK,aAevDtO,EAAQuO,GAAkBR,GAKtBf,UACI4C,MAAO,SACPuF,UAAW,EACXzP,SAAU,GAGdiO,eAAgB,WACZ,OAAQpH,KAGZwI,SAAU,SAASvP,GACf,MAAOnB,MAAK5D,OAAOsU,SAASvV,KAAK6E,KAAMmB,KAClCmE,KAAKb,IAAItD,EAAMoC,UAAYvD,KAAKC,QAAQ6Q,WAAa9Q,KAAK4I,MAAQQ,OAc/EzN,EAAQwO,GAAiBT,GAKrBf,UACI4C,MAAO,QACPuF,UAAW,GACX9M,SAAU,IACVG,UAAWsI,GAAuBC,GAClCrL,SAAU,GAGdiO,eAAgB,WACZ,MAAO3F,GAAc1N,UAAUqT,eAAenU,KAAK6E,OAGvD0Q,SAAU,SAASvP,GACf,GACI6C,GADAG,EAAYnE,KAAKC,QAAQkE,SAW7B,OARIA,IAAasI,GAAuBC,IACpC1I,EAAW7C,EAAM6C,SACVG,EAAYsI,GACnBzI,EAAW7C,EAAM8C,UACVE,EAAYuI,KACnB1I,EAAW7C,EAAM+C,WAGdlE,KAAK5D,OAAOsU,SAASvV,KAAK6E,KAAMmB,IACnCgD,EAAYhD,EAAMgD,WAClBhD,EAAM2B,SAAW9C,KAAKC,QAAQ6Q,WAC9BrM,GAAIT,GAAYhE,KAAKC,QAAQ+D,UAAY7C,EAAMD,UAAYS,IAGnEI,KAAM,SAASZ,GACX,GAAIgD,GAAYkF,EAAalI,EAAMgD,UAC/BA,IACAnE,KAAKH,QAAQkC,KAAK/B,KAAKC,QAAQsL,MAAQpH,EAAWhD,GAGtDnB,KAAKH,QAAQkC,KAAK/B,KAAKC,QAAQsL,MAAOpK,MA2B9CxF,EAAQyO,GAAe3B,GAKnBE,UACI4C,MAAO,MACPlK,SAAU,EACVkQ,KAAM,EACNC,SAAU,IACVN,KAAM,IACNJ,UAAW,EACXW,aAAc,IAGlBnC,eAAgB,WACZ,OAAQ/G,KAGZiI,QAAS,SAASrP,GACd,GAAIlB,GAAUD,KAAKC,QAEfkR,EAAgBhQ,EAAME,SAASnG,SAAW+E,EAAQoB,SAClD+P,EAAgBjQ,EAAM2B,SAAW7C,EAAQ6Q,UACzCY,EAAiBvQ,EAAMwB,UAAY1C,EAAQiR,IAI/C,IAFAlR,KAAKyQ,QAEAtP,EAAMD,UAAYO,IAAgC,IAAfzB,KAAKuK,MACzC,MAAOvK,MAAK2R,aAKhB,IAAIP,GAAiBM,GAAkBP,EAAe,CAClD,GAAIhQ,EAAMD,WAAaS,GACnB,MAAO3B,MAAK2R,aAGhB,IAAIC,GAAgB5R,KAAKqK,MAASlJ,EAAMsB,UAAYzC,KAAKqK,MAAQpK,EAAQuR,UAAY,EACjFK,GAAiB7R,KAAKsK,SAAWvH,EAAY/C,KAAKsK,QAASnJ,EAAMoB,QAAUtC,EAAQwR,YAEvFzR,MAAKqK,MAAQlJ,EAAMsB,UACnBzC,KAAKsK,QAAUnJ,EAAMoB,OAEhBsP,GAAkBD,EAGnB5R,KAAKuK,OAAS,EAFdvK,KAAKuK,MAAQ,EAKjBvK,KAAKiK,OAAS9I,CAId,IAAI2Q,GAAW9R,KAAKuK,MAAQtK,EAAQsR,IACpC,IAAiB,IAAbO,EAGA,MAAK9R,MAAKkQ,sBAGNlQ,KAAKgK,OAAS7P,EAAkB,WAC5B6F,KAAK4I,MAAQiH,GACb7P,KAAKqQ,WACNpQ,EAAQuR,SAAUxR,MACdoJ,IANAyG,GAUnB,MAAOC,KAGX6B,YAAa,WAIT,MAHA3R,MAAKgK,OAAS7P,EAAkB,WAC5B6F,KAAK4I,MAAQkH,IACd9P,KAAKC,QAAQuR,SAAUxR,MACnB8P,IAGXW,MAAO,WACHa,aAAatR,KAAKgK,SAGtBjI,KAAM,WACE/B,KAAK4I,OAASiH,KACd7P,KAAKiK,OAAO6H,SAAW9R,KAAKuK,MAC5BvK,KAAKH,QAAQkC,KAAK/B,KAAKC,QAAQsL,MAAOvL,KAAKiK,YAoBvDO,GAAOuH,QAAU,QAMjBvH,GAAO7B,UAOHqJ,WAAW,EAQXnH,YAAasE,GAMb9O,QAAQ,EASRH,YAAa,KAObO,WAAY,KAOZiK,SAEKR,IAAoB7J,QAAQ,KAC5ByJ,GAAmBzJ,QAAQ,IAAU,YACrC8J,IAAkBhG,UAAWsI,MAC7B9C,GAAiBxF,UAAWsI,KAAyB,WACrDrC,KACAA,IAAiBmB,MAAO,YAAagG,KAAM,IAAM,SACjDxH,KAQLoB,UAMI8G,WAAY,OAOZC,YAAa,OASbC,aAAc,OAOdC,eAAgB,OAOhBC,SAAU,OAQVC,kBAAmB,iBAI3B,IAAIC,IAAO,EACPC,GAAc,CA+BlB7H,IAAQ1O,WAMJ8L,IAAK,SAAS9H,GAaV,MAZA5E,GAAO2E,KAAKC,QAASA,GAGjBA,EAAQ4K,aACR7K,KAAK6K,YAAYwE,SAEjBpP,EAAQC,cAERF,KAAKmB,MAAMyL,UACX5M,KAAKmB,MAAMpE,OAASkD,EAAQC,YAC5BF,KAAKmB,MAAMb,QAERN,MASXyS,KAAM,SAASC,GACX1S,KAAK6B,QAAQ8Q,QAAUD,EAAQF,GAAcD,IASjDvQ,UAAW,SAAS+M,GAChB,GAAIlN,GAAU7B,KAAK6B,OACnB,KAAIA,EAAQ8Q,QAAZ,CAKA3S,KAAK6K,YAAY2E,gBAAgBT,EAEjC,IAAIvF,GACAiB,EAAczK,KAAKyK,YAKnBmI,EAAgB/Q,EAAQ+Q,gBAIvBA,GAAkBA,GAAiBA,EAAchK,MAAQiH,MAC1D+C,EAAgB/Q,EAAQ+Q,cAAgB,KAI5C,KADA,GAAI5X,GAAI,EACDA,EAAIyP,EAAYvP,QACnBsO,EAAaiB,EAAYzP,GAQrB6G,EAAQ8Q,UAAYH,IACfI,GAAiBpJ,GAAcoJ,IAChCpJ,EAAW2G,iBAAiByC,GAGhCpJ,EAAWiH,QAFXjH,EAAWxH,UAAU+M,IAOpB6D,GAAiBpJ,EAAWZ,OAASQ,GAAcD,GAAgBD,MACpE0J,EAAgB/Q,EAAQ+Q,cAAgBpJ,GAE5CxO,MASRyO,IAAK,SAASD,GACV,GAAIA,YAAsBf,GACtB,MAAOe,EAIX,KAAK,GADDiB,GAAczK,KAAKyK,YACdzP,EAAI,EAAGA,EAAIyP,EAAYvP,OAAQF,IACpC,GAAIyP,EAAYzP,GAAGiF,QAAQsL,OAAS/B,EAChC,MAAOiB,GAAYzP,EAG3B,OAAO,OASXgQ,IAAK,SAASxB,GACV,GAAI/O,EAAe+O,EAAY,MAAOxJ,MAClC,MAAOA,KAIX,IAAI6S,GAAW7S,KAAKyJ,IAAID,EAAWvJ,QAAQsL,MAS3C,OARIsH,IACA7S,KAAK8S,OAAOD,GAGhB7S,KAAKyK,YAAY/L,KAAK8K,GACtBA,EAAW3J,QAAUG,KAErBA,KAAK6K,YAAYwE,SACV7F,GAQXsJ,OAAQ,SAAStJ,GACb,GAAI/O,EAAe+O,EAAY,SAAUxJ,MACrC,MAAOA,KAGX,IAAIyK,GAAczK,KAAKyK,WAKvB,OAJAjB,GAAaxJ,KAAKyJ,IAAID,GACtBiB,EAAY8D,OAAOtQ,EAAQwM,EAAajB,GAAa,GAErDxJ,KAAK6K,YAAYwE,SACVrP,MASX+S,GAAI,SAASC,EAAQ/V,GACjB,GAAI2N,GAAW5K,KAAK4K,QAKpB,OAJA/P,GAAKqC,EAAS8V,GAAS,SAASzH,GAC5BX,EAASW,GAASX,EAASW,OAC3BX,EAASW,GAAO7M,KAAKzB,KAElB+C,MASXiT,IAAK,SAASD,EAAQ/V,GAClB,GAAI2N,GAAW5K,KAAK4K,QAQpB,OAPA/P,GAAKqC,EAAS8V,GAAS,SAASzH,GACvBtO,EAGD2N,EAASW,GAAOgD,OAAOtQ,EAAQ2M,EAASW,GAAQtO,GAAU,SAFnD2N,GAASW,KAKjBvL,MAQX+B,KAAM,SAASwJ,EAAOC,GAEdxL,KAAKC,QAAQ+R,WACb1G,GAAgBC,EAAOC,EAI3B,IAAIZ,GAAW5K,KAAK4K,SAASW,IAAUvL,KAAK4K,SAASW,GAAOnN,OAC5D,IAAKwM,GAAaA,EAAS1P,OAA3B,CAIAsQ,EAAKrO,KAAOoO,EACZC,EAAKkE,eAAiB,WAClBlE,EAAK9H,SAASgM,iBAIlB,KADA,GAAI1U,GAAI,EACDA,EAAI4P,EAAS1P,QAChB0P,EAAS5P,GAAGwQ,GACZxQ,MAQR4R,QAAS,WACL5M,KAAKT,SAAWuL,GAAe9K,MAAM,GAErCA,KAAK4K,YACL5K,KAAK6B,WACL7B,KAAKmB,MAAMyL,UACX5M,KAAKT,QAAU,OA4BvBlE,EAAOmP,IACH/I,YAAaA,GACb6F,WAAYA,GACZ3F,UAAWA,GACXC,aAAcA,GAEdiH,eAAgBA,GAChBO,YAAaA,GACbD,cAAeA,GACfD,YAAaA,GACb2G,iBAAkBA,GAClB5G,gBAAiBA,GACjB6G,aAAcA,GAEdjL,eAAgBA,GAChBC,eAAgBA,GAChBC,gBAAiBA,GACjBC,aAAcA,GACdC,eAAgBA,GAChBwH,qBAAsBA,GACtBC,mBAAoBA,GACpBC,cAAeA,GAEfhC,QAASA,GACT/K,MAAOA,EACPiI,YAAaA,EAEbhH,WAAYA,EACZG,WAAYA,EACZL,kBAAmBA,EACnBI,gBAAiBA,EACjBwF,iBAAkBA,EAElBkC,WAAYA,EACZiB,eAAgBA,EAChBwJ,IAAK9I,GACL+I,IAAKxJ,EACLyJ,MAAOjJ,GACPkJ,MAAOvJ,EACPwJ,OAAQpJ,GACRqJ,MAAOxJ,GAEPgJ,GAAIjW,EACJmW,IAAK5V,EACLxC,KAAMA,EACNW,MAAOA,EACPH,OAAQA,EACRM,QAASA,EACTnB,OAAQA,EACRqE,SAAUA,UAGH2U,SAAU9W,IAAiB8W,OAAOC,IACzCD,OAAO,WACH,MAAOhJ,MAEa,mBAAVkJ,SAAyBA,OAAOC,QAC9CD,OAAOC,QAAUnJ,GAEjBzQ,EAAOE,GAAcuQ,IAGtBzQ,OAAQC,SAAU"}