/*
 Copyright (C) <PERSON> 2017
 Distributed under the MIT License (license terms are at http://opensource.org/licenses/MIT).
 */const isBrowser='undefined'!=typeof window&&'undefined'!=typeof window.document,longerTimeoutBrowsers=['Edge','Trident','Firefox'];let timeoutDuration=0;for(let e=0;e<longerTimeoutBrowsers.length;e+=1)if(isBrowser&&0<=navigator.userAgent.indexOf(longerTimeoutBrowsers[e])){timeoutDuration=1;break}function microtaskDebounce(e){let t=!1;return()=>{t||(t=!0,Promise.resolve().then(()=>{t=!1,e()}))}}function taskDebounce(e){let t=!1;return()=>{t||(t=!0,setTimeout(()=>{t=!1,e()},timeoutDuration))}}const supportsMicroTasks=isBrowser&&window.Promise;var debounce=supportsMicroTasks?microtaskDebounce:taskDebounce;function isFunction(e){return e&&'[object Function]'==={}.toString.call(e)}function getStyleComputedProperty(e,t){if(1!==e.nodeType)return[];const o=window.getComputedStyle(e,null);return t?o[t]:o}function getParentNode(e){return'HTML'===e.nodeName?e:e.parentNode||e.host}function getScrollParent(e){if(!e)return window.document.body;switch(e.nodeName){case'HTML':case'BODY':return e.ownerDocument.body;case'#document':return e.body;}const{overflow:t,overflowX:o,overflowY:n}=getStyleComputedProperty(e);return /(auto|scroll)/.test(t+n+o)?e:getScrollParent(getParentNode(e))}function getOffsetParent(e){const t=e&&e.offsetParent,o=t&&t.nodeName;return o&&'BODY'!==o&&'HTML'!==o?-1!==['TD','TABLE'].indexOf(t.nodeName)&&'static'===getStyleComputedProperty(t,'position')?getOffsetParent(t):t:e?e.ownerDocument.documentElement:window.document.documentElement}function isOffsetContainer(e){const{nodeName:t}=e;return'BODY'!==t&&('HTML'===t||getOffsetParent(e.firstElementChild)===e)}function getRoot(e){return null===e.parentNode?e:getRoot(e.parentNode)}function findCommonOffsetParent(e,t){if(!e||!e.nodeType||!t||!t.nodeType)return window.document.documentElement;const o=e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_FOLLOWING,n=o?e:t,i=o?t:e,r=document.createRange();r.setStart(n,0),r.setEnd(i,0);const{commonAncestorContainer:p}=r;if(e!==p&&t!==p||n.contains(i))return isOffsetContainer(p)?p:getOffsetParent(p);const d=getRoot(e);return d.host?findCommonOffsetParent(d.host,t):findCommonOffsetParent(e,getRoot(t).host)}function getScroll(e,t='top'){const o='top'===t?'scrollTop':'scrollLeft',n=e.nodeName;if('BODY'===n||'HTML'===n){const t=e.ownerDocument.documentElement,n=e.ownerDocument.scrollingElement||t;return n[o]}return e[o]}function includeScroll(e,t,o=!1){const n=getScroll(t,'top'),i=getScroll(t,'left'),r=o?-1:1;return e.top+=n*r,e.bottom+=n*r,e.left+=i*r,e.right+=i*r,e}function getBordersSize(e,t){const o='x'===t?'Left':'Top',n='Left'==o?'Right':'Bottom';return+e[`border${o}Width`].split('px')[0]+ +e[`border${n}Width`].split('px')[0]}let isIE10;var isIE10$1=function(){return void 0==isIE10&&(isIE10=-1!==navigator.appVersion.indexOf('MSIE 10')),isIE10};function getSize(e,t,o,n){return Math.max(t[`offset${e}`],t[`scroll${e}`],o[`client${e}`],o[`offset${e}`],o[`scroll${e}`],isIE10$1()?o[`offset${e}`]+n[`margin${'Height'===e?'Top':'Left'}`]+n[`margin${'Height'===e?'Bottom':'Right'}`]:0)}function getWindowSizes(){const e=window.document.body,t=window.document.documentElement,o=isIE10$1()&&window.getComputedStyle(t);return{height:getSize('Height',e,t,o),width:getSize('Width',e,t,o)}}var _extends=Object.assign||function(e){for(var t,o=1;o<arguments.length;o++)for(var n in t=arguments[o],t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e};function getClientRect(e){return _extends({},e,{right:e.left+e.width,bottom:e.top+e.height})}function getBoundingClientRect(e){let t={};if(isIE10$1())try{t=e.getBoundingClientRect();const o=getScroll(e,'top'),n=getScroll(e,'left');t.top+=o,t.left+=n,t.bottom+=o,t.right+=n}catch(e){}else t=e.getBoundingClientRect();const o={left:t.left,top:t.top,width:t.right-t.left,height:t.bottom-t.top},n='HTML'===e.nodeName?getWindowSizes():{},i=n.width||e.clientWidth||o.right-o.left,r=n.height||e.clientHeight||o.bottom-o.top;let p=e.offsetWidth-i,d=e.offsetHeight-r;if(p||d){const t=getStyleComputedProperty(e);p-=getBordersSize(t,'x'),d-=getBordersSize(t,'y'),o.width-=p,o.height-=d}return getClientRect(o)}function getOffsetRectRelativeToArbitraryNode(e,t){const o=isIE10$1(),n='HTML'===t.nodeName,i=getBoundingClientRect(e),r=getBoundingClientRect(t),p=getScrollParent(e),d=getStyleComputedProperty(t),s=+d.borderTopWidth.split('px')[0],a=+d.borderLeftWidth.split('px')[0];let f=getClientRect({top:i.top-r.top-s,left:i.left-r.left-a,width:i.width,height:i.height});if(f.marginTop=0,f.marginLeft=0,!o&&n){const e=+d.marginTop.split('px')[0],t=+d.marginLeft.split('px')[0];f.top-=s-e,f.bottom-=s-e,f.left-=a-t,f.right-=a-t,f.marginTop=e,f.marginLeft=t}return(o?t.contains(p):t===p&&'BODY'!==p.nodeName)&&(f=includeScroll(f,t)),f}function getViewportOffsetRectRelativeToArtbitraryNode(e){var t=Math.max;const o=e.ownerDocument.documentElement,n=getOffsetRectRelativeToArbitraryNode(e,o),i=t(o.clientWidth,window.innerWidth||0),r=t(o.clientHeight,window.innerHeight||0),p=getScroll(o),d=getScroll(o,'left'),s={top:p-n.top+n.marginTop,left:d-n.left+n.marginLeft,width:i,height:r};return getClientRect(s)}function isFixed(e){const t=e.nodeName;return'BODY'===t||'HTML'===t?!1:!('fixed'!==getStyleComputedProperty(e,'position'))||isFixed(getParentNode(e))}function getBoundaries(e,t,o,n){let i={top:0,left:0};const r=findCommonOffsetParent(e,t);if('viewport'===n)i=getViewportOffsetRectRelativeToArtbitraryNode(r);else{let t;'scrollParent'===n?(t=getScrollParent(getParentNode(e)),'BODY'===t.nodeName&&(t=e.ownerDocument.documentElement)):'window'===n?t=e.ownerDocument.documentElement:t=n;const o=getOffsetRectRelativeToArbitraryNode(t,r);if('HTML'===t.nodeName&&!isFixed(r)){const{height:e,width:t}=getWindowSizes();i.top+=o.top-o.marginTop,i.bottom=e+o.top,i.left+=o.left-o.marginLeft,i.right=t+o.left}else i=o}return i.left+=o,i.top+=o,i.right-=o,i.bottom-=o,i}function getArea({width:e,height:t}){return e*t}function computeAutoPlacement(e,t,o,n,i,r=0){if(-1===e.indexOf('auto'))return e;const p=getBoundaries(o,n,r,i),d={top:{width:p.width,height:t.top-p.top},right:{width:p.right-t.right,height:p.height},bottom:{width:p.width,height:p.bottom-t.bottom},left:{width:t.left-p.left,height:p.height}},s=Object.keys(d).map((e)=>_extends({key:e},d[e],{area:getArea(d[e])})).sort((e,t)=>t.area-e.area),a=s.filter(({width:e,height:t})=>e>=o.clientWidth&&t>=o.clientHeight),f=0<a.length?a[0].key:s[0].key,l=e.split('-')[1];return f+(l?`-${l}`:'')}function getReferenceOffsets(e,t,o){const n=findCommonOffsetParent(t,o);return getOffsetRectRelativeToArbitraryNode(o,n)}function getOuterSizes(e){const t=window.getComputedStyle(e),o=parseFloat(t.marginTop)+parseFloat(t.marginBottom),n=parseFloat(t.marginLeft)+parseFloat(t.marginRight),i={width:e.offsetWidth+n,height:e.offsetHeight+o};return i}function getOppositePlacement(e){const t={left:'right',right:'left',bottom:'top',top:'bottom'};return e.replace(/left|right|bottom|top/g,(e)=>t[e])}function getPopperOffsets(e,t,o){o=o.split('-')[0];const n=getOuterSizes(e),i={width:n.width,height:n.height},r=-1!==['right','left'].indexOf(o),p=r?'top':'left',d=r?'left':'top',s=r?'height':'width',a=r?'width':'height';return i[p]=t[p]+t[s]/2-n[s]/2,i[d]=o===d?t[d]-n[a]:t[getOppositePlacement(d)],i}function find(e,t){return Array.prototype.find?e.find(t):e.filter(t)[0]}function findIndex(e,t,o){if(Array.prototype.findIndex)return e.findIndex((e)=>e[t]===o);const n=find(e,(e)=>e[t]===o);return e.indexOf(n)}function runModifiers(e,t,o){const n=void 0===o?e:e.slice(0,findIndex(e,'name',o));return n.forEach((e)=>{e['function']&&console.warn('`modifier.function` is deprecated, use `modifier.fn`!');const o=e['function']||e.fn;e.enabled&&isFunction(o)&&(t.offsets.popper=getClientRect(t.offsets.popper),t.offsets.reference=getClientRect(t.offsets.reference),t=o(t,e))}),t}function update(){if(this.state.isDestroyed)return;let e={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}};e.offsets.reference=getReferenceOffsets(this.state,this.popper,this.reference),e.placement=computeAutoPlacement(this.options.placement,e.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),e.originalPlacement=e.placement,e.offsets.popper=getPopperOffsets(this.popper,e.offsets.reference,e.placement),e.offsets.popper.position='absolute',e=runModifiers(this.modifiers,e),this.state.isCreated?this.options.onUpdate(e):(this.state.isCreated=!0,this.options.onCreate(e))}function isModifierEnabled(e,t){return e.some(({name:e,enabled:o})=>o&&e===t)}function getSupportedPropertyName(e){const t=[!1,'ms','Webkit','Moz','O'],o=e.charAt(0).toUpperCase()+e.slice(1);for(let n=0;n<t.length-1;n++){const i=t[n],r=i?`${i}${o}`:e;if('undefined'!=typeof window.document.body.style[r])return r}return null}function destroy(){return this.state.isDestroyed=!0,isModifierEnabled(this.modifiers,'applyStyle')&&(this.popper.removeAttribute('x-placement'),this.popper.style.left='',this.popper.style.position='',this.popper.style.top='',this.popper.style[getSupportedPropertyName('transform')]=''),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}function getWindow(e){const t=e.ownerDocument;return t?t.defaultView:window}function attachToScrollParents(e,t,o,n){const i='BODY'===e.nodeName,r=i?e.ownerDocument.defaultView:e;r.addEventListener(t,o,{passive:!0}),i||attachToScrollParents(getScrollParent(r.parentNode),t,o,n),n.push(r)}function setupEventListeners(e,t,o,n){o.updateBound=n,getWindow(e).addEventListener('resize',o.updateBound,{passive:!0});const i=getScrollParent(e);return attachToScrollParents(i,'scroll',o.updateBound,o.scrollParents),o.scrollElement=i,o.eventsEnabled=!0,o}function enableEventListeners(){this.state.eventsEnabled||(this.state=setupEventListeners(this.reference,this.options,this.state,this.scheduleUpdate))}function removeEventListeners(e,t){return getWindow(e).removeEventListener('resize',t.updateBound),t.scrollParents.forEach((e)=>{e.removeEventListener('scroll',t.updateBound)}),t.updateBound=null,t.scrollParents=[],t.scrollElement=null,t.eventsEnabled=!1,t}function disableEventListeners(){this.state.eventsEnabled&&(window.cancelAnimationFrame(this.scheduleUpdate),this.state=removeEventListeners(this.reference,this.state))}function isNumeric(e){return''!==e&&!isNaN(parseFloat(e))&&isFinite(e)}function setStyles(e,t){Object.keys(t).forEach((o)=>{let n='';-1!==['width','height','top','right','bottom','left'].indexOf(o)&&isNumeric(t[o])&&(n='px'),e.style[o]=t[o]+n})}function setAttributes(e,t){Object.keys(t).forEach(function(o){const n=t[o];!1===n?e.removeAttribute(o):e.setAttribute(o,t[o])})}function applyStyle(e){return setStyles(e.instance.popper,e.styles),setAttributes(e.instance.popper,e.attributes),e.arrowElement&&Object.keys(e.arrowStyles).length&&setStyles(e.arrowElement,e.arrowStyles),e}function applyStyleOnLoad(e,t,o,n,i){const r=getReferenceOffsets(i,t,e),p=computeAutoPlacement(o.placement,r,t,e,o.modifiers.flip.boundariesElement,o.modifiers.flip.padding);return t.setAttribute('x-placement',p),setStyles(t,{position:'absolute'}),o}function computeStyle(e,t){var o=Math.floor;const{x:n,y:i}=t,{popper:r}=e.offsets,p=find(e.instance.modifiers,(e)=>'applyStyle'===e.name).gpuAcceleration;void 0!==p&&console.warn('WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!');const d=void 0===p?t.gpuAcceleration:p,s=getOffsetParent(e.instance.popper),a=getBoundingClientRect(s),f={position:r.position},l={left:o(r.left),top:o(r.top),bottom:o(r.bottom),right:o(r.right)},m='bottom'===n?'top':'bottom',c='right'===i?'left':'right',h=getSupportedPropertyName('transform');let u,g;if(g='bottom'==m?-a.height+l.bottom:l.top,u='right'==c?-a.width+l.right:l.left,d&&h)f[h]=`translate3d(${u}px, ${g}px, 0)`,f[m]=0,f[c]=0,f.willChange='transform';else{const e='bottom'==m?-1:1,t='right'==c?-1:1;f[m]=g*e,f[c]=u*t,f.willChange=`${m}, ${c}`}const b={"x-placement":e.placement};return e.attributes=_extends({},b,e.attributes),e.styles=_extends({},f,e.styles),e.arrowStyles=_extends({},e.offsets.arrow,e.arrowStyles),e}function isModifierRequired(e,t,o){const n=find(e,({name:e})=>e===t),i=!!n&&e.some((e)=>e.name===o&&e.enabled&&e.order<n.order);if(!i){const e=`\`${t}\``,n=`\`${o}\``;console.warn(`${n} modifier is required by ${e} modifier in order to work, be sure to include it before ${e}!`)}return i}function arrow(e,t){if(!isModifierRequired(e.instance.modifiers,'arrow','keepTogether'))return e;let o=t.element;if('string'==typeof o){if(o=e.instance.popper.querySelector(o),!o)return e;}else if(!e.instance.popper.contains(o))return console.warn('WARNING: `arrow.element` must be child of its popper element!'),e;const n=e.placement.split('-')[0],{popper:i,reference:r}=e.offsets,p=-1!==['left','right'].indexOf(n),d=p?'height':'width',s=p?'Top':'Left',a=s.toLowerCase(),f=p?'left':'top',l=p?'bottom':'right',m=getOuterSizes(o)[d];r[l]-m<i[a]&&(e.offsets.popper[a]-=i[a]-(r[l]-m)),r[a]+m>i[l]&&(e.offsets.popper[a]+=r[a]+m-i[l]);const c=r[a]+r[d]/2-m/2,h=getStyleComputedProperty(e.instance.popper,`margin${s}`).replace('px','');let u=c-getClientRect(e.offsets.popper)[a]-h;return u=Math.max(Math.min(i[d]-m,u),0),e.arrowElement=o,e.offsets.arrow={},e.offsets.arrow[a]=Math.round(u),e.offsets.arrow[f]='',e}function getOppositeVariation(e){if('end'===e)return'start';return'start'===e?'end':e}var placements=['auto-start','auto','auto-end','top-start','top','top-end','right-start','right','right-end','bottom-end','bottom','bottom-start','left-end','left','left-start'];const validPlacements=placements.slice(3);function clockwise(e,t=!1){const o=validPlacements.indexOf(e),n=validPlacements.slice(o+1).concat(validPlacements.slice(0,o));return t?n.reverse():n}const BEHAVIORS={FLIP:'flip',CLOCKWISE:'clockwise',COUNTERCLOCKWISE:'counterclockwise'};function flip(e,t){if(isModifierEnabled(e.instance.modifiers,'inner'))return e;if(e.flipped&&e.placement===e.originalPlacement)return e;const o=getBoundaries(e.instance.popper,e.instance.reference,t.padding,t.boundariesElement);let n=e.placement.split('-')[0],i=getOppositePlacement(n),r=e.placement.split('-')[1]||'',p=[];switch(t.behavior){case BEHAVIORS.FLIP:p=[n,i];break;case BEHAVIORS.CLOCKWISE:p=clockwise(n);break;case BEHAVIORS.COUNTERCLOCKWISE:p=clockwise(n,!0);break;default:p=t.behavior;}return p.forEach((d,s)=>{if(n!==d||p.length===s+1)return e;n=e.placement.split('-')[0],i=getOppositePlacement(n);const a=e.offsets.popper,f=e.offsets.reference,l=Math.floor,m='left'===n&&l(a.right)>l(f.left)||'right'===n&&l(a.left)<l(f.right)||'top'===n&&l(a.bottom)>l(f.top)||'bottom'===n&&l(a.top)<l(f.bottom),c=l(a.left)<l(o.left),h=l(a.right)>l(o.right),u=l(a.top)<l(o.top),g=l(a.bottom)>l(o.bottom),b='left'===n&&c||'right'===n&&h||'top'===n&&u||'bottom'===n&&g,w=-1!==['top','bottom'].indexOf(n),y=!!t.flipVariations&&(w&&'start'===r&&c||w&&'end'===r&&h||!w&&'start'===r&&u||!w&&'end'===r&&g);(m||b||y)&&(e.flipped=!0,(m||b)&&(n=p[s+1]),y&&(r=getOppositeVariation(r)),e.placement=n+(r?'-'+r:''),e.offsets.popper=_extends({},e.offsets.popper,getPopperOffsets(e.instance.popper,e.offsets.reference,e.placement)),e=runModifiers(e.instance.modifiers,e,'flip'))}),e}function keepTogether(e){const{popper:t,reference:o}=e.offsets,n=e.placement.split('-')[0],i=Math.floor,r=-1!==['top','bottom'].indexOf(n),p=r?'right':'bottom',d=r?'left':'top',s=r?'width':'height';return t[p]<i(o[d])&&(e.offsets.popper[d]=i(o[d])-t[s]),t[d]>i(o[p])&&(e.offsets.popper[d]=i(o[p])),e}function toValue(e,t,o,n){var i=Math.max;const r=e.match(/((?:\-|\+)?\d*\.?\d*)(.*)/),p=+r[1],d=r[2];if(!p)return e;if(0===d.indexOf('%')){let e;switch(d){case'%p':e=o;break;case'%':case'%r':default:e=n;}const i=getClientRect(e);return i[t]/100*p}if('vh'===d||'vw'===d){let e;return e='vh'===d?i(document.documentElement.clientHeight,window.innerHeight||0):i(document.documentElement.clientWidth,window.innerWidth||0),e/100*p}return p}function parseOffset(e,t,o,n){const i=[0,0],r=-1!==['right','left'].indexOf(n),p=e.split(/(\+|\-)/).map((e)=>e.trim()),d=p.indexOf(find(p,(e)=>-1!==e.search(/,|\s/)));p[d]&&-1===p[d].indexOf(',')&&console.warn('Offsets separated by white space(s) are deprecated, use a comma (,) instead.');const s=/\s*,\s*|\s+/;let a=-1===d?[p]:[p.slice(0,d).concat([p[d].split(s)[0]]),[p[d].split(s)[1]].concat(p.slice(d+1))];return a=a.map((e,n)=>{const i=(1===n?!r:r)?'height':'width';let p=!1;return e.reduce((e,t)=>''===e[e.length-1]&&-1!==['+','-'].indexOf(t)?(e[e.length-1]=t,p=!0,e):p?(e[e.length-1]+=t,p=!1,e):e.concat(t),[]).map((e)=>toValue(e,i,t,o))}),a.forEach((e,t)=>{e.forEach((o,n)=>{isNumeric(o)&&(i[t]+=o*('-'===e[n-1]?-1:1))})}),i}function offset(e,{offset:t}){const{placement:o,offsets:{popper:n,reference:i}}=e,r=o.split('-')[0];let p;return p=isNumeric(+t)?[+t,0]:parseOffset(t,n,i,r),'left'===r?(n.top+=p[0],n.left-=p[1]):'right'===r?(n.top+=p[0],n.left+=p[1]):'top'===r?(n.left+=p[0],n.top-=p[1]):'bottom'===r&&(n.left+=p[0],n.top+=p[1]),e.popper=n,e}function preventOverflow(e,t){let o=t.boundariesElement||getOffsetParent(e.instance.popper);e.instance.reference===o&&(o=getOffsetParent(o));const n=getBoundaries(e.instance.popper,e.instance.reference,t.padding,o);t.boundaries=n;const i=t.priority;let r=e.offsets.popper;const p={primary(e){let o=r[e];return r[e]<n[e]&&!t.escapeWithReference&&(o=Math.max(r[e],n[e])),{[e]:o}},secondary(e){const o='right'===e?'left':'top';let i=r[o];return r[e]>n[e]&&!t.escapeWithReference&&(i=Math.min(r[o],n[e]-('right'===e?r.width:r.height))),{[o]:i}}};return i.forEach((e)=>{const t=-1===['left','top'].indexOf(e)?'secondary':'primary';r=_extends({},r,p[t](e))}),e.offsets.popper=r,e}function shift(e){const t=e.placement,o=t.split('-')[0],n=t.split('-')[1];if(n){const{reference:t,popper:i}=e.offsets,r=-1!==['bottom','top'].indexOf(o),p=r?'left':'top',d=r?'width':'height',s={start:{[p]:t[p]},end:{[p]:t[p]+t[d]-i[d]}};e.offsets.popper=_extends({},i,s[n])}return e}function hide(e){if(!isModifierRequired(e.instance.modifiers,'hide','preventOverflow'))return e;const t=e.offsets.reference,o=find(e.instance.modifiers,(e)=>'preventOverflow'===e.name).boundaries;if(t.bottom<o.top||t.left>o.right||t.top>o.bottom||t.right<o.left){if(!0===e.hide)return e;e.hide=!0,e.attributes['x-out-of-boundaries']=''}else{if(!1===e.hide)return e;e.hide=!1,e.attributes['x-out-of-boundaries']=!1}return e}function inner(e){const t=e.placement,o=t.split('-')[0],{popper:n,reference:i}=e.offsets,r=-1!==['left','right'].indexOf(o),p=-1===['top','left'].indexOf(o);return n[r?'left':'top']=i[o]-(p?n[r?'width':'height']:0),e.placement=getOppositePlacement(t),e.offsets.popper=getClientRect(n),e}var modifiers={shift:{order:100,enabled:!0,fn:shift},offset:{order:200,enabled:!0,fn:offset,offset:0},preventOverflow:{order:300,enabled:!0,fn:preventOverflow,priority:['left','right','top','bottom'],padding:5,boundariesElement:'scrollParent'},keepTogether:{order:400,enabled:!0,fn:keepTogether},arrow:{order:500,enabled:!0,fn:arrow,element:'[x-arrow]'},flip:{order:600,enabled:!0,fn:flip,behavior:'flip',padding:5,boundariesElement:'viewport'},inner:{order:700,enabled:!1,fn:inner},hide:{order:800,enabled:!0,fn:hide},computeStyle:{order:850,enabled:!0,fn:computeStyle,gpuAcceleration:!0,x:'bottom',y:'right'},applyStyle:{order:900,enabled:!0,fn:applyStyle,onLoad:applyStyleOnLoad,gpuAcceleration:void 0}},Defaults={placement:'bottom',eventsEnabled:!0,removeOnDestroy:!1,onCreate:()=>{},onUpdate:()=>{},modifiers};class Popper{constructor(e,t,o={}){this.scheduleUpdate=()=>requestAnimationFrame(this.update),this.update=debounce(this.update.bind(this)),this.options=_extends({},Popper.Defaults,o),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=e&&e.jquery?e[0]:e,this.popper=t&&t.jquery?t[0]:t,this.options.modifiers={},Object.keys(_extends({},Popper.Defaults.modifiers,o.modifiers)).forEach((e)=>{this.options.modifiers[e]=_extends({},Popper.Defaults.modifiers[e]||{},o.modifiers?o.modifiers[e]:{})}),this.modifiers=Object.keys(this.options.modifiers).map((e)=>_extends({name:e},this.options.modifiers[e])).sort((e,t)=>e.order-t.order),this.modifiers.forEach((e)=>{e.enabled&&isFunction(e.onLoad)&&e.onLoad(this.reference,this.popper,this.options,e,this.state)}),this.update();const n=this.options.eventsEnabled;n&&this.enableEventListeners(),this.state.eventsEnabled=n}update(){return update.call(this)}destroy(){return destroy.call(this)}enableEventListeners(){return enableEventListeners.call(this)}disableEventListeners(){return disableEventListeners.call(this)}}Popper.Utils=('undefined'==typeof window?global:window).PopperUtils,Popper.placements=placements,Popper.Defaults=Defaults;export default Popper;
//# sourceMappingURL=popper.min.js.map
