{"version": 3, "sources": ["../../node_modules/popper.js/dist/esm/popper.js", "../../js/src/util.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../js/src/dropdown.js", "../../js/src/modal.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/index.js"], "names": ["isFunction", "functionToCheck", "toString", "call", "getStyleComputedProperty", "element", "property", "nodeType", "css", "window", "getComputedStyle", "getParentNode", "nodeName", "parentNode", "host", "getScrollParent", "indexOf", "document", "body", "_getStyleComputedProp", "overflow", "overflowX", "test", "overflowY", "getOffsetParent", "offsetParent", "documentElement", "isOffsetContainer", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "getRoot", "node", "findCommonOffsetParent", "element1", "element2", "order", "compareDocumentPosition", "Node", "DOCUMENT_POSITION_FOLLOWING", "start", "end", "range", "createRange", "setStart", "setEnd", "commonAncestorContainer", "contains", "element1root", "getScroll", "upperSide", "arguments", "length", "undefined", "html", "scrollingElement", "includeScroll", "rect", "subtract", "scrollTop", "scrollLeft", "modifier", "top", "bottom", "left", "right", "getBordersSize", "styles", "axis", "sideA", "sideB", "split", "getSize", "computedStyle", "Math", "max", "isIE10$1", "getWindowSizes", "height", "width", "getClientRect", "offsets", "_extends", "getBoundingClientRect", "err", "result", "sizes", "clientWidth", "clientHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "offsetWidth", "vertScrollbar", "offsetHeight", "getOffsetRectRelativeToArbitraryNode", "children", "parent", "isIE10", "isHTML", "childrenRect", "parentRect", "scrollParent", "borderTopWidth", "borderLeftWidth", "marginTop", "marginLeft", "getViewportOffsetRectRelativeToArtbitraryNode", "relativeOffset", "innerWidth", "innerHeight", "isFixed", "getBoundaries", "popper", "reference", "padding", "boundariesElement", "boundaries", "boundariesNode", "_getWindowSizes", "getArea", "_ref", "computeAutoPlacement", "placement", "refRect", "rects", "sorted<PERSON>reas", "Object", "keys", "map", "key", "area", "sort", "a", "b", "filtered<PERSON><PERSON>s", "filter", "_ref2", "computedPlacement", "variation", "getReferenceOffsets", "state", "getOuterSizes", "x", "parseFloat", "marginBottom", "y", "marginRight", "getOppositePlacement", "hash", "replace", "matched", "getPopperOffsets", "referenceOffsets", "popperRect", "popperOffsets", "isHoriz", "mainSide", "secondarySide", "measurement", "secondaryMeasurement", "find", "arr", "check", "Array", "prototype", "findIndex", "prop", "value", "cur", "match", "obj", "runModifiers", "modifiers", "data", "ends", "slice", "for<PERSON>ach", "function", "console", "warn", "fn", "enabled", "update", "this", "isDestroyed", "instance", "arrowStyles", "attributes", "flipped", "options", "flip", "originalPlacement", "position", "isCreated", "onUpdate", "onCreate", "isModifierEnabled", "modifierName", "some", "name", "getSupportedPropertyName", "prefixes", "upperProp", "char<PERSON>t", "toUpperCase", "i", "prefix", "to<PERSON><PERSON><PERSON>", "style", "destroy", "removeAttribute", "disableEventListeners", "removeOnDestroy", "<PERSON><PERSON><PERSON><PERSON>", "attachToScrollParents", "event", "callback", "scrollParents", "isBody", "target", "addEventListener", "passive", "push", "setupEventListeners", "updateBound", "scrollElement", "eventsEnabled", "enableEventListeners", "scheduleUpdate", "removeEventListeners", "removeEventListener", "cancelAnimationFrame", "isNumeric", "n", "isNaN", "isFinite", "setStyles", "unit", "setAttributes", "setAttribute", "isModifierRequired", "requestingName", "requested<PERSON><PERSON>", "requesting", "isRequired", "_requesting", "requested", "getOppositeVariation", "clockwise", "counter", "index", "validPlacements", "concat", "reverse", "toValue", "str", "parseOffset", "offset", "basePlacement", "useHeight", "fragments", "frag", "trim", "divider", "search", "splitRegex", "ops", "op", "mergeWithPrevious", "reduce", "index2", "<PERSON><PERSON>", "toType", "toLowerCase", "getSpecialTransitionEndEvent", "transition", "$", "is", "handleObj", "handler", "apply", "transitionEndTest", "QUnit", "el", "createElement", "TransitionEndEvent", "transitionEndEmulator", "duration", "called", "one", "TRANSITION_END", "triggerTransitionEnd", "_this", "random", "getElementById", "selector", "getAttribute", "error", "trigger", "Boolean", "componentName", "config", "configTypes", "hasOwnProperty", "expectedTypes", "valueType", "isElement", "RegExp", "Error", "emulateTransitionEnd", "supportsTransitionEnd", "special", "<PERSON><PERSON>", "NAME", "JQUERY_NO_CONFLICT", "Event", "ClassName", "_element", "close", "rootElement", "_getRootElement", "_triggerCloseEvent", "isDefaultPrevented", "_removeElement", "dispose", "removeData", "getSelectorFromElement", "closest", "ALERT", "closeEvent", "CLOSE", "removeClass", "SHOW", "hasClass", "FADE", "_destroyElement", "detach", "CLOSED", "remove", "_jQueryInterface", "each", "$element", "_handleDismiss", "alertInstance", "preventDefault", "on", "CLICK_DATA_API", "DISMISS", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "<PERSON><PERSON>", "Selector", "toggle", "triggerChangeEvent", "addAriaPressed", "DATA_TOGGLE", "input", "INPUT", "type", "checked", "ACTIVE", "activeElement", "hasAttribute", "classList", "focus", "toggleClass", "DATA_TOGGLE_CARROT", "button", "BUTTON", "FOCUS_BLUR_DATA_API", "FOCUS", "Carousel", "DATA_KEY", "EVENT_KEY", "<PERSON><PERSON><PERSON>", "DefaultType", "Direction", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "_config", "_getConfig", "_indicatorsElement", "INDICATORS", "_addEventListeners", "next", "_slide", "NEXT", "nextWhenVisible", "hidden", "prev", "PREV", "pause", "NEXT_PREV", "cycle", "interval", "setInterval", "visibilityState", "bind", "to", "ACTIVE_ITEM", "activeIndex", "_getItemIndex", "SLID", "direction", "off", "extend", "typeCheckConfig", "keyboard", "KEYDOWN", "_this2", "_keydown", "MOUSEENTER", "MOUSELEAVE", "TOUCHEND", "setTimeout", "tagName", "which", "makeArray", "ITEM", "_getItemByDirection", "isNextDirection", "isPrevDirection", "lastItemIndex", "wrap", "itemIndex", "_triggerSlideEvent", "relatedTarget", "eventDirectionName", "targetIndex", "fromIndex", "slideEvent", "SLIDE", "_setActiveIndicatorElement", "nextIndicator", "addClass", "directionalClassName", "orderClassName", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "LEFT", "RIGHT", "slidEvent", "reflow", "_this3", "action", "slide", "_dataApiClickHandler", "CAROUSEL", "slideIndex", "DATA_SLIDE", "LOAD_DATA_API", "DATA_RIDE", "$carousel", "Collapse", "Dimension", "_isTransitioning", "_triggerArray", "id", "tab<PERSON>og<PERSON>", "elem", "_parent", "_getParent", "_addAriaAndCollapsedClass", "hide", "show", "actives", "activesData", "ACTIVES", "startEvent", "dimension", "_getDimension", "COLLAPSE", "COLLAPSING", "COLLAPSED", "attr", "setTransitioning", "complete", "SHOWN", "scrollSize", "HIDE", "HIDDEN", "isTransitioning", "WIDTH", "HEIGHT", "j<PERSON>y", "_getTargetFromElement", "trigger<PERSON><PERSON>y", "isOpen", "$this", "currentTarget", "$trigger", "$target", "nativeHints", "<PERSON><PERSON><PERSON><PERSON>", "longerTimeoutBrowsers", "timeoutDuration", "navigator", "userAgent", "debounce", "hint", "isNative", "MutationObserver", "scheduled", "observe", "appVersion", "classCallCheck", "TypeError", "createClass", "defineProperties", "props", "descriptor", "enumerable", "configurable", "writable", "defineProperty", "protoProps", "staticProps", "assign", "source", "placements", "BEHAVIORS", "FLIP", "CLOCKWISE", "COUNTERCLOCKWISE", "De<PERSON>ults", "shift", "shiftvariation", "_data$offsets", "isVertical", "side", "shiftOffsets", "preventOverflow", "priority", "primary", "escapeWithReference", "secondary", "min", "keepTogether", "floor", "opSide", "arrow", "arrowElement", "querySelector", "len", "sideCapitalized", "altSide", "arrowElementSize", "center", "popperMarginSide", "sideValue", "round", "placementOpposite", "flipOrder", "behavior", "step", "refOffsets", "overlapsRef", "overflowsLeft", "overflowsRight", "overflowsTop", "overflowsBottom", "overflowsBoundaries", "flippedVariation", "flipVariations", "inner", "subtractLength", "bound", "computeStyle", "legacyGpuAccelerationOption", "gpuAcceleration", "offsetParentRect", "prefixedProperty", "<PERSON><PERSON><PERSON><PERSON>", "invertTop", "invertLeft", "x-placement", "applyStyle", "onLoad", "modifierOptions", "<PERSON><PERSON>", "requestAnimationFrame", "Utils", "global", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Dropdown", "REGEXP_KEYDOWN", "ARROW_UP_KEYCODE", "AttachmentMap", "_popper", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "disabled", "DISABLED", "_getParentFromElement", "isActive", "_clearMenus", "showEvent", "DROPUP", "MENULEFT", "MENURIGHT", "_getPopperConfig", "NAVBAR_NAV", "noop", "CLICK", "stopPropagation", "constructor", "MENU", "_getPlacement", "$parentDropdown", "BOTTOM", "TOP", "TOPEND", "BOTTOMEND", "offsetConf", "popperConfig", "toggles", "context", "dropdownMenu", "hideEvent", "_dataApiKeydownHandler", "items", "VISIBLE_ITEMS", "get", "KEYDOWN_DATA_API", "KEYUP_DATA_API", "FORM_CHILD", "e", "Modal", "_dialog", "DIALOG", "_backdrop", "_isShown", "_isBodyOverflowing", "_ignoreBackdropClick", "_originalBodyPadding", "_scrollbarWidth", "_checkScrollbar", "_setScrollbar", "_adjustDialog", "OPEN", "_setEscapeEvent", "_setResizeEvent", "CLICK_DISMISS", "DATA_DISMISS", "MOUSEDOWN_DISMISS", "MOUSEUP_DISMISS", "_showBackdrop", "_showElement", "FOCUSIN", "_hideModal", "handleUpdate", "ELEMENT_NODE", "append<PERSON><PERSON><PERSON>", "display", "_enforceFocus", "shownEvent", "transitionComplete", "_this4", "has", "KEYDOWN_DISMISS", "RESIZE", "_this6", "_resetAdjustments", "_resetScrollbar", "_this7", "_removeBackdrop", "animate", "backdrop", "doAnimate", "className", "BACKDROP", "appendTo", "_this8", "callback<PERSON><PERSON><PERSON>", "isModalOverflowing", "scrollHeight", "paddingLeft", "paddingRight", "_getScrollbarWidth", "FIXED_CONTENT", "actualPadding", "calculatedPadding", "_this9", "STICKY_CONTENT", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "NAVBAR_TOGGLER", "margin", "scrollDiv", "SCROLLBAR_MEASURER", "scrollbarWidth", "<PERSON><PERSON><PERSON>", "BSCLS_PREFIX_REGEX", "HoverState", "<PERSON><PERSON>", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "tip", "_setListeners", "enable", "disable", "toggle<PERSON>nabled", "dataKey", "_getDelegateConfig", "click", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "isWithContent", "isInTheDom", "ownerDocument", "tipId", "getUID", "<PERSON><PERSON><PERSON><PERSON>", "animation", "attachment", "_getAttachment", "addAttachmentClass", "container", "INSERTED", "fallbackPlacement", "ARROW", "_handlePopperPlacementChange", "_fixTransition", "prevHoverState", "OUT", "_TRANSITION_DURATION", "_cleanTipClass", "HOVER", "getTitle", "CLASS_PREFIX", "template", "$tip", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TOOLTIP_INNER", "content", "empty", "append", "text", "title", "MANUAL", "eventIn", "eventOut", "FOCUSOUT", "_fixTitle", "titleType", "delay", "tabClass", "join", "initConfigAnimation", "Popover", "_getContent", "TITLE", "CONTENT", "ScrollSpy", "OffsetMethod", "_scrollElement", "_selector", "NAV_LINKS", "LIST_ITEMS", "DROPDOWN_ITEMS", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "SCROLL", "_process", "refresh", "autoMethod", "POSITION", "OFFSET", "offsetMethod", "method", "offsetBase", "_getScrollTop", "_getScrollHeight", "targetSelector", "targetBCR", "item", "pageYOffset", "_getOffsetHeight", "maxScroll", "_activate", "_clear", "queries", "$link", "DROPDOWN_ITEM", "DROPDOWN", "DROPDOWN_TOGGLE", "parents", "NAV_LIST_GROUP", "NAV_ITEMS", "ACTIVATE", "scrollSpys", "DATA_SPY", "$spy", "Tab", "previous", "listElement", "itemSelector", "ACTIVE_UL", "hiddenEvent", "activeElements", "active", "_transitionComplete", "dropdown<PERSON><PERSON>d", "DROPDOWN_ACTIVE_CHILD", "DROPDOWN_MENU", "dropdownElement", "version"], "mappings": ";;;;;+MA8GA,SAASA,EAAWC,GAElB,OAAOA,GAA8D,yBAAnCC,SAASC,KAAKF,GAUlD,SAASG,EAAyBC,EAASC,GACzC,GAAyB,IAArBD,EAAQE,SACV,SAGF,IAAIC,EAAMC,OAAOC,iBAAiBL,EAAS,MAC3C,OAAOC,EAAWE,EAAIF,GAAYE,EAUpC,SAASG,EAAcN,GACrB,MAAyB,SAArBA,EAAQO,SACHP,EAEFA,EAAQQ,YAAcR,EAAQS,KAUvC,SAASC,EAAgBV,GAEvB,IAAKA,IAAwE,KAA5D,OAAQ,OAAQ,aAAaW,QAAQX,EAAQO,UAC5D,OAAOH,OAAOQ,SAASC,KAKzB,IAAIC,EAAwBf,EAAyBC,GACjDe,EAAWD,EAAsBC,SACjCC,EAAYF,EAAsBE,UAGtC,MAAI,gBAAgBC,KAAKF,EAFTD,EAAsBI,UAEUF,GACvChB,EAGFU,EAAgBJ,EAAcN,IAUvC,SAASmB,EAAgBnB,GAEvB,IAAIoB,EAAepB,GAAWA,EAAQoB,aAClCb,EAAWa,GAAgBA,EAAab,SAE5C,OAAKA,GAAyB,SAAbA,GAAoC,SAAbA,GAMgB,KAAnD,KAAM,SAASI,QAAQS,EAAab,WAA2E,WAAvDR,EAAyBqB,EAAc,YAC3FD,EAAgBC,GAGlBA,EATEhB,OAAOQ,SAASS,gBAY3B,SAASC,EAAkBtB,GACzB,IAAIO,EAAWP,EAAQO,SAEvB,MAAiB,SAAbA,IAGgB,SAAbA,GAAuBY,EAAgBnB,EAAQuB,qBAAuBvB,GAU/E,SAASwB,EAAQC,GACf,OAAwB,OAApBA,EAAKjB,WACAgB,EAAQC,EAAKjB,YAGfiB,EAWT,SAASC,EAAuBC,EAAUC,GAExC,KAAKD,GAAaA,EAASzB,UAAa0B,GAAaA,EAAS1B,UAC5D,OAAOE,OAAOQ,SAASS,gBAIzB,IAAIQ,EAAQF,EAASG,wBAAwBF,GAAYG,KAAKC,4BAC1DC,EAAQJ,EAAQF,EAAWC,EAC3BM,EAAML,EAAQD,EAAWD,EAGzBQ,EAAQvB,SAASwB,cACrBD,EAAME,SAASJ,EAAO,GACtBE,EAAMG,OAAOJ,EAAK,GAClB,IAAIK,EAA0BJ,EAAMI,wBAIpC,GAAIZ,IAAaY,GAA2BX,IAAaW,GAA2BN,EAAMO,SAASN,GACjG,OAAIZ,EAAkBiB,GACbA,EAGFpB,EAAgBoB,GAIzB,IAAIE,EAAejB,EAAQG,GAC3B,OAAIc,EAAahC,KACRiB,EAAuBe,EAAahC,KAAMmB,GAE1CF,EAAuBC,EAAUH,EAAQI,GAAUnB,MAY9D,SAASiC,EAAU1C,GACjB,IAEI2C,EAAqB,SAFdC,UAAUC,OAAS,QAAsBC,IAAjBF,UAAU,GAAmBA,UAAU,GAAK,OAE9C,YAAc,aAC3CrC,EAAWP,EAAQO,SAEvB,GAAiB,SAAbA,GAAoC,SAAbA,EAAqB,CAC9C,IAAIwC,EAAO3C,OAAOQ,SAASS,gBAE3B,OADuBjB,OAAOQ,SAASoC,kBAAoBD,GACnCJ,GAG1B,OAAO3C,EAAQ2C,GAYjB,SAASM,EAAcC,EAAMlD,GAC3B,IAAImD,EAAWP,UAAUC,OAAS,QAAsBC,IAAjBF,UAAU,IAAmBA,UAAU,GAE1EQ,EAAYV,EAAU1C,EAAS,OAC/BqD,EAAaX,EAAU1C,EAAS,QAChCsD,EAAWH,GAAY,EAAI,EAK/B,OAJAD,EAAKK,KAAOH,EAAYE,EACxBJ,EAAKM,QAAUJ,EAAYE,EAC3BJ,EAAKO,MAAQJ,EAAaC,EAC1BJ,EAAKQ,OAASL,EAAaC,EACpBJ,EAaT,SAASS,EAAeC,EAAQC,GAC9B,IAAIC,EAAiB,MAATD,EAAe,OAAS,MAChCE,EAAkB,SAAVD,EAAmB,QAAU,SAEzC,OAAQF,EAAO,SAAWE,EAAQ,SAASE,MAAM,MAAM,KAAMJ,EAAO,SAAWG,EAAQ,SAASC,MAAM,MAAM,GAkB9G,SAASC,EAAQJ,EAAMhD,EAAMkC,EAAMmB,GACjC,OAAOC,KAAKC,IAAIvD,EAAK,SAAWgD,GAAOhD,EAAK,SAAWgD,GAAOd,EAAK,SAAWc,GAAOd,EAAK,SAAWc,GAAOd,EAAK,SAAWc,GAAOQ,KAAatB,EAAK,SAAWc,GAAQK,EAAc,UAAqB,WAATL,EAAoB,MAAQ,SAAWK,EAAc,UAAqB,WAATL,EAAoB,SAAW,UAAY,GAGhT,SAASS,IACP,IAAIzD,EAAOT,OAAOQ,SAASC,KACvBkC,EAAO3C,OAAOQ,SAASS,gBACvB6C,EAAgBG,MAAcjE,OAAOC,iBAAiB0C,GAE1D,OACEwB,OAAQN,EAAQ,SAAUpD,EAAMkC,EAAMmB,GACtCM,MAAOP,EAAQ,QAASpD,EAAMkC,EAAMmB,IAoExC,SAASO,EAAcC,GACrB,OAAOC,MAAaD,GAClBhB,MAAOgB,EAAQjB,KAAOiB,EAAQF,MAC9BhB,OAAQkB,EAAQnB,IAAMmB,EAAQH,SAWlC,SAASK,EAAsB5E,GAC7B,IAAIkD,KAKJ,GAAImB,KACF,IACEnB,EAAOlD,EAAQ4E,wBACf,IAAIxB,EAAYV,EAAU1C,EAAS,OAC/BqD,EAAaX,EAAU1C,EAAS,QACpCkD,EAAKK,KAAOH,EACZF,EAAKO,MAAQJ,EACbH,EAAKM,QAAUJ,EACfF,EAAKQ,OAASL,EACd,MAAOwB,SAET3B,EAAOlD,EAAQ4E,wBAGjB,IAAIE,GACFrB,KAAMP,EAAKO,KACXF,IAAKL,EAAKK,IACViB,MAAOtB,EAAKQ,MAAQR,EAAKO,KACzBc,OAAQrB,EAAKM,OAASN,EAAKK,KAIzBwB,EAA6B,SAArB/E,EAAQO,SAAsB+D,OACtCE,EAAQO,EAAMP,OAASxE,EAAQgF,aAAeF,EAAOpB,MAAQoB,EAAOrB,KACpEc,EAASQ,EAAMR,QAAUvE,EAAQiF,cAAgBH,EAAOtB,OAASsB,EAAOvB,IAExE2B,EAAiBlF,EAAQmF,YAAcX,EACvCY,EAAgBpF,EAAQqF,aAAed,EAI3C,GAAIW,GAAkBE,EAAe,CACnC,IAAIxB,EAAS7D,EAAyBC,GACtCkF,GAAkBvB,EAAeC,EAAQ,KACzCwB,GAAiBzB,EAAeC,EAAQ,KAExCkB,EAAON,OAASU,EAChBJ,EAAOP,QAAUa,EAGnB,OAAOX,EAAcK,GAGvB,SAASQ,EAAqCC,EAAUC,GACtD,IAAIC,EAASpB,KACTqB,EAA6B,SAApBF,EAAOjF,SAChBoF,EAAef,EAAsBW,GACrCK,EAAahB,EAAsBY,GACnCK,EAAenF,EAAgB6E,GAE/B3B,EAAS7D,EAAyByF,GAClCM,GAAkBlC,EAAOkC,eAAe9B,MAAM,MAAM,GACpD+B,GAAmBnC,EAAOmC,gBAAgB/B,MAAM,MAAM,GAEtDU,EAAUD,GACZlB,IAAKoC,EAAapC,IAAMqC,EAAWrC,IAAMuC,EACzCrC,KAAMkC,EAAalC,KAAOmC,EAAWnC,KAAOsC,EAC5CvB,MAAOmB,EAAanB,MACpBD,OAAQoB,EAAapB,SASvB,GAPAG,EAAQsB,UAAY,EACpBtB,EAAQuB,WAAa,GAMhBR,GAAUC,EAAQ,CACrB,IAAIM,GAAapC,EAAOoC,UAAUhC,MAAM,MAAM,GAC1CiC,GAAcrC,EAAOqC,WAAWjC,MAAM,MAAM,GAEhDU,EAAQnB,KAAOuC,EAAiBE,EAChCtB,EAAQlB,QAAUsC,EAAiBE,EACnCtB,EAAQjB,MAAQsC,EAAkBE,EAClCvB,EAAQhB,OAASqC,EAAkBE,EAGnCvB,EAAQsB,UAAYA,EACpBtB,EAAQuB,WAAaA,EAOvB,OAJIR,EAASD,EAAOhD,SAASqD,GAAgBL,IAAWK,GAA0C,SAA1BA,EAAatF,YACnFmE,EAAUzB,EAAcyB,EAASc,IAG5Bd,EAGT,SAASwB,EAA8ClG,GACrD,IAAI+C,EAAO3C,OAAOQ,SAASS,gBACvB8E,EAAiBb,EAAqCtF,EAAS+C,GAC/DyB,EAAQL,KAAKC,IAAIrB,EAAKiC,YAAa5E,OAAOgG,YAAc,GACxD7B,EAASJ,KAAKC,IAAIrB,EAAKkC,aAAc7E,OAAOiG,aAAe,GAE3DjD,EAAYV,EAAUK,GACtBM,EAAaX,EAAUK,EAAM,QASjC,OAAO0B,GANLlB,IAAKH,EAAY+C,EAAe5C,IAAM4C,EAAeH,UACrDvC,KAAMJ,EAAa8C,EAAe1C,KAAO0C,EAAeF,WACxDzB,MAAOA,EACPD,OAAQA,IAcZ,SAAS+B,EAAQtG,GACf,IAAIO,EAAWP,EAAQO,SACvB,MAAiB,SAAbA,GAAoC,SAAbA,IAG2B,UAAlDR,EAAyBC,EAAS,aAG/BsG,EAAQhG,EAAcN,KAa/B,SAASuG,EAAcC,EAAQC,EAAWC,EAASC,GAEjD,IAAIC,GAAerD,IAAK,EAAGE,KAAM,GAC7BrC,EAAeM,EAAuB8E,EAAQC,GAGlD,GAA0B,aAAtBE,EACFC,EAAaV,EAA8C9E,OACtD,CAEL,IAAIyF,OAAiB,EACK,iBAAtBF,EAE8B,UADhCE,EAAiBnG,EAAgBJ,EAAckG,KAC5BjG,WACjBsG,EAAiBzG,OAAOQ,SAASS,iBAGnCwF,EAD+B,WAAtBF,EACQvG,OAAOQ,SAASS,gBAEhBsF,EAGnB,IAAIjC,EAAUY,EAAqCuB,EAAgBzF,GAGnE,GAAgC,SAA5ByF,EAAetG,UAAwB+F,EAAQlF,GAWjDwF,EAAalC,MAXmD,CAChE,IAAIoC,EAAkBxC,IAClBC,EAASuC,EAAgBvC,OACzBC,EAAQsC,EAAgBtC,MAE5BoC,EAAWrD,KAAOmB,EAAQnB,IAAMmB,EAAQsB,UACxCY,EAAWpD,OAASe,EAASG,EAAQnB,IACrCqD,EAAWnD,MAAQiB,EAAQjB,KAAOiB,EAAQuB,WAC1CW,EAAWlD,MAAQc,EAAQE,EAAQjB,MAavC,OALAmD,EAAWnD,MAAQiD,EACnBE,EAAWrD,KAAOmD,EAClBE,EAAWlD,OAASgD,EACpBE,EAAWpD,QAAUkD,EAEdE,EAGT,SAASG,EAAQC,GAIf,OAHYA,EAAKxC,MACJwC,EAAKzC,OAcpB,SAAS0C,EAAqBC,EAAWC,EAASX,EAAQC,EAAWE,GACnE,IAAID,EAAU9D,UAAUC,OAAS,QAAsBC,IAAjBF,UAAU,GAAmBA,UAAU,GAAK,EAElF,IAAmC,IAA/BsE,EAAUvG,QAAQ,QACpB,OAAOuG,EAGT,IAAIN,EAAaL,EAAcC,EAAQC,EAAWC,EAASC,GAEvDS,GACF7D,KACEiB,MAAOoC,EAAWpC,MAClBD,OAAQ4C,EAAQ5D,IAAMqD,EAAWrD,KAEnCG,OACEc,MAAOoC,EAAWlD,MAAQyD,EAAQzD,MAClCa,OAAQqC,EAAWrC,QAErBf,QACEgB,MAAOoC,EAAWpC,MAClBD,OAAQqC,EAAWpD,OAAS2D,EAAQ3D,QAEtCC,MACEe,MAAO2C,EAAQ1D,KAAOmD,EAAWnD,KACjCc,OAAQqC,EAAWrC,SAInB8C,EAAcC,OAAOC,KAAKH,GAAOI,IAAI,SAAUC,GACjD,OAAO9C,IACL8C,IAAKA,GACJL,EAAMK,IACPC,KAAMX,EAAQK,EAAMK,QAErBE,KAAK,SAAUC,EAAGC,GACnB,OAAOA,EAAEH,KAAOE,EAAEF,OAGhBI,EAAgBT,EAAYU,OAAO,SAAUC,GAC/C,IAAIxD,EAAQwD,EAAMxD,MACdD,EAASyD,EAAMzD,OACnB,OAAOC,GAASgC,EAAOxB,aAAeT,GAAUiC,EAAOvB,eAGrDgD,EAAoBH,EAAcjF,OAAS,EAAIiF,EAAc,GAAGL,IAAMJ,EAAY,GAAGI,IAErFS,EAAYhB,EAAUlD,MAAM,KAAK,GAErC,OAAOiE,GAAqBC,EAAY,IAAMA,EAAY,IAY5D,SAASC,EAAoBC,EAAO5B,EAAQC,GAE1C,OAAOnB,EAAqCmB,EADnB/E,EAAuB8E,EAAQC,IAW1D,SAAS4B,EAAcrI,GACrB,IAAI4D,EAASxD,OAAOC,iBAAiBL,GACjCsI,EAAIC,WAAW3E,EAAOoC,WAAauC,WAAW3E,EAAO4E,cACrDC,EAAIF,WAAW3E,EAAOqC,YAAcsC,WAAW3E,EAAO8E,aAK1D,OAHElE,MAAOxE,EAAQmF,YAAcsD,EAC7BlE,OAAQvE,EAAQqF,aAAeiD,GAYnC,SAASK,EAAqBzB,GAC5B,IAAI0B,GAASnF,KAAM,QAASC,MAAO,OAAQF,OAAQ,MAAOD,IAAK,UAC/D,OAAO2D,EAAU2B,QAAQ,yBAA0B,SAAUC,GAC3D,OAAOF,EAAKE,KAchB,SAASC,EAAiBvC,EAAQwC,EAAkB9B,GAClDA,EAAYA,EAAUlD,MAAM,KAAK,GAGjC,IAAIiF,EAAaZ,EAAc7B,GAG3B0C,GACF1E,MAAOyE,EAAWzE,MAClBD,OAAQ0E,EAAW1E,QAIjB4E,GAAoD,KAAzC,QAAS,QAAQxI,QAAQuG,GACpCkC,EAAWD,EAAU,MAAQ,OAC7BE,EAAgBF,EAAU,OAAS,MACnCG,EAAcH,EAAU,SAAW,QACnCI,EAAwBJ,EAAqB,QAAX,SAStC,OAPAD,EAAcE,GAAYJ,EAAiBI,GAAYJ,EAAiBM,GAAe,EAAIL,EAAWK,GAAe,EAEnHJ,EAAcG,GADZnC,IAAcmC,EACeL,EAAiBK,GAAiBJ,EAAWM,GAE7CP,EAAiBL,EAAqBU,IAGhEH,EAYT,SAASM,EAAKC,EAAKC,GAEjB,OAAIC,MAAMC,UAAUJ,KACXC,EAAID,KAAKE,GAIXD,EAAI1B,OAAO2B,GAAO,GAY3B,SAASG,EAAUJ,EAAKK,EAAMC,GAE5B,GAAIJ,MAAMC,UAAUC,UAClB,OAAOJ,EAAII,UAAU,SAAUG,GAC7B,OAAOA,EAAIF,KAAUC,IAKzB,IAAIE,EAAQT,EAAKC,EAAK,SAAUS,GAC9B,OAAOA,EAAIJ,KAAUC,IAEvB,OAAON,EAAI9I,QAAQsJ,GAarB,SAASE,EAAaC,EAAWC,EAAMC,GAmBrC,YAlB8BxH,IAATwH,EAAqBF,EAAYA,EAAUG,MAAM,EAAGV,EAAUO,EAAW,OAAQE,KAEvFE,QAAQ,SAAUlH,GAC3BA,EAASmH,UACXC,QAAQC,KAAK,yDAEf,IAAIC,EAAKtH,EAASmH,UAAYnH,EAASsH,GACnCtH,EAASuH,SAAWlL,EAAWiL,KAIjCP,EAAK3F,QAAQ8B,OAAS/B,EAAc4F,EAAK3F,QAAQ8B,QACjD6D,EAAK3F,QAAQ+B,UAAYhC,EAAc4F,EAAK3F,QAAQ+B,WAEpD4D,EAAOO,EAAGP,EAAM/G,MAIb+G,EAUT,SAASS,IAEP,IAAIC,KAAK3C,MAAM4C,YAAf,CAIA,IAAIX,GACFY,SAAUF,KACVnH,UACAsH,eACAC,cACAC,SAAS,EACT1G,YAIF2F,EAAK3F,QAAQ+B,UAAY0B,EAAoB4C,KAAK3C,MAAO2C,KAAKvE,OAAQuE,KAAKtE,WAK3E4D,EAAKnD,UAAYD,EAAqB8D,KAAKM,QAAQnE,UAAWmD,EAAK3F,QAAQ+B,UAAWsE,KAAKvE,OAAQuE,KAAKtE,UAAWsE,KAAKM,QAAQjB,UAAUkB,KAAK3E,kBAAmBoE,KAAKM,QAAQjB,UAAUkB,KAAK5E,SAG9L2D,EAAKkB,kBAAoBlB,EAAKnD,UAG9BmD,EAAK3F,QAAQ8B,OAASuC,EAAiBgC,KAAKvE,OAAQ6D,EAAK3F,QAAQ+B,UAAW4D,EAAKnD,WACjFmD,EAAK3F,QAAQ8B,OAAOgF,SAAW,WAG/BnB,EAAOF,EAAaY,KAAKX,UAAWC,GAI/BU,KAAK3C,MAAMqD,UAIdV,KAAKM,QAAQK,SAASrB,IAHtBU,KAAK3C,MAAMqD,WAAY,EACvBV,KAAKM,QAAQM,SAAStB,KAY1B,SAASuB,EAAkBxB,EAAWyB,GACpC,OAAOzB,EAAU0B,KAAK,SAAU9E,GAC9B,IAAI+E,EAAO/E,EAAK+E,KAEhB,OADc/E,EAAK6D,SACDkB,IAASF,IAW/B,SAASG,EAAyB/L,GAIhC,IAAK,IAHDgM,IAAY,EAAO,KAAM,SAAU,MAAO,KAC1CC,EAAYjM,EAASkM,OAAO,GAAGC,cAAgBnM,EAASsK,MAAM,GAEzD8B,EAAI,EAAGA,EAAIJ,EAASpJ,OAAS,EAAGwJ,IAAK,CAC5C,IAAIC,EAASL,EAASI,GAClBE,EAAUD,EAAS,GAAKA,EAASJ,EAAYjM,EACjD,GAAmD,oBAAxCG,OAAOQ,SAASC,KAAK2L,MAAMD,GACpC,OAAOA,EAGX,OAAO,KAQT,SAASE,IAmBP,OAlBA1B,KAAK3C,MAAM4C,aAAc,EAGrBY,EAAkBb,KAAKX,UAAW,gBACpCW,KAAKvE,OAAOkG,gBAAgB,eAC5B3B,KAAKvE,OAAOgG,MAAM/I,KAAO,GACzBsH,KAAKvE,OAAOgG,MAAMhB,SAAW,GAC7BT,KAAKvE,OAAOgG,MAAMjJ,IAAM,GACxBwH,KAAKvE,OAAOgG,MAAMR,EAAyB,cAAgB,IAG7DjB,KAAK4B,wBAID5B,KAAKM,QAAQuB,iBACf7B,KAAKvE,OAAOhG,WAAWqM,YAAY9B,KAAKvE,QAEnCuE,KAGT,SAAS+B,EAAsBjH,EAAckH,EAAOC,EAAUC,GAC5D,IAAIC,EAAmC,SAA1BrH,EAAatF,SACtB4M,EAASD,EAAS9M,OAASyF,EAC/BsH,EAAOC,iBAAiBL,EAAOC,GAAYK,SAAS,IAE/CH,GACHJ,EAAsBpM,EAAgByM,EAAO3M,YAAauM,EAAOC,EAAUC,GAE7EA,EAAcK,KAAKH,GASrB,SAASI,EAAoB9G,EAAW4E,EAASjD,EAAOoF,GAEtDpF,EAAMoF,YAAcA,EACpBpN,OAAOgN,iBAAiB,SAAUhF,EAAMoF,aAAeH,SAAS,IAGhE,IAAII,EAAgB/M,EAAgB+F,GAKpC,OAJAqG,EAAsBW,EAAe,SAAUrF,EAAMoF,YAAapF,EAAM6E,eACxE7E,EAAMqF,cAAgBA,EACtBrF,EAAMsF,eAAgB,EAEftF,EAST,SAASuF,IACF5C,KAAK3C,MAAMsF,gBACd3C,KAAK3C,MAAQmF,EAAoBxC,KAAKtE,UAAWsE,KAAKM,QAASN,KAAK3C,MAAO2C,KAAK6C,iBAUpF,SAASC,EAAqBpH,EAAW2B,GAcvC,OAZAhI,OAAO0N,oBAAoB,SAAU1F,EAAMoF,aAG3CpF,EAAM6E,cAAczC,QAAQ,SAAU2C,GACpCA,EAAOW,oBAAoB,SAAU1F,EAAMoF,eAI7CpF,EAAMoF,YAAc,KACpBpF,EAAM6E,iBACN7E,EAAMqF,cAAgB,KACtBrF,EAAMsF,eAAgB,EACftF,EAUT,SAASuE,IACH5B,KAAK3C,MAAMsF,gBACbtN,OAAO2N,qBAAqBhD,KAAK6C,gBACjC7C,KAAK3C,MAAQyF,EAAqB9C,KAAKtE,UAAWsE,KAAK3C,QAW3D,SAAS4F,EAAUC,GACjB,MAAa,KAANA,IAAaC,MAAM3F,WAAW0F,KAAOE,SAASF,GAWvD,SAASG,EAAUpO,EAAS4D,GAC1B0D,OAAOC,KAAK3D,GAAQ4G,QAAQ,SAAUV,GACpC,IAAIuE,EAAO,IAEkE,KAAxE,QAAS,SAAU,MAAO,QAAS,SAAU,QAAQ1N,QAAQmJ,IAAgBkE,EAAUpK,EAAOkG,MACjGuE,EAAO,MAETrO,EAAQwM,MAAM1C,GAAQlG,EAAOkG,GAAQuE,IAYzC,SAASC,EAActO,EAASmL,GAC9B7D,OAAOC,KAAK4D,GAAYX,QAAQ,SAAUV,IAE1B,IADFqB,EAAWrB,GAErB9J,EAAQuO,aAAazE,EAAMqB,EAAWrB,IAEtC9J,EAAQ0M,gBAAgB5C,KAqK9B,SAAS0E,EAAmBpE,EAAWqE,EAAgBC,GACrD,IAAIC,EAAanF,EAAKY,EAAW,SAAUpD,GAEzC,OADWA,EAAK+E,OACA0C,IAGdG,IAAeD,GAAcvE,EAAU0B,KAAK,SAAUxI,GACxD,OAAOA,EAASyI,OAAS2C,GAAiBpL,EAASuH,SAAWvH,EAASzB,MAAQ8M,EAAW9M,QAG5F,IAAK+M,EAAY,CACf,IAAIC,EAAc,IAAMJ,EAAiB,IACrCK,EAAY,IAAMJ,EAAgB,IACtChE,QAAQC,KAAKmE,EAAY,4BAA8BD,EAAc,4DAA8DA,EAAc,KAEnJ,OAAOD,EAyFT,SAASG,EAAqB7G,GAC5B,MAAkB,QAAdA,EACK,QACgB,UAAdA,EACF,MAEFA,EAiDT,SAAS8G,EAAU9H,GACjB,IAAI+H,EAAUrM,UAAUC,OAAS,QAAsBC,IAAjBF,UAAU,IAAmBA,UAAU,GAEzEsM,EAAQC,GAAgBxO,QAAQuG,GAChCuC,EAAM0F,GAAgB5E,MAAM2E,EAAQ,GAAGE,OAAOD,GAAgB5E,MAAM,EAAG2E,IAC3E,OAAOD,EAAUxF,EAAI4F,UAAY5F,EA4InC,SAAS6F,EAAQC,EAAKjG,EAAaJ,EAAeF,GAEhD,IAAIhF,EAAQuL,EAAItF,MAAM,6BAClBF,GAAS/F,EAAM,GACfqK,EAAOrK,EAAM,GAGjB,IAAK+F,EACH,OAAOwF,EAGT,GAA0B,IAAtBlB,EAAK1N,QAAQ,KAAY,CAC3B,IAAIX,OAAU,EACd,OAAQqO,GACN,IAAK,KACHrO,EAAUkJ,EACV,MACF,IAAK,IACL,IAAK,KACL,QACElJ,EAAUgJ,EAId,OADWvE,EAAczE,GACbsJ,GAAe,IAAMS,EAC5B,GAAa,OAATsE,GAA0B,OAATA,EAAe,CAQzC,OALa,OAATA,EACKlK,KAAKC,IAAIxD,SAASS,gBAAgB4D,aAAc7E,OAAOiG,aAAe,GAEtElC,KAAKC,IAAIxD,SAASS,gBAAgB2D,YAAa5E,OAAOgG,YAAc,IAE/D,IAAM2D,EAIpB,OAAOA,EAeX,SAASyF,EAAYC,EAAQvG,EAAeF,EAAkB0G,GAC5D,IAAIhL,GAAW,EAAG,GAKdiL,GAA0D,KAA7C,QAAS,QAAQhP,QAAQ+O,GAItCE,EAAYH,EAAOzL,MAAM,WAAWwD,IAAI,SAAUqI,GACpD,OAAOA,EAAKC,SAKVC,EAAUH,EAAUjP,QAAQ6I,EAAKoG,EAAW,SAAUC,GACxD,OAAgC,IAAzBA,EAAKG,OAAO,WAGjBJ,EAAUG,KAAiD,IAArCH,EAAUG,GAASpP,QAAQ,MACnD+J,QAAQC,KAAK,gFAKf,IAAIsF,EAAa,cACbC,GAAmB,IAAbH,GAAkBH,EAAUrF,MAAM,EAAGwF,GAASX,QAAQQ,EAAUG,GAAS/L,MAAMiM,GAAY,MAAOL,EAAUG,GAAS/L,MAAMiM,GAAY,IAAIb,OAAOQ,EAAUrF,MAAMwF,EAAU,MAAQH,GAqC9L,OAlCAM,EAAMA,EAAI1I,IAAI,SAAU2I,EAAIjB,GAE1B,IAAI5F,GAAyB,IAAV4F,GAAeS,EAAYA,GAAa,SAAW,QAClES,GAAoB,EACxB,OAAOD,EAGNE,OAAO,SAAUzI,EAAGC,GACnB,MAAwB,KAApBD,EAAEA,EAAE/E,OAAS,KAAwC,KAA1B,IAAK,KAAKlC,QAAQkH,IAC/CD,EAAEA,EAAE/E,OAAS,GAAKgF,EAClBuI,GAAoB,EACbxI,GACEwI,GACTxI,EAAEA,EAAE/E,OAAS,IAAMgF,EACnBuI,GAAoB,EACbxI,GAEAA,EAAEwH,OAAOvH,QAInBL,IAAI,SAAU+H,GACb,OAAOD,EAAQC,EAAKjG,EAAaJ,EAAeF,QAKhDwB,QAAQ,SAAU2F,EAAIjB,GACxBiB,EAAG3F,QAAQ,SAAUqF,EAAMS,GACrBtC,EAAU6B,KACZnL,EAAQwK,IAAUW,GAA2B,MAAnBM,EAAGG,EAAS,IAAc,EAAI,QAIvD5L,+CAhmDT,IAAK,ICjCC6L,EAAQ,oBAqBHC,EAAOtG,YACJrK,SAASC,KAAKoK,GAAKD,MAAM,iBAAiB,GAAGwG,uBAGhDC,oBAEKC,EAAWzO,iBACPyO,EAAWzO,WAFpB,SAGE6K,MACD6D,EAAE7D,EAAMI,QAAQ0D,GAAG9F,aACdgC,EAAM+D,UAAUC,QAAQC,MAAMjG,KAAMnI,sBAO1CqO,OACH7Q,OAAO8Q,aACF,MAGHC,EAAKvQ,SAASwQ,cAAc,iBAE7B,IAAMrF,KAAQsF,KACa,oBAAnBF,EAAG3E,MAAMT,cAEXsF,EAAmBtF,WAKvB,WAGAuF,EAAsBC,cACzBC,GAAS,WAEXzG,MAAM0G,IAAIlB,EAAKmB,eAAgB,cACtB,eAGA,WACJF,KACEG,qBAALC,IAEDL,GAEIxG,SA5DL4F,GAAa,EAIXU,oBACe,oCACA,4BACA,2CACA,iBAwEfd,kBAEY,yBAFL,SAIJjE,YAlFO,IAqFGnI,KAAK0N,gBACXjR,SAASkR,eAAexF,WAC1BA,0BATE,SAYYtM,OACjB+R,EAAW/R,EAAQgS,aAAa,eAC/BD,GAAyB,MAAbA,MACJ/R,EAAQgS,aAAa,SAAW,eAIzBpB,EAAEhQ,UAAU4I,KAAKuI,GAClBlP,OAAS,EAAIkP,EAAW,KACzC,MAAOE,UACA,cAtBA,SA0BJjS,UACEA,EAAQqF,mCA3BN,SA8BUrF,KACjBA,GAASkS,QAAQvB,EAAWzO,4BA/BrB,kBAmCFiQ,QAAQxB,cAnCN,SAsCDzG,UACAA,EAAI,IAAMA,GAAKhK,0BAvCd,SA0CKkS,EAAeC,EAAQC,OAChC,IAAMrS,KAAYqS,KACjBhL,OAAOsC,UAAU2I,eAAezS,KAAKwS,EAAarS,GAAW,KACzDuS,EAAgBF,EAAYrS,GAC5B8J,EAAgBsI,EAAOpS,GACvBwS,EAAgB1I,GAASwG,EAAKmC,UAAU3I,GACxB,UAAYyG,EAAOzG,OAEpC,IAAI4I,OAAOH,GAAevR,KAAKwR,SAC5B,IAAIG,MACLR,EAAchG,cAAjB,aACWnM,EADX,oBACuCwS,EADvC,wBAEsBD,EAFtB,kBApEGvB,MAEXrG,GAAGiI,qBAAuBvB,EAExBf,EAAKuC,4BACL/F,MAAMgG,QAAQxC,EAAKmB,gBAAkBhB,KA0EpCH,EAxJK,6JCERyC,EAAS,eASPC,EAAsB,QAKtBC,EAAsBtC,EAAEhG,GAAGqI,GAO3BE,6FAMAC,SACI,aACA,YACA,QAUJJ,wBAEQhT,QACLqT,SAAWrT,6BAalBsT,MAxDiB,SAwDXtT,KACMA,GAAW+K,KAAKsI,aAEpBE,EAAcxI,KAAKyI,gBAAgBxT,GACrB+K,KAAK0I,mBAAmBF,GAE5BG,2BAIXC,eAAeJ,MAGtBK,QArEiB,aAsEbC,WAAW9I,KAAKsI,SA3DM,iBA4DnBA,SAAW,QAMlBG,gBA7EiB,SA6EDxT,OACR+R,EAAWxB,EAAKuD,uBAAuB9T,GACzCwF,GAAa,SAEbuM,MACOnB,EAAEmB,GAAU,IAGlBvM,MACMoL,EAAE5Q,GAAS+T,QAAX,IAAuBX,EAAUY,OAAS,IAG9CxO,KAGTiO,mBA5FiB,SA4FEzT,OACXiU,EAAarD,EAAEuC,MAAMA,EAAMe,gBAE/BlU,GAASkS,QAAQ+B,GACZA,KAGTN,eAnGiB,SAmGF3T,gBACXA,GAASmU,YAAYf,EAAUgB,MAE5B7D,EAAKuC,yBACLlC,EAAE5Q,GAASqU,SAASjB,EAAUkB,QAKjCtU,GACCyR,IAAIlB,EAAKmB,eAAgB,SAAC3E,UAAU6E,EAAK2C,gBAAgBvU,EAAS+M,KAClE8F,qBA/FqB,UAyFjB0B,gBAAgBvU,MASzBuU,gBAjHiB,SAiHDvU,KACZA,GACCwU,SACAtC,QAAQiB,EAAMsB,QACdC,YAMEC,iBA3HU,SA2HOtC,UACftH,KAAK6J,KAAK,eACTC,EAAWjE,EAAE7F,MACfV,EAAawK,EAASxK,KAnHJ,YAqHjBA,MACI,IAAI2I,EAAMjI,QACRV,KAvHW,WAuHIA,IAGX,UAAXgI,KACGA,GAAQtH,WAKZ+J,eA3IU,SA2IKC,UACb,SAAUhI,GACXA,KACIiI,mBAGM1B,MAAMvI,sDAvIE,iCAoJ1BnK,UAAUqU,GACV9B,EAAM+B,wBA7II,0BA8IDC,QACTnC,EAAM8B,eAAe,IAAI9B,MAUzBpI,GAAGqI,GAAoBD,EAAM2B,mBAC7B/J,GAAGqI,GAAMmC,YAAcpC,IACvBpI,GAAGqI,GAAMoC,WAAc,oBACrBzK,GAAGqI,GAAQC,EACNF,EAAM2B,kBAGR3B,EAlLM,GCHTsC,EAAU,eASRrC,EAAsB,SAKtBC,EAAsBtC,EAAEhG,GAAGqI,GAE3BG,UACK,gBACA,YACA,SAGLmC,sBACiB,sCACA,gCACA,eACA,iBACA,QAGjBpC,iEAEkB,oDAWlBmC,wBAEQtV,QACLqT,SAAWrT,6BAalBwV,OA3DkB,eA4DZC,GAAqB,EACrBC,GAAiB,EACfnC,EAAmB3C,EAAE7F,KAAKsI,UAAUU,QACxCwB,EAASI,aACT,MAEEpC,EAAa,KACTqC,EAAQhF,EAAE7F,KAAKsI,UAAU7J,KAAK+L,EAASM,OAAO,MAEhDD,EAAO,IACU,UAAfA,EAAME,QACJF,EAAMG,SACRnF,EAAE7F,KAAKsI,UAAUgB,SAASjB,EAAU4C,WACf,MAEhB,KACCC,EAAgBrF,EAAE2C,GAAa/J,KAAK+L,EAASS,QAAQ,GAEvDC,KACAA,GAAe9B,YAAYf,EAAU4C,WAKzCP,EAAoB,IAClBG,EAAMM,aAAa,aACrB3C,EAAY2C,aAAa,aACzBN,EAAMO,UAAU3T,SAAS,aACzB+Q,EAAY4C,UAAU3T,SAAS,qBAG3BuT,SAAWnF,EAAE7F,KAAKsI,UAAUgB,SAASjB,EAAU4C,UACnDJ,GAAO1D,QAAQ,YAGbkE,WACW,GAKjBV,QACGrC,SAAS9E,aAAa,gBACxBqC,EAAE7F,KAAKsI,UAAUgB,SAASjB,EAAU4C,SAGrCP,KACA1K,KAAKsI,UAAUgD,YAAYjD,EAAU4C,WAI3CpC,QA/GkB,aAgHdC,WAAW9I,KAAKsI,SArGM,kBAsGnBA,SAAW,QAMXsB,iBAvHW,SAuHMtC,UACftH,KAAK6J,KAAK,eACXvK,EAAOuG,EAAE7F,MAAMV,KA9GG,aAgHjBA,MACI,IAAIiL,EAAOvK,QAChBA,MAAMV,KAlHY,YAkHGA,IAGV,WAAXgI,KACGA,sDAvHe,iCAqI1BzR,UACCqU,GAAG9B,EAAM+B,eAAgBK,EAASe,mBAAoB,SAACvJ,KAChDiI,qBAEFuB,EAASxJ,EAAMI,OAEdyD,EAAE2F,GAAQlC,SAASjB,EAAUoD,YACvB5F,EAAE2F,GAAQxC,QAAQwB,EAASiB,WAG/B7B,iBAAiB7U,KAAK8Q,EAAE2F,GAAS,YAEzCtB,GAAG9B,EAAMsD,oBAAqBlB,EAASe,mBAAoB,SAACvJ,OACrDwJ,EAAS3F,EAAE7D,EAAMI,QAAQ4G,QAAQwB,EAASiB,QAAQ,KACtDD,GAAQF,YAAYjD,EAAUsD,MAAO,eAAezV,KAAK8L,EAAM+I,WAUnElL,GAAGqI,GAAoBqC,EAAOX,mBAC9B/J,GAAGqI,GAAMmC,YAAcE,IACvB1K,GAAGqI,GAAMoC,WAAc,oBACrBzK,GAAGqI,GAAQC,EACNoC,EAAOX,kBAGTW,EA9KO,GCGVqB,EAAY,eASV1D,EAAyB,WAEzB2D,EAAyB,cACzBC,EAAAA,IAA6BD,EAE7B1D,EAAyBtC,EAAEhG,GAAGqI,GAM9B6D,YACO,cACA,SACA,QACA,cACA,GAGPC,YACO,4BACA,gBACA,yBACA,wBACA,WAGPC,QACO,YACA,YACA,aACA,SAGP7D,iBACqB0D,cACDA,oBACGA,0BACGA,0BACAA,sBACFA,yFAKxBzD,YACO,kBACA,eACA,cACA,2BACA,0BACA,0BACA,0BACA,iBAGPmC,UACU,sBACA,6BACA,2BACA,sDACA,kCACA,0CACA,0BAUVoB,wBAEQ3W,EAASqS,QACd4E,OAAqB,UACrBC,UAAqB,UACrBC,eAAqB,UAErBC,WAAqB,OACrBC,YAAqB,OAErBC,aAAqB,UAErBC,QAAqBxM,KAAKyM,WAAWnF,QACrCgB,SAAqBzC,EAAE5Q,GAAS,QAChCyX,mBAAqB7G,EAAE7F,KAAKsI,UAAU7J,KAAK+L,EAASmC,YAAY,QAEhEC,gDAiBPC,KAnHoB,WAoHb7M,KAAKsM,iBACHQ,OAAOb,EAAUc,SAI1BC,gBAzHoB,YA4HbnX,SAASoX,QACXpH,EAAE7F,KAAKsI,UAAUxC,GAAG,aAAsD,WAAvCD,EAAE7F,KAAKsI,UAAUlT,IAAI,oBACpDyX,UAITK,KAlIoB,WAmIblN,KAAKsM,iBACHQ,OAAOb,EAAUkB,SAI1BC,MAxIoB,SAwIdpL,GACCA,SACEqK,WAAY,GAGfxG,EAAE7F,KAAKsI,UAAU7J,KAAK+L,EAAS6C,WAAW,IAC5C7H,EAAKuC,4BACAnB,qBAAqB5G,KAAKsI,eAC1BgF,OAAM,kBAGCtN,KAAKmM,gBACdA,UAAY,QAGnBmB,MAvJoB,SAuJdtL,GACCA,SACEqK,WAAY,GAGfrM,KAAKmM,0BACOnM,KAAKmM,gBACdA,UAAY,MAGfnM,KAAKwM,QAAQe,WAAavN,KAAKqM,iBAC5BF,UAAYqB,aACd3X,SAAS4X,gBAAkBzN,KAAKgN,gBAAkBhN,KAAK6M,MAAMa,KAAK1N,MACnEA,KAAKwM,QAAQe,cAKnBI,GAzKoB,SAyKjBxJ,mBACIiI,eAAiBvG,EAAE7F,KAAKsI,UAAU7J,KAAK+L,EAASoD,aAAa,OAE5DC,EAAc7N,KAAK8N,cAAc9N,KAAKoM,qBAExCjI,EAAQnE,KAAKkM,OAAOpU,OAAS,GAAKqM,EAAQ,MAI1CnE,KAAKsM,aACLtM,KAAKsI,UAAU5B,IAAI0B,EAAM2F,KAAM,kBAAMlH,EAAK8G,GAAGxJ,aAI7C0J,IAAgB1J,cACbiJ,kBACAE,YAIDU,EAAY7J,EAAQ0J,EACxB5B,EAAUc,KACVd,EAAUkB,UAEPL,OAAOkB,EAAWhO,KAAKkM,OAAO/H,QAGrC0E,QApMoB,aAqMhB7I,KAAKsI,UAAU2F,IAAInC,KACnBhD,WAAW9I,KAAKsI,SAAUuD,QAEvBK,OAAqB,UACrBM,QAAqB,UACrBlE,SAAqB,UACrB6D,UAAqB,UACrBE,UAAqB,UACrBC,WAAqB,UACrBF,eAAqB,UACrBM,mBAAqB,QAM5BD,WArNoB,SAqNTnF,YACAzB,EAAEqI,UAAWnC,EAASzE,KAC1B6G,gBAAgBjG,EAAMZ,EAAQ0E,GAC5B1E,KAGTsF,mBA3NoB,sBA4Nd5M,KAAKwM,QAAQ4B,YACbpO,KAAKsI,UACJ4B,GAAG9B,EAAMiG,QAAS,SAACrM,UAAUsM,EAAKC,SAASvM,KAGrB,UAAvBhC,KAAKwM,QAAQY,UACbpN,KAAKsI,UACJ4B,GAAG9B,EAAMoG,WAAY,SAACxM,UAAUsM,EAAKlB,MAAMpL,KAC3CkI,GAAG9B,EAAMqG,WAAY,SAACzM,UAAUsM,EAAKhB,MAAMtL,KAC1C,iBAAkBnM,SAASS,mBAQ3B0J,KAAKsI,UAAU4B,GAAG9B,EAAMsG,SAAU,aAC7BtB,QACDkB,EAAK/B,2BACM+B,EAAK/B,gBAEfA,aAAeoC,WAAW,SAAC3M,UAAUsM,EAAKhB,MAAMtL,IAhOhC,IAgOiEsM,EAAK9B,QAAQe,gBAM3GgB,SAxPoB,SAwPXvM,OACH,kBAAkB9L,KAAK8L,EAAMI,OAAOwM,gBAIhC5M,EAAM6M,YA7Oa,KA+OjB5E,sBACDiD,kBA/OkB,KAkPjBjD,sBACD4C,gCAOXiB,cA3QoB,SA2QN7Y,eACPiX,OAASrG,EAAEiJ,UAAUjJ,EAAE5Q,GAASwF,SAASgE,KAAK+L,EAASuE,OACrD/O,KAAKkM,OAAOtW,QAAQX,MAG7B+Z,oBAhRoB,SAgRAhB,EAAW9C,OACvB+D,EAAkBjB,IAAc/B,EAAUc,KAC1CmC,EAAkBlB,IAAc/B,EAAUkB,KAC1CU,EAAkB7N,KAAK8N,cAAc5C,GACrCiE,EAAkBnP,KAAKkM,OAAOpU,OAAS,MACrBoX,GAAmC,IAAhBrB,GACnBoB,GAAmBpB,IAAgBsB,KAErCnP,KAAKwM,QAAQ4C,YAC1BlE,MAIHmE,GAAaxB,GADDG,IAAc/B,EAAUkB,MAAQ,EAAI,IACZnN,KAAKkM,OAAOpU,cAEhC,IAAfuX,EACLrP,KAAKkM,OAAOlM,KAAKkM,OAAOpU,OAAS,GAAKkI,KAAKkM,OAAOmD,MAItDC,mBApSoB,SAoSDC,EAAeC,OAC1BC,EAAczP,KAAK8N,cAAcyB,GACjCG,EAAY1P,KAAK8N,cAAcjI,EAAE7F,KAAKsI,UAAU7J,KAAK+L,EAASoD,aAAa,IAC3E+B,EAAa9J,EAAEuC,MAAMA,EAAMwH,iCAEpBJ,OACLE,KACFD,aAGJzP,KAAKsI,UAAUnB,QAAQwI,GAElBA,KAGTE,2BAnToB,SAmTO5a,MACrB+K,KAAK0M,mBAAoB,GACzB1M,KAAK0M,oBACJjO,KAAK+L,EAASS,QACd7B,YAAYf,EAAU4C,YAEnB6E,EAAgB9P,KAAK0M,mBAAmBlS,SAC5CwF,KAAK8N,cAAc7Y,IAGjB6a,KACAA,GAAeC,SAAS1H,EAAU4C,YAK1C6B,OAnUoB,SAmUbkB,EAAW/Y,OAQZ+a,EACAC,EACAT,SATEtE,EAAgBrF,EAAE7F,KAAKsI,UAAU7J,KAAK+L,EAASoD,aAAa,GAC5DsC,EAAqBlQ,KAAK8N,cAAc5C,GACxCiF,EAAgBlb,GAAWiW,GAC/BlL,KAAKgP,oBAAoBhB,EAAW9C,GAChCkF,EAAmBpQ,KAAK8N,cAAcqC,GACtCE,EAAYjJ,QAAQpH,KAAKmM,cAM3B6B,IAAc/B,EAAUc,QACH1E,EAAUiI,OAChBjI,EAAU0E,OACNd,EAAUqE,SAERjI,EAAUkI,QAChBlI,EAAU8E,OACNlB,EAAUsE,OAG7BJ,GAAetK,EAAEsK,GAAa7G,SAASjB,EAAU4C,aAC9CqB,YAAa,WAIDtM,KAAKsP,mBAAmBa,EAAaX,GACzC7G,sBAIVuC,GAAkBiF,QAKlB7D,YAAa,EAEd+D,QACGjD,aAGFyC,2BAA2BM,OAE1BK,EAAY3K,EAAEuC,MAAMA,EAAM2F,oBACfoC,YACJX,OACLU,KACFE,IAGF5K,EAAKuC,yBACPlC,EAAE7F,KAAKsI,UAAUgB,SAASjB,EAAUuH,UAElCO,GAAaJ,SAASE,KAEnBQ,OAAON,KAEVjF,GAAe6E,SAASC,KACxBG,GAAaJ,SAASC,KAEtB9E,GACCxE,IAAIlB,EAAKmB,eAAgB,aACtBwJ,GACC/G,YAAe4G,EADlB,IAC0CC,GACvCF,SAAS1H,EAAU4C,UAEpBC,GAAe9B,YAAef,EAAU4C,OAA1C,IAAoDgF,EAApD,IAAsED,KAEjE1D,YAAa,aAEP,kBAAMzG,EAAE6K,EAAKpI,UAAUnB,QAAQqJ,IAAY,KAGvD1I,qBA/XsB,SAkYvBoD,GAAe9B,YAAYf,EAAU4C,UACrCkF,GAAaJ,SAAS1H,EAAU4C,aAE7BqB,YAAa,IAChBtM,KAAKsI,UAAUnB,QAAQqJ,IAGvBH,QACG/C,YAOF1D,iBAhaa,SAgaItC,UACftH,KAAK6J,KAAK,eACXvK,EAAYuG,EAAE7F,MAAMV,KAAKuM,GACvBW,EAAU3G,EAAEqI,UAAWnC,EAASlG,EAAE7F,MAAMV,QAExB,iBAAXgI,KACP4G,OAAO1B,EAASlF,OAGdqJ,EAA2B,iBAAXrJ,EAAsBA,EAASkF,EAAQoE,SAExDtR,MACI,IAAIsM,EAAS5L,KAAMwM,KACxBxM,MAAMV,KAAKuM,EAAUvM,IAGH,iBAAXgI,IACJqG,GAAGrG,QACH,GAAsB,iBAAXqJ,EAAqB,IACT,oBAAjBrR,EAAKqR,SACR,IAAI9I,MAAJ,oBAA8B8I,EAA9B,OAEHA,UACInE,EAAQe,aACZH,UACAE,cAKJuD,qBA9ba,SA8bQ7O,OACpBgF,EAAWxB,EAAKuD,uBAAuB/I,SAExCgH,OAIC5E,EAASyD,EAAEmB,GAAU,MAEtB5E,GAAWyD,EAAEzD,GAAQkH,SAASjB,EAAUyI,eAIvCxJ,EAAazB,EAAEqI,UAAWrI,EAAEzD,GAAQ9C,OAAQuG,EAAE7F,MAAMV,QACpDyR,EAAa/Q,KAAKiH,aAAa,iBAEjC8J,MACKxD,UAAW,KAGX3D,iBAAiB7U,KAAK8Q,EAAEzD,GAASkF,GAEtCyJ,KACA3O,GAAQ9C,KAAKuM,GAAU8B,GAAGoD,KAGxB9G,kEA9cqB,sDAmGpB8B,oBAuXTlW,UACCqU,GAAG9B,EAAM+B,eAAgBK,EAASwG,WAAYpF,EAASiF,wBAExDxb,QAAQ6U,GAAG9B,EAAM6I,cAAe,aAC9BzG,EAAS0G,WAAWrH,KAAK,eACnBsH,EAAYtL,EAAE7F,QACX4J,iBAAiB7U,KAAKoc,EAAWA,EAAU7R,cAWtDO,GAAGqI,GAAoB0D,EAAShC,mBAChC/J,GAAGqI,GAAMmC,YAAcuB,IACvB/L,GAAGqI,GAAMoC,WAAc,oBACrBzK,GAAGqI,GAAQC,EACNyD,EAAShC,kBAGXgC,EA5fS,GCAZwF,GAAY,eASVlJ,EAAsB,WAEtB2D,EAAsB,cAGtB1D,EAAsBtC,EAAEhG,GAAGqI,GAG3B6D,WACK,SACA,IAGLC,UACK,iBACA,oBAGL5D,sJAQAC,QACS,gBACA,sBACA,uBACA,aAGTgJ,SACK,eACA,UAGL7G,WACU,iCACA,4BAUV4G,wBAEQnc,EAASqS,QACdgK,kBAAmB,OACnBhJ,SAAmBrT,OACnBuX,QAAmBxM,KAAKyM,WAAWnF,QACnCiK,cAAmB1L,EAAEiJ,UAAUjJ,EAClC,mCAAmC5Q,EAAQuc,GAA3C,6CAC0Cvc,EAAQuc,GADlD,WAIG,IADCC,EAAa5L,EAAE2E,EAASI,aACrBtJ,EAAI,EAAGA,EAAImQ,EAAW3Z,OAAQwJ,IAAK,KACpCoQ,EAAOD,EAAWnQ,GAClB0F,EAAWxB,EAAKuD,uBAAuB2I,GAC5B,OAAb1K,GAAqBnB,EAAEmB,GAAUhK,OAAO/H,GAAS6C,OAAS,QACvDyZ,cAAchP,KAAKmP,QAIvBC,QAAU3R,KAAKwM,QAAQ/R,OAASuF,KAAK4R,aAAe,KAEpD5R,KAAKwM,QAAQ/R,aACXoX,0BAA0B7R,KAAKsI,SAAUtI,KAAKuR,eAGjDvR,KAAKwM,QAAQ/B,aACVA,oCAkBTA,OAvGoB,WAwGd5E,EAAE7F,KAAKsI,UAAUgB,SAASjB,EAAUgB,WACjCyI,YAEAC,UAITA,KA/GoB,0BAgHd/R,KAAKsR,mBACPzL,EAAE7F,KAAKsI,UAAUgB,SAASjB,EAAUgB,WAIlC2I,EACAC,KAEAjS,KAAK2R,aACG9L,EAAEiJ,UAAUjJ,EAAE7F,KAAK2R,SAASnX,WAAWA,SAASgQ,EAAS0H,WACtDpa,WACD,SAIVka,MACYnM,EAAEmM,GAAS1S,KAAKuM,KACXoG,EAAYX,uBAK3Ba,EAAatM,EAAEuC,MAAMA,EAAMiB,WAC/BrJ,KAAKsI,UAAUnB,QAAQgL,IACrBA,EAAWxJ,sBAIXqJ,MACOpI,iBAAiB7U,KAAK8Q,EAAEmM,GAAU,QACtCC,KACDD,GAAS1S,KAAKuM,EAAU,WAIxBuG,EAAYpS,KAAKqS,kBAErBrS,KAAKsI,UACJc,YAAYf,EAAUiK,UACtBvC,SAAS1H,EAAUkK,iBAEjBjK,SAAS7G,MAAM2Q,GAAa,EAE7BpS,KAAKuR,cAAczZ,UACnBkI,KAAKuR,eACJnI,YAAYf,EAAUmK,WACtBC,KAAK,iBAAiB,QAGtBC,kBAAiB,OAEhBC,EAAW,aACb9L,EAAKyB,UACJc,YAAYf,EAAUkK,YACtBxC,SAAS1H,EAAUiK,UACnBvC,SAAS1H,EAAUgB,QAEjBf,SAAS7G,MAAM2Q,GAAa,KAE5BM,kBAAiB,KAEpB7L,EAAKyB,UAAUnB,QAAQiB,EAAMwK,WAG5BpN,EAAKuC,6BAMJ8K,EAAAA,UADuBT,EAAU,GAAG/Q,cAAgB+Q,EAAU5S,MAAM,MAGxEQ,KAAKsI,UACJ5B,IAAIlB,EAAKmB,eAAgBgM,GACzB7K,qBA3KqB,UA6KnBQ,SAAS7G,MAAM2Q,GAAgBpS,KAAKsI,SAASuK,GAAlD,oBAGFf,KA/LoB,0BAgMd9R,KAAKsR,kBACNzL,EAAE7F,KAAKsI,UAAUgB,SAASjB,EAAUgB,WAIjC8I,EAAatM,EAAEuC,MAAMA,EAAM0K,WAC/B9S,KAAKsI,UAAUnB,QAAQgL,IACrBA,EAAWxJ,0BAITyJ,EAAkBpS,KAAKqS,wBAExB/J,SAAS7G,MAAM2Q,GAAgBpS,KAAKsI,SAASzO,wBAAwBuY,GAA1E,OAEK3B,OAAOzQ,KAAKsI,YAEftI,KAAKsI,UACJyH,SAAS1H,EAAUkK,YACnBnJ,YAAYf,EAAUiK,UACtBlJ,YAAYf,EAAUgB,MAErBrJ,KAAKuR,cAAczZ,WAChB,IAAIwJ,EAAI,EAAGA,EAAItB,KAAKuR,cAAczZ,OAAQwJ,IAAK,KAC5C6F,EAAUnH,KAAKuR,cAAcjQ,GAC7B0F,EAAWxB,EAAKuD,uBAAuB5B,GAC5B,OAAbH,IACYnB,EAAEmB,GACLsC,SAASjB,EAAUgB,SAC1BlC,GAAS4I,SAAS1H,EAAUmK,WACxBC,KAAK,iBAAiB,SAM/BC,kBAAiB,OAEhBC,EAAW,aACVD,kBAAiB,KACpBpE,EAAKhG,UACJc,YAAYf,EAAUkK,YACtBxC,SAAS1H,EAAUiK,UACnBnL,QAAQiB,EAAM2K,cAGdzK,SAAS7G,MAAM2Q,GAAa,GAE5B5M,EAAKuC,0BAKR/H,KAAKsI,UACJ5B,IAAIlB,EAAKmB,eAAgBgM,GACzB7K,qBAxOqB,cA2O1B4K,iBA1PoB,SA0PHM,QACV1B,iBAAmB0B,KAG1BnK,QA9PoB,aA+PhBC,WAAW9I,KAAKsI,SAAUuD,QAEvBW,QAAmB,UACnBmF,QAAmB,UACnBrJ,SAAmB,UACnBiJ,cAAmB,UACnBD,iBAAmB,QAM1B7E,WA3QoB,SA2QTnF,YACAzB,EAAEqI,UAAWnC,EAASzE,KACxBmD,OAASrD,QAAQE,EAAOmD,UAC1B0D,gBAAgBjG,EAAMZ,EAAQ0E,GAC5B1E,KAGT+K,cAlRoB,kBAmRDxM,EAAE7F,KAAKsI,UAAUgB,SAAS+H,EAAU4B,OACnC5B,EAAU4B,MAAQ5B,EAAU6B,UAGhDtB,WAvRoB,sBAwRdnX,EAAS,KACT+K,EAAKmC,UAAU3H,KAAKwM,QAAQ/R,WACrBuF,KAAKwM,QAAQ/R,OAGoB,oBAA/BuF,KAAKwM,QAAQ/R,OAAO0Y,WACpBnT,KAAKwM,QAAQ/R,OAAO,OAGtBoL,EAAE7F,KAAKwM,QAAQ/R,QAAQ,OAG5BuM,EAAAA,yCACqChH,KAAKwM,QAAQ/R,OADlD,cAGJA,GAAQgE,KAAKuI,GAAU6C,KAAK,SAACvI,EAAGrM,KAC3B4c,0BACHT,EAASgC,sBAAsBne,IAC9BA,MAIEwF,KAGToX,0BAjToB,SAiTM5c,EAASoe,MAC7Bpe,EAAS,KACLqe,EAASzN,EAAE5Q,GAASqU,SAASjB,EAAUgB,MAEzCgK,EAAavb,UACbub,GACC/H,YAAYjD,EAAUmK,WAAYc,GAClCb,KAAK,gBAAiBa,OAQxBF,sBAhUa,SAgUSne,OACrB+R,EAAWxB,EAAKuD,uBAAuB9T,UACtC+R,EAAWnB,EAAEmB,GAAU,GAAK,QAG9B4C,iBArUa,SAqUItC,UACftH,KAAK6J,KAAK,eACT0J,EAAU1N,EAAE7F,MACdV,EAAYiU,EAAMjU,KAAKuM,GACrBW,EAAU3G,EAAEqI,UAEhBnC,EACAwH,EAAMjU,OACY,iBAAXgI,GAAuBA,OAG3BhI,GAAQkN,EAAQ/B,QAAU,YAAYvU,KAAKoR,OACtCmD,QAAS,GAGdnL,MACI,IAAI8R,EAASpR,KAAMwM,KACpBlN,KAAKuM,EAAUvM,IAGD,iBAAXgI,EAAqB,IACF,oBAAjBhI,EAAKgI,SACR,IAAIO,MAAJ,oBAA8BP,EAA9B,OAEHA,uDAnVe,sDAuFjByE,oBA0QTlW,UAAUqU,GAAG9B,EAAM+B,eAAgBK,EAASI,YAAa,SAAU5I,GAE/B,MAAhCA,EAAMwR,cAAc5E,WAChB3E,qBAGFwJ,EAAW5N,EAAE7F,MACbgH,EAAWxB,EAAKuD,uBAAuB/I,QAC3CgH,GAAU6C,KAAK,eACT6J,EAAU7N,EAAE7F,MAEZsH,EADUoM,EAAQpU,KAAKuM,GACN,SAAW4H,EAASnU,SAClCsK,iBAAiB7U,KAAK2e,EAASpM,SAW1CzH,GAAGqI,GAAoBkJ,EAASxH,mBAChC/J,GAAGqI,GAAMmC,YAAc+G,IACvBvR,GAAGqI,GAAMoC,WAAc,oBACrBzK,GAAGqI,GAAQC,EACNiJ,EAASxH,kBAGXwH,EAzYS,GLaduC,IAAe,cAAe,wCAe9BC,GAA8B,oBAAXve,OACnBwe,IAAyB,OAAQ,UAAW,WAC5CC,GAAkB,EACbxS,GAAI,EAAGA,GAAIuS,GAAsB/b,OAAQwJ,IAAK,EACrD,GAAIsS,IAAaG,UAAUC,UAAUpe,QAAQie,GAAsBvS,MAAO,EAAG,CAC3EwS,GAAkB,EAClB,MA6CJ,IAWIG,GAXiCL,IAzDzB,SAAc/T,GACxB,OAAO8T,GAAY5S,KAAK,SAAUmT,GAChC,OAAQrU,GAAM,IAAI/K,WAAWc,QAAQse,IAAS,IAuDAC,CAAS9e,OAAO+e,kBAzClE,SAA2BvU,GACzB,IAAIwU,GAAY,EACZ/S,EAAI,EACJoQ,EAAO7b,SAASwQ,cAAc,QAYlC,OAPe,IAAI+N,iBAAiB,WAClCvU,IACAwU,GAAY,IAGLC,QAAQ5C,GAAQtR,YAAY,IAE9B,WACAiU,IACHA,GAAY,EACZ3C,EAAKlO,aAAa,UAAWlC,GAC7BA,GAAQ,KAKd,SAAsBzB,GACpB,IAAIwU,GAAY,EAChB,OAAO,WACAA,IACHA,GAAY,EACZ1F,WAAW,WACT0F,GAAY,EACZxU,KACCiU,OA4PLpZ,QAAS3C,EAETuB,GAAW,WAIb,YAHevB,IAAX2C,KACFA,IAAsD,IAA7CqZ,UAAUQ,WAAW3e,QAAQ,YAEjC8E,IAkBL8Z,GAAiB,SAAUtU,EAAUmK,GACvC,KAAMnK,aAAoBmK,GACxB,MAAM,IAAIoK,UAAU,sCAIpBC,GAAc,WAChB,SAASC,EAAiBvS,EAAQwS,GAChC,IAAK,IAAItT,EAAI,EAAGA,EAAIsT,EAAM9c,OAAQwJ,IAAK,CACrC,IAAIuT,EAAaD,EAAMtT,GACvBuT,EAAWC,WAAaD,EAAWC,aAAc,EACjDD,EAAWE,cAAe,EACtB,UAAWF,IAAYA,EAAWG,UAAW,GACjDzY,OAAO0Y,eAAe7S,EAAQyS,EAAWnY,IAAKmY,IAIlD,OAAO,SAAUxK,EAAa6K,EAAYC,GAGxC,OAFID,GAAYP,EAAiBtK,EAAYxL,UAAWqW,GACpDC,GAAaR,EAAiBtK,EAAa8K,GACxC9K,GAdO,GAsBd4K,GAAiB,SAAU9V,EAAKzC,EAAKsC,GAYvC,OAXItC,KAAOyC,EACT5C,OAAO0Y,eAAe9V,EAAKzC,GACzBsC,MAAOA,EACP8V,YAAY,EACZC,cAAc,EACdC,UAAU,IAGZ7V,EAAIzC,GAAOsC,EAGNG,GAGLvF,GAAW2C,OAAO6Y,QAAU,SAAUhT,GACxC,IAAK,IAAId,EAAI,EAAGA,EAAIzJ,UAAUC,OAAQwJ,IAAK,CACzC,IAAI+T,EAASxd,UAAUyJ,GAEvB,IAAK,IAAI5E,KAAO2Y,EACV9Y,OAAOsC,UAAU2I,eAAezS,KAAKsgB,EAAQ3Y,KAC/C0F,EAAO1F,GAAO2Y,EAAO3Y,IAK3B,OAAO0F,GA29BLkT,IAAc,aAAc,OAAQ,WAAY,YAAa,MAAO,UAAW,cAAe,QAAS,YAAa,aAAc,SAAU,eAAgB,WAAY,OAAQ,cAGhLlR,GAAkBkR,GAAW9V,MAAM,GAoBnC+V,IACFC,KAAM,OACNC,UAAW,YACXC,iBAAkB,oBAmyBhBC,IAKFxZ,UAAW,SAMXwG,eAAe,EAOfd,iBAAiB,EAQjBjB,SAAU,aAUVD,SAAU,aAOVtB,WApXAuW,OAEE9e,MAAO,IAEPgJ,SAAS,EAETD,GA9HJ,SAAeP,GACb,IAAInD,EAAYmD,EAAKnD,UACjBwI,EAAgBxI,EAAUlD,MAAM,KAAK,GACrC4c,EAAiB1Z,EAAUlD,MAAM,KAAK,GAG1C,GAAI4c,EAAgB,CAClB,IAAIC,EAAgBxW,EAAK3F,QACrB+B,EAAYoa,EAAcpa,UAC1BD,EAASqa,EAAcra,OAEvBsa,GAA2D,KAA7C,SAAU,OAAOngB,QAAQ+O,GACvCqR,EAAOD,EAAa,OAAS,MAC7BxX,EAAcwX,EAAa,QAAU,SAErCE,GACF/e,MAAO+d,MAAmBe,EAAMta,EAAUsa,IAC1C7e,IAAK8d,MAAmBe,EAAMta,EAAUsa,GAAQta,EAAU6C,GAAe9C,EAAO8C,KAGlFe,EAAK3F,QAAQ8B,OAAS7B,MAAa6B,EAAQwa,EAAaJ,IAG1D,OAAOvW,IAgJPoF,QAEE5N,MAAO,IAEPgJ,SAAS,EAETD,GAzQJ,SAAgBP,EAAMrD,GACpB,IAAIyI,EAASzI,EAAKyI,OACdvI,EAAYmD,EAAKnD,UACjB2Z,EAAgBxW,EAAK3F,QACrB8B,EAASqa,EAAcra,OACvBC,EAAYoa,EAAcpa,UAE1BiJ,EAAgBxI,EAAUlD,MAAM,KAAK,GAErCU,OAAU,EAsBd,OApBEA,EADEsJ,GAAWyB,KACDA,EAAQ,GAEVD,EAAYC,EAAQjJ,EAAQC,EAAWiJ,GAG7B,SAAlBA,GACFlJ,EAAOjD,KAAOmB,EAAQ,GACtB8B,EAAO/C,MAAQiB,EAAQ,IACI,UAAlBgL,GACTlJ,EAAOjD,KAAOmB,EAAQ,GACtB8B,EAAO/C,MAAQiB,EAAQ,IACI,QAAlBgL,GACTlJ,EAAO/C,MAAQiB,EAAQ,GACvB8B,EAAOjD,KAAOmB,EAAQ,IACK,WAAlBgL,IACTlJ,EAAO/C,MAAQiB,EAAQ,GACvB8B,EAAOjD,KAAOmB,EAAQ,IAGxB2F,EAAK7D,OAASA,EACP6D,GA8OLoF,OAAQ,GAoBVwR,iBAEEpf,MAAO,IAEPgJ,SAAS,EAETD,GA9PJ,SAAyBP,EAAMgB,GAC7B,IAAI1E,EAAoB0E,EAAQ1E,mBAAqBxF,EAAgBkJ,EAAKY,SAASzE,QAK/E6D,EAAKY,SAASxE,YAAcE,IAC9BA,EAAoBxF,EAAgBwF,IAGtC,IAAIC,EAAaL,EAAc8D,EAAKY,SAASzE,OAAQ6D,EAAKY,SAASxE,UAAW4E,EAAQ3E,QAASC,GAC/F0E,EAAQzE,WAAaA,EAErB,IAAI/E,EAAQwJ,EAAQ6V,SAChB1a,EAAS6D,EAAK3F,QAAQ8B,OAEtBkD,GACFyX,QAAS,SAAiBja,GACxB,IAAI6C,EAAQvD,EAAOU,GAInB,OAHIV,EAAOU,GAAaN,EAAWM,KAAemE,EAAQ+V,sBACxDrX,EAAQ5F,KAAKC,IAAIoC,EAAOU,GAAYN,EAAWM,KAE1C8Y,MAAmB9Y,EAAW6C,IAEvCsX,UAAW,SAAmBna,GAC5B,IAAIkC,EAAyB,UAAdlC,EAAwB,OAAS,MAC5C6C,EAAQvD,EAAO4C,GAInB,OAHI5C,EAAOU,GAAaN,EAAWM,KAAemE,EAAQ+V,sBACxDrX,EAAQ5F,KAAKmd,IAAI9a,EAAO4C,GAAWxC,EAAWM,IAA4B,UAAdA,EAAwBV,EAAOhC,MAAQgC,EAAOjC,UAErGyb,MAAmB5W,EAAUW,KAWxC,OAPAlI,EAAM2I,QAAQ,SAAUtD,GACtB,IAAI6Z,GAA+C,KAAvC,OAAQ,OAAOpgB,QAAQuG,GAAoB,UAAY,YACnEV,EAAS7B,MAAa6B,EAAQkD,EAAMqX,GAAM7Z,MAG5CmD,EAAK3F,QAAQ8B,OAASA,EAEf6D,GA2NL6W,UAAW,OAAQ,QAAS,MAAO,UAOnCxa,QAAS,EAMTC,kBAAmB,gBAYrB4a,cAEE1f,MAAO,IAEPgJ,SAAS,EAETD,GA9eJ,SAAsBP,GACpB,IAAIwW,EAAgBxW,EAAK3F,QACrB8B,EAASqa,EAAcra,OACvBC,EAAYoa,EAAcpa,UAE1BS,EAAYmD,EAAKnD,UAAUlD,MAAM,KAAK,GACtCwd,EAAQrd,KAAKqd,MACbV,GAAuD,KAAzC,MAAO,UAAUngB,QAAQuG,GACvC6Z,EAAOD,EAAa,QAAU,SAC9BW,EAASX,EAAa,OAAS,MAC/BxX,EAAcwX,EAAa,QAAU,SASzC,OAPIta,EAAOua,GAAQS,EAAM/a,EAAUgb,MACjCpX,EAAK3F,QAAQ8B,OAAOib,GAAUD,EAAM/a,EAAUgb,IAAWjb,EAAO8C,IAE9D9C,EAAOib,GAAUD,EAAM/a,EAAUsa,MACnC1W,EAAK3F,QAAQ8B,OAAOib,GAAUD,EAAM/a,EAAUsa,KAGzC1W,IAwePqX,OAEE7f,MAAO,IAEPgJ,SAAS,EAETD,GAtvBJ,SAAeP,EAAMgB,GAEnB,IAAKmD,EAAmBnE,EAAKY,SAASb,UAAW,QAAS,gBACxD,OAAOC,EAGT,IAAIsX,EAAetW,EAAQrL,QAG3B,GAA4B,iBAAjB2hB,GAIT,KAHAA,EAAetX,EAAKY,SAASzE,OAAOob,cAAcD,IAIhD,OAAOtX,OAKT,IAAKA,EAAKY,SAASzE,OAAOhE,SAASmf,GAEjC,OADAjX,QAAQC,KAAK,iEACNN,EAIX,IAAInD,EAAYmD,EAAKnD,UAAUlD,MAAM,KAAK,GACtC6c,EAAgBxW,EAAK3F,QACrB8B,EAASqa,EAAcra,OACvBC,EAAYoa,EAAcpa,UAE1Bqa,GAAuD,KAAzC,OAAQ,SAASngB,QAAQuG,GAEvC2a,EAAMf,EAAa,SAAW,QAC9BgB,EAAkBhB,EAAa,MAAQ,OACvCC,EAAOe,EAAgBrR,cACvBsR,EAAUjB,EAAa,OAAS,MAChCW,EAASX,EAAa,SAAW,QACjCkB,EAAmB3Z,EAAcsZ,GAAcE,GAQ/Cpb,EAAUgb,GAAUO,EAAmBxb,EAAOua,KAChD1W,EAAK3F,QAAQ8B,OAAOua,IAASva,EAAOua,IAASta,EAAUgb,GAAUO,IAG/Dvb,EAAUsa,GAAQiB,EAAmBxb,EAAOib,KAC9CpX,EAAK3F,QAAQ8B,OAAOua,IAASta,EAAUsa,GAAQiB,EAAmBxb,EAAOib,IAI3E,IAAIQ,EAASxb,EAAUsa,GAAQta,EAAUob,GAAO,EAAIG,EAAmB,EAInEE,EAAmBniB,EAAyBsK,EAAKY,SAASzE,OAAQ,SAAWsb,GAAiBjZ,QAAQ,KAAM,IAC5GsZ,EAAYF,EAASxd,EAAc4F,EAAK3F,QAAQ8B,QAAQua,GAAQmB,EAUpE,OAPAC,EAAYhe,KAAKC,IAAID,KAAKmd,IAAI9a,EAAOqb,GAAOG,EAAkBG,GAAY,GAE1E9X,EAAKsX,aAAeA,EACpBtX,EAAK3F,QAAQgd,SACbrX,EAAK3F,QAAQgd,MAAMX,GAAQ5c,KAAKie,MAAMD,GACtC9X,EAAK3F,QAAQgd,MAAMK,GAAW,GAEvB1X,GAmrBLrK,QAAS,aAcXsL,MAEEzJ,MAAO,IAEPgJ,SAAS,EAETD,GAjnBJ,SAAcP,EAAMgB,GAElB,GAAIO,EAAkBvB,EAAKY,SAASb,UAAW,SAC7C,OAAOC,EAGT,GAAIA,EAAKe,SAAWf,EAAKnD,YAAcmD,EAAKkB,kBAE1C,OAAOlB,EAGT,IAAIzD,EAAaL,EAAc8D,EAAKY,SAASzE,OAAQ6D,EAAKY,SAASxE,UAAW4E,EAAQ3E,QAAS2E,EAAQ1E,mBAEnGO,EAAYmD,EAAKnD,UAAUlD,MAAM,KAAK,GACtCqe,EAAoB1Z,EAAqBzB,GACzCgB,EAAYmC,EAAKnD,UAAUlD,MAAM,KAAK,IAAM,GAE5Cse,KAEJ,OAAQjX,EAAQkX,UACd,KAAKjC,GAAUC,KACb+B,GAAapb,EAAWmb,GACxB,MACF,KAAK/B,GAAUE,UACb8B,EAAYtT,EAAU9H,GACtB,MACF,KAAKoZ,GAAUG,iBACb6B,EAAYtT,EAAU9H,GAAW,GACjC,MACF,QACEob,EAAYjX,EAAQkX,SAkDxB,OA/CAD,EAAU9X,QAAQ,SAAUgY,EAAMtT,GAChC,GAAIhI,IAAcsb,GAAQF,EAAUzf,SAAWqM,EAAQ,EACrD,OAAO7E,EAGTnD,EAAYmD,EAAKnD,UAAUlD,MAAM,KAAK,GACtCqe,EAAoB1Z,EAAqBzB,GAEzC,IAAIgC,EAAgBmB,EAAK3F,QAAQ8B,OAC7Bic,EAAapY,EAAK3F,QAAQ+B,UAG1B+a,EAAQrd,KAAKqd,MACbkB,EAA4B,SAAdxb,GAAwBsa,EAAMtY,EAAcxF,OAAS8d,EAAMiB,EAAWhf,OAAuB,UAAdyD,GAAyBsa,EAAMtY,EAAczF,MAAQ+d,EAAMiB,EAAW/e,QAAwB,QAAdwD,GAAuBsa,EAAMtY,EAAc1F,QAAUge,EAAMiB,EAAWlf,MAAsB,WAAd2D,GAA0Bsa,EAAMtY,EAAc3F,KAAOie,EAAMiB,EAAWjf,QAEjUmf,EAAgBnB,EAAMtY,EAAczF,MAAQ+d,EAAM5a,EAAWnD,MAC7Dmf,EAAiBpB,EAAMtY,EAAcxF,OAAS8d,EAAM5a,EAAWlD,OAC/Dmf,EAAerB,EAAMtY,EAAc3F,KAAOie,EAAM5a,EAAWrD,KAC3Duf,EAAkBtB,EAAMtY,EAAc1F,QAAUge,EAAM5a,EAAWpD,QAEjEuf,EAAoC,SAAd7b,GAAwByb,GAA+B,UAAdzb,GAAyB0b,GAAgC,QAAd1b,GAAuB2b,GAA8B,WAAd3b,GAA0B4b,EAG3KhC,GAAuD,KAAzC,MAAO,UAAUngB,QAAQuG,GACvC8b,IAAqB3X,EAAQ4X,iBAAmBnC,GAA4B,UAAd5Y,GAAyBya,GAAiB7B,GAA4B,QAAd5Y,GAAuB0a,IAAmB9B,GAA4B,UAAd5Y,GAAyB2a,IAAiB/B,GAA4B,QAAd5Y,GAAuB4a,IAE7PJ,GAAeK,GAAuBC,KAExC3Y,EAAKe,SAAU,GAEXsX,GAAeK,KACjB7b,EAAYob,EAAUpT,EAAQ,IAG5B8T,IACF9a,EAAY6G,EAAqB7G,IAGnCmC,EAAKnD,UAAYA,GAAagB,EAAY,IAAMA,EAAY,IAI5DmC,EAAK3F,QAAQ8B,OAAS7B,MAAa0F,EAAK3F,QAAQ8B,OAAQuC,EAAiBsB,EAAKY,SAASzE,OAAQ6D,EAAK3F,QAAQ+B,UAAW4D,EAAKnD,YAE5HmD,EAAOF,EAAaE,EAAKY,SAASb,UAAWC,EAAM,WAGhDA,GAwiBLkY,SAAU,OAKV7b,QAAS,EAOTC,kBAAmB,YAUrBuc,OAEErhB,MAAO,IAEPgJ,SAAS,EAETD,GArPJ,SAAeP,GACb,IAAInD,EAAYmD,EAAKnD,UACjBwI,EAAgBxI,EAAUlD,MAAM,KAAK,GACrC6c,EAAgBxW,EAAK3F,QACrB8B,EAASqa,EAAcra,OACvBC,EAAYoa,EAAcpa,UAE1B0C,GAAwD,KAA7C,OAAQ,SAASxI,QAAQ+O,GAEpCyT,GAA6D,KAA3C,MAAO,QAAQxiB,QAAQ+O,GAO7C,OALAlJ,EAAO2C,EAAU,OAAS,OAAS1C,EAAUiJ,IAAkByT,EAAiB3c,EAAO2C,EAAU,QAAU,UAAY,GAEvHkB,EAAKnD,UAAYyB,EAAqBzB,GACtCmD,EAAK3F,QAAQ8B,OAAS/B,EAAc+B,GAE7B6D,IAkPPwS,MAEEhb,MAAO,IAEPgJ,SAAS,EAETD,GA9SJ,SAAcP,GACZ,IAAKmE,EAAmBnE,EAAKY,SAASb,UAAW,OAAQ,mBACvD,OAAOC,EAGT,IAAIlD,EAAUkD,EAAK3F,QAAQ+B,UACvB2c,EAAQ5Z,EAAKa,EAAKY,SAASb,UAAW,SAAU9G,GAClD,MAAyB,oBAAlBA,EAASyI,OACfnF,WAEH,GAAIO,EAAQ3D,OAAS4f,EAAM7f,KAAO4D,EAAQ1D,KAAO2f,EAAM1f,OAASyD,EAAQ5D,IAAM6f,EAAM5f,QAAU2D,EAAQzD,MAAQ0f,EAAM3f,KAAM,CAExH,IAAkB,IAAd4G,EAAKwS,KACP,OAAOxS,EAGTA,EAAKwS,MAAO,EACZxS,EAAKc,WAAW,uBAAyB,OACpC,CAEL,IAAkB,IAAdd,EAAKwS,KACP,OAAOxS,EAGTA,EAAKwS,MAAO,EACZxS,EAAKc,WAAW,wBAAyB,EAG3C,OAAOd,IAoSPgZ,cAEExhB,MAAO,IAEPgJ,SAAS,EAETD,GAp9BJ,SAAsBP,EAAMgB,GAC1B,IAAI/C,EAAI+C,EAAQ/C,EACZG,EAAI4C,EAAQ5C,EACZjC,EAAS6D,EAAK3F,QAAQ8B,OAItB8c,EAA8B9Z,EAAKa,EAAKY,SAASb,UAAW,SAAU9G,GACxE,MAAyB,eAAlBA,EAASyI,OACfwX,qBACiCzgB,IAAhCwgB,GACF5Y,QAAQC,KAAK,iIAEf,IAAI4Y,OAAkDzgB,IAAhCwgB,EAA4CA,EAA8BjY,EAAQkY,gBAGpGC,EAAmB5e,EADJzD,EAAgBkJ,EAAKY,SAASzE,SAI7C5C,GACF4H,SAAUhF,EAAOgF,UAIf9G,GACFjB,KAAMU,KAAKqd,MAAMhb,EAAO/C,MACxBF,IAAKY,KAAKqd,MAAMhb,EAAOjD,KACvBC,OAAQW,KAAKqd,MAAMhb,EAAOhD,QAC1BE,MAAOS,KAAKqd,MAAMhb,EAAO9C,QAGvBI,EAAc,WAANwE,EAAiB,MAAQ,SACjCvE,EAAc,UAAN0E,EAAgB,OAAS,QAKjCgb,EAAmBzX,EAAyB,aAW5CvI,OAAO,EACPF,OAAM,EAWV,GATEA,EADY,WAAVO,GACK0f,EAAiBjf,OAASG,EAAQlB,OAEnCkB,EAAQnB,IAGdE,EADY,UAAVM,GACMyf,EAAiBhf,MAAQE,EAAQhB,MAElCgB,EAAQjB,KAEb8f,GAAmBE,EACrB7f,EAAO6f,GAAoB,eAAiBhgB,EAAO,OAASF,EAAM,SAClEK,EAAOE,GAAS,EAChBF,EAAOG,GAAS,EAChBH,EAAO8f,WAAa,gBACf,CAEL,IAAIC,EAAsB,WAAV7f,GAAsB,EAAI,EACtC8f,EAAuB,UAAV7f,GAAqB,EAAI,EAC1CH,EAAOE,GAASP,EAAMogB,EACtB/f,EAAOG,GAASN,EAAOmgB,EACvBhgB,EAAO8f,WAAa5f,EAAQ,KAAOC,EAIrC,IAAIoH,GACF0Y,cAAexZ,EAAKnD,WAQtB,OAJAmD,EAAKc,WAAaxG,MAAawG,EAAYd,EAAKc,YAChDd,EAAKzG,OAASe,MAAaf,EAAQyG,EAAKzG,QACxCyG,EAAKa,YAAcvG,MAAa0F,EAAK3F,QAAQgd,MAAOrX,EAAKa,aAElDb,GAs4BLkZ,iBAAiB,EAMjBjb,EAAG,SAMHG,EAAG,SAkBLqb,YAEEjiB,MAAO,IAEPgJ,SAAS,EAETD,GApjCJ,SAAoBP,GAgBlB,OAXA+D,EAAU/D,EAAKY,SAASzE,OAAQ6D,EAAKzG,QAIrC0K,EAAcjE,EAAKY,SAASzE,OAAQ6D,EAAKc,YAGrCd,EAAKsX,cAAgBra,OAAOC,KAAK8C,EAAKa,aAAarI,QACrDuL,EAAU/D,EAAKsX,aAActX,EAAKa,aAG7Bb,GAsiCL0Z,OAzhCJ,SAA0Btd,EAAWD,EAAQ6E,EAAS2Y,EAAiB5b,GAErE,IAAIY,EAAmBb,EAAoBC,EAAO5B,EAAQC,GAKtDS,EAAYD,EAAqBoE,EAAQnE,UAAW8B,EAAkBxC,EAAQC,EAAW4E,EAAQjB,UAAUkB,KAAK3E,kBAAmB0E,EAAQjB,UAAUkB,KAAK5E,SAQ9J,OANAF,EAAO+H,aAAa,cAAerH,GAInCkH,EAAU5H,GAAUgF,SAAU,aAEvBH,GAihCLkY,qBAAiBzgB,KAiGjBmhB,GAAS,WASX,SAASA,EAAOxd,EAAWD,GACzB,IAAIoL,EAAQ7G,KAERM,EAAUzI,UAAUC,OAAS,QAAsBC,IAAjBF,UAAU,GAAmBA,UAAU,MAC7E2c,GAAexU,KAAMkZ,GAErBlZ,KAAK6C,eAAiB,WACpB,OAAOsW,sBAAsBtS,EAAM9G,SAIrCC,KAAKD,OAASkU,GAASjU,KAAKD,OAAO2N,KAAK1N,OAGxCA,KAAKM,QAAU1G,MAAasf,EAAOvD,SAAUrV,GAG7CN,KAAK3C,OACH4C,aAAa,EACbS,WAAW,EACXwB,kBAIFlC,KAAKtE,UAAYA,EAAUyX,OAASzX,EAAU,GAAKA,EACnDsE,KAAKvE,OAASA,EAAO0X,OAAS1X,EAAO,GAAKA,EAG1CuE,KAAKM,QAAQjB,aACb9C,OAAOC,KAAK5C,MAAasf,EAAOvD,SAAStW,UAAWiB,EAAQjB,YAAYI,QAAQ,SAAUuB,GACxF6F,EAAMvG,QAAQjB,UAAU2B,GAAQpH,MAAasf,EAAOvD,SAAStW,UAAU2B,OAAaV,EAAQjB,UAAYiB,EAAQjB,UAAU2B,SAI5HhB,KAAKX,UAAY9C,OAAOC,KAAKwD,KAAKM,QAAQjB,WAAW5C,IAAI,SAAUuE,GACjE,OAAOpH,IACLoH,KAAMA,GACL6F,EAAMvG,QAAQjB,UAAU2B,MAG5BpE,KAAK,SAAUC,EAAGC,GACjB,OAAOD,EAAE/F,MAAQgG,EAAEhG,QAOrBkJ,KAAKX,UAAUI,QAAQ,SAAUwZ,GAC3BA,EAAgBnZ,SAAWlL,EAAWqkB,EAAgBD,SACxDC,EAAgBD,OAAOnS,EAAMnL,UAAWmL,EAAMpL,OAAQoL,EAAMvG,QAAS2Y,EAAiBpS,EAAMxJ,SAKhG2C,KAAKD,SAEL,IAAI4C,EAAgB3C,KAAKM,QAAQqC,cAC7BA,GAEF3C,KAAK4C,uBAGP5C,KAAK3C,MAAMsF,cAAgBA,EAqD7B,OA9CA+R,GAAYwE,IACVxc,IAAK,SACLsC,MAAO,WACL,OAAOe,EAAOhL,KAAKiL,SAGrBtD,IAAK,UACLsC,MAAO,WACL,OAAO0C,EAAQ3M,KAAKiL,SAGtBtD,IAAK,uBACLsC,MAAO,WACL,OAAO4D,EAAqB7N,KAAKiL,SAGnCtD,IAAK,wBACLsC,MAAO,WACL,OAAO4C,EAAsB7M,KAAKiL,UA4B/BkZ,EA7HI,GAqJbA,GAAOE,OAA2B,oBAAX/jB,OAAyBA,OAASgkB,QAAQC,YACjEJ,GAAO5D,WAAaA,GACpB4D,GAAOvD,SAAWA,GMx3ElB,IAAM4D,GAAY,cAMM,oBAAXL,SACH,IAAIrR,MAAM,oEASZK,EAA2B,WAE3B2D,EAA2B,cAC3BC,EAAAA,IAA+BD,EAE/B1D,EAA2BtC,EAAEhG,GAAGqI,GAOhCsR,EAA2B,IAAI5R,OAAU6R,YAEzCrR,eACsB0D,kBACEA,cACFA,gBACCA,gBACAA,2IAMvBzD,YACQ,gBACA,cACA,mBACA,+BACA,sBAGRmC,eACY,sCACA,sBACA,4BACA,4BACA,gDAGZkP,OACQ,mBACA,iBACA,yBACA,cAGR3N,UACU,QACA,GAGVC,UACU,gCACA,WAUVuN,wBAEQtkB,EAASqS,QACdgB,SAAYrT,OACZ0kB,QAAY,UACZnN,QAAYxM,KAAKyM,WAAWnF,QAC5BsS,MAAY5Z,KAAK6Z,uBACjBC,UAAY9Z,KAAK+Z,qBAEjBnN,gDAoBPnC,OA9GoB,eA+GdzK,KAAKsI,SAAS0R,WAAYnU,EAAE7F,KAAKsI,UAAUgB,SAASjB,EAAU4R,eAI5Dxf,EAAW8e,EAASW,sBAAsBla,KAAKsI,UAC/C6R,EAAWtU,EAAE7F,KAAK4Z,OAAOtQ,SAASjB,EAAUgB,WAEzC+Q,eAELD,OAIE5K,iBACYvP,KAAKsI,UAEjB+R,EAAYxU,EAAEuC,MAAMA,EAAMiB,KAAMkG,QAEpC9U,GAAQ0M,QAAQkT,IAEdA,EAAU1R,0BAIV1T,EAAU+K,KAAKsI,SAEfzC,EAAEpL,GAAQ6O,SAASjB,EAAUiS,UAC3BzU,EAAE7F,KAAK4Z,OAAOtQ,SAASjB,EAAUkS,WAAa1U,EAAE7F,KAAK4Z,OAAOtQ,SAASjB,EAAUmS,gBACvE/f,QAGTkf,QAAU,IAAIT,GAAOjkB,EAAS+K,KAAK4Z,MAAO5Z,KAAKya,oBAMhD,iBAAkB5kB,SAASS,kBAC3BuP,EAAEpL,GAAQuO,QAAQwB,EAASkQ,YAAY5iB,UACvC,QAAQ0C,WAAW0P,GAAG,YAAa,KAAMrE,EAAE8U,WAG1CrS,SAAS+C,aACT/C,SAAS9E,aAAa,iBAAiB,KAE1CxD,KAAK4Z,OAAOtO,YAAYjD,EAAUgB,QAClC5O,GACC6Q,YAAYjD,EAAUgB,MACtBlC,QAAQtB,EAAEuC,MAAMA,EAAMwK,MAAOrD,UAGlC1G,QAlKoB,aAmKhBC,WAAW9I,KAAKsI,SAAUuD,KAC1B7L,KAAKsI,UAAU2F,IAAInC,QAChBxD,SAAW,UACXsR,MAAQ,KACQ,OAAjB5Z,KAAK2Z,cACFA,QAAQjY,eAEViY,QAAU,QAGjB5Z,OA7KoB,gBA8Kb+Z,UAAY9Z,KAAK+Z,gBACD,OAAjB/Z,KAAK2Z,cACFA,QAAQ9W,oBAMjB+J,mBAtLoB,wBAuLhB5M,KAAKsI,UAAU4B,GAAG9B,EAAMwS,MAAO,SAAC5Y,KAC1BiI,mBACA4Q,oBACDpQ,cAITgC,WA9LoB,SA8LTnF,YACAzB,EAAEqI,UAETlO,KAAK8a,YAAY/O,QACjBlG,EAAE7F,KAAKsI,UAAUhJ,OACjBgI,KAGG6G,gBACHjG,EACAZ,EACAtH,KAAK8a,YAAY9O,aAGZ1E,KAGTuS,gBA/MoB,eAgNb7Z,KAAK4Z,MAAO,KACTnf,EAAS8e,EAASW,sBAAsBla,KAAKsI,eAC9CsR,MAAQ/T,EAAEpL,GAAQgE,KAAK+L,EAASuQ,MAAM,UAEtC/a,KAAK4Z,SAGdoB,cAvNoB,eAwNZC,EAAkBpV,EAAE7F,KAAKsI,UAAU7N,SACrC0B,EAAYud,EAAcwB,cAG1BD,EAAgB3R,SAASjB,EAAUiS,WACzBZ,EAAcyB,IACtBtV,EAAE7F,KAAK4Z,OAAOtQ,SAASjB,EAAUmS,eACvBd,EAAc0B,SAEnBvV,EAAE7F,KAAK4Z,OAAOtQ,SAASjB,EAAUmS,eAC9Bd,EAAc2B,WAErBlf,KAGT4d,cAvOoB,kBAwOXlU,EAAE7F,KAAKsI,UAAUU,QAAQ,WAAWlR,OAAS,KAGtD2iB,iBA3OoB,sBA4OZa,KAC6B,mBAAxBtb,KAAKwM,QAAQ9H,SACX7E,GAAK,SAACP,YACV3F,QAAUkM,EAAEqI,UAAW5O,EAAK3F,QAAS2U,EAAK9B,QAAQ9H,OAAOpF,EAAK3F,cAC5D2F,KAGEoF,OAAS1E,KAAKwM,QAAQ9H,WAE7B6W,aACQvb,KAAKgb,kCAENM,gBAEGtb,KAAKwM,QAAQjM,eAMzBP,KAAK8Z,cACMza,UAAU0Z,qBACX/Y,KAAK8Z,YAGZyB,KAKF3R,iBA1Qa,SA0QItC,UACftH,KAAK6J,KAAK,eACXvK,EAAOuG,EAAE7F,MAAMV,KAAKuM,GAClBW,EAA4B,iBAAXlF,EAAsBA,EAAS,QAEjDhI,MACI,IAAIia,EAASvZ,KAAMwM,KACxBxM,MAAMV,KAAKuM,EAAUvM,IAGH,iBAAXgI,EAAqB,IACF,oBAAjBhI,EAAKgI,SACR,IAAIO,MAAJ,oBAA8BP,EAA9B,OAEHA,WAKJ8S,YA7Ra,SA6RDpY,OACbA,GAnQyB,IAmQfA,EAAM6M,QACH,UAAf7M,EAAM+I,MAvQqB,IAuQD/I,EAAM6M,WAK7B,IADC2M,EAAU3V,EAAEiJ,UAAUjJ,EAAE2E,EAASI,cAC9BtJ,EAAI,EAAGA,EAAIka,EAAQ1jB,OAAQwJ,IAAK,KACjC7G,EAAgB8e,EAASW,sBAAsBsB,EAAQla,IACvDma,EAAgB5V,EAAE2V,EAAQla,IAAIhC,KAAKuM,GACnC0D,iBACYiM,EAAQla,OAGrBma,OAICC,EAAeD,EAAQ7B,SACxB/T,EAAEpL,GAAQ6O,SAASjB,EAAUgB,SAI9BrH,IAAyB,UAAfA,EAAM+I,MAChB,kBAAkB7U,KAAK8L,EAAMI,OAAOwM,UAA2B,UAAf5M,EAAM+I,MA7R/B,IA6RmD/I,EAAM6M,QAC7EhJ,EAAEpO,SAASgD,EAAQuH,EAAMI,cAI1BuZ,EAAY9V,EAAEuC,MAAMA,EAAM0K,KAAMvD,KACpC9U,GAAQ0M,QAAQwU,GACdA,EAAUhT,uBAMV,iBAAkB9S,SAASS,mBAC3B,QAAQkE,WAAWyT,IAAI,YAAa,KAAMpI,EAAE8U,QAGxCrZ,GAAGkC,aAAa,gBAAiB,WAEvCkY,GAActS,YAAYf,EAAUgB,QACpC5O,GACC2O,YAAYf,EAAUgB,MACtBlC,QAAQtB,EAAEuC,MAAMA,EAAM2K,OAAQxD,WAI9B2K,sBA/Ua,SA+USjlB,OACvBwF,EACEuM,EAAWxB,EAAKuD,uBAAuB9T,UAEzC+R,MACOnB,EAAEmB,GAAU,IAGhBvM,GAAUxF,EAAQQ,cAGpBmmB,uBA1Va,SA0VU5Z,SACvBwX,EAAetjB,KAAK8L,EAAM6M,QAAU,UAAU3Y,KAAK8L,EAAMI,OAAOwM,UApUxC,KAoUoD5M,EAAM6M,OACpF,kBAAkB3Y,KAAK8L,EAAMI,OAAOwM,aAIjC3E,mBACA4Q,kBAEF7a,KAAKga,UAAYnU,EAAE7F,MAAMsJ,SAASjB,EAAU4R,iBAI1Cxf,EAAW8e,EAASW,sBAAsBla,MAC1Cma,EAAWtU,EAAEpL,GAAQ6O,SAASjB,EAAUgB,UAEzC8Q,GApVwB,KAoVXnY,EAAM6M,OAnVK,KAmVuB7M,EAAM6M,UACrDsL,GArVwB,KAqVXnY,EAAM6M,OApVK,KAoVuB7M,EAAM6M,YAWpDgN,EAAQhW,EAAEpL,GAAQgE,KAAK+L,EAASsR,eAAeC,SAEhDF,EAAM/jB,YAIPqM,EAAQ0X,EAAMjmB,QAAQoM,EAAMI,QAnWH,KAqWzBJ,EAAM6M,OAA8B1K,EAAQ,OApWnB,KAwWzBnC,EAAM6M,OAAgC1K,EAAQ0X,EAAM/jB,OAAS,OAI7DqM,EAAQ,MACF,KAGJA,GAAOkH,iBApXgB,KAuVvBrJ,EAAM6M,MAA0B,KAC5BpE,EAAS5E,EAAEpL,GAAQgE,KAAK+L,EAASI,aAAa,KAClDH,GAAQtD,QAAQ,WAGlBnH,MAAMmH,QAAQ,0DAjWW,sDAoFtB4E,6CAIAC,oBA6STnW,UACCqU,GAAG9B,EAAM4T,iBAAkBxR,EAASI,YAAc2O,EAASqC,wBAC3D1R,GAAG9B,EAAM4T,iBAAkBxR,EAASuQ,KAAMxB,EAASqC,wBACnD1R,GAAM9B,EAAM+B,eAHf,IAGiC/B,EAAM6T,eAAkB1C,EAASa,aAC/DlQ,GAAG9B,EAAM+B,eAAgBK,EAASI,YAAa,SAAU5I,KAClDiI,mBACA4Q,oBACGjR,iBAAiB7U,KAAK8Q,EAAE7F,MAAO,YAEzCkK,GAAG9B,EAAM+B,eAAgBK,EAAS0R,WAAY,SAACC,KAC5CtB,sBAUJhb,GAAGqI,GAAoBqR,EAAS3P,mBAChC/J,GAAGqI,GAAMmC,YAAckP,IACvB1Z,GAAGqI,GAAMoC,WAAc,oBACrBzK,GAAGqI,GAAQC,EACNoR,EAAS3P,kBAGX2P,EAjbS,GCDZ6C,GAAS,eASPlU,EAA+B,QAG/B4D,EAAAA,YAEA3D,EAA+BtC,EAAEhG,GAAGqI,GAKpC6D,aACO,YACA,SACA,QACA,GAGPC,YACO,4BACA,gBACA,eACA,WAGP5D,uWAcAC,sBACiB,mCACA,sBACA,kBACA,YACA,QAGjBmC,UACiB,4BACA,qCACA,uCACA,mEACA,6BACA,mBAUjB4R,wBAEQnnB,EAASqS,QACdkF,QAAuBxM,KAAKyM,WAAWnF,QACvCgB,SAAuBrT,OACvBonB,QAAuBxW,EAAE5Q,GAASwJ,KAAK+L,EAAS8R,QAAQ,QACxDC,UAAuB,UACvBC,UAAuB,OACvBC,oBAAuB,OACvBC,sBAAuB,OACvBC,qBAAuB,OACvBC,gBAAuB,6BAiB9BnS,OAnGiB,SAmGV8E,UACEvP,KAAKwc,SAAWxc,KAAK8R,OAAS9R,KAAK+R,KAAKxC,MAGjDwC,KAvGiB,SAuGZxC,kBACCvP,KAAKsR,mBAAoBtR,KAAKwc,UAI9BhX,EAAKuC,yBAA2BlC,EAAE7F,KAAKsI,UAAUgB,SAASjB,EAAUkB,aACjE+H,kBAAmB,OAGpB+I,EAAYxU,EAAEuC,MAAMA,EAAMiB,0BAI9BrJ,KAAKsI,UAAUnB,QAAQkT,GAErBra,KAAKwc,UAAYnC,EAAU1R,4BAI1B6T,UAAW,OAEXK,uBACAC,qBAEAC,kBAEHlnB,SAASC,MAAMia,SAAS1H,EAAU2U,WAE/BC,uBACAC,oBAEHld,KAAKsI,UAAU4B,GACf9B,EAAM+U,cACN3S,EAAS4S,aACT,SAACpb,UAAU6E,EAAKiL,KAAK9P,OAGrBhC,KAAKqc,SAASnS,GAAG9B,EAAMiV,kBAAmB,aACxCxW,EAAKyB,UAAU5B,IAAI0B,EAAMkV,gBAAiB,SAACtb,GACvC6D,EAAE7D,EAAMI,QAAQ0D,GAAGe,EAAKyB,cACrBoU,sBAAuB,YAK7Ba,cAAc,kBAAM1W,EAAK2W,aAAajO,UAG7CuC,KAvJiB,SAuJZ9P,iBACCA,KACIiI,kBAGJjK,KAAKsR,kBAAqBtR,KAAKwc,cAI7Bb,EAAY9V,EAAEuC,MAAMA,EAAM0K,WAE9B9S,KAAKsI,UAAUnB,QAAQwU,GAEpB3b,KAAKwc,WAAYb,EAAUhT,2BAI3B6T,UAAW,MAEV5W,EAAaJ,EAAKuC,yBAA2BlC,EAAE7F,KAAKsI,UAAUgB,SAASjB,EAAUkB,MAEnF3D,SACG0L,kBAAmB,QAGrB2L,uBACAC,oBAEHrnB,UAAUoY,IAAI7F,EAAMqV,WAEpBzd,KAAKsI,UAAUc,YAAYf,EAAUgB,QAErCrJ,KAAKsI,UAAU2F,IAAI7F,EAAM+U,iBACzBnd,KAAKqc,SAASpO,IAAI7F,EAAMiV,mBAEtBzX,IAEA5F,KAAKsI,UACJ5B,IAAIlB,EAAKmB,eAAgB,SAAC3E,UAAUsM,EAAKoP,WAAW1b,KACpD8F,qBA/K4B,UAiL1B4V,kBAIT7U,QApMiB,aAqMbC,WAAW9I,KAAKsI,SA1Le,cA4L/BjT,OAAQQ,SAAUmK,KAAKsI,SAAUtI,KAAKuc,WAAWtO,IAAInC,QAElDU,QAAuB,UACvBlE,SAAuB,UACvB+T,QAAuB,UACvBE,UAAuB,UACvBC,SAAuB,UACvBC,mBAAuB,UACvBC,qBAAuB,UACvBE,gBAAuB,QAG9Be,aAnNiB,gBAoNVZ,mBAKPtQ,WAzNiB,SAyNNnF,YACAzB,EAAEqI,UAAWnC,EAASzE,KAC1B6G,gBAAgBjG,EAAMZ,EAAQ0E,GAC5B1E,KAGTkW,aA/NiB,SA+NJjO,cACL3J,EAAaJ,EAAKuC,yBACtBlC,EAAE7F,KAAKsI,UAAUgB,SAASjB,EAAUkB,MAEjCvJ,KAAKsI,SAAS7S,YAChBuK,KAAKsI,SAAS7S,WAAWN,WAAa6B,KAAK4mB,uBAEnC9nB,KAAK+nB,YAAY7d,KAAKsI,eAG5BA,SAAS7G,MAAMqc,QAAU,aACzBxV,SAAS3G,gBAAgB,oBACzB2G,SAASjQ,UAAY,EAEtBuN,KACG6K,OAAOzQ,KAAKsI,YAGjBtI,KAAKsI,UAAUyH,SAAS1H,EAAUgB,MAEhCrJ,KAAKwM,QAAQnB,YACV0S,oBAGDC,EAAanY,EAAEuC,MAAMA,EAAMwK,yBAI3BqL,EAAqB,WACrBvN,EAAKlE,QAAQnB,SACV/C,SAAS+C,UAEXiG,kBAAmB,IACtBZ,EAAKpI,UAAUnB,QAAQ6W,IAGvBpY,IACA5F,KAAKqc,SACJ3V,IAAIlB,EAAKmB,eAAgBsX,GACzBnW,qBAvP4B,YA6PnCiW,cA5QiB,wBA6QbloB,UACCoY,IAAI7F,EAAMqV,SACVvT,GAAG9B,EAAMqV,QAAS,SAACzb,GACdnM,WAAamM,EAAMI,QACnB8b,EAAK5V,WAAatG,EAAMI,QACvByD,EAAEqY,EAAK5V,UAAU6V,IAAInc,EAAMI,QAAQtK,UACjCwQ,SAAS+C,aAKtB4R,gBAxRiB,sBAyRXjd,KAAKwc,UAAYxc,KAAKwM,QAAQ4B,WAC9BpO,KAAKsI,UAAU4B,GAAG9B,EAAMgW,gBAAiB,SAACpc,GAzQb,KA0QzBA,EAAM6M,UACF5E,mBACD6H,UAIC9R,KAAKwc,YACbxc,KAAKsI,UAAU2F,IAAI7F,EAAMgW,oBAI/BlB,gBAtSiB,sBAuSXld,KAAKwc,WACLnnB,QAAQ6U,GAAG9B,EAAMiW,OAAQ,SAACrc,UAAUsc,EAAKX,aAAa3b,OAEtD3M,QAAQ4Y,IAAI7F,EAAMiW,WAIxBX,WA9SiB,2BA+SVpV,SAAS7G,MAAMqc,QAAU,YACzBxV,SAAS9E,aAAa,eAAe,QACrC8N,kBAAmB,OACnBiM,cAAc,aACf1nB,SAASC,MAAMsT,YAAYf,EAAU2U,QAClCuB,sBACAC,oBACHC,EAAKnW,UAAUnB,QAAQiB,EAAM2K,aAInC2L,gBA1TiB,WA2TX1e,KAAKuc,cACLvc,KAAKuc,WAAW5S,cACb4S,UAAY,SAIrBgB,cAjUiB,SAiUHtb,cACN0c,EAAU9Y,EAAE7F,KAAKsI,UAAUgB,SAASjB,EAAUkB,MAClDlB,EAAUkB,KAAO,MAEfvJ,KAAKwc,UAAYxc,KAAKwM,QAAQoS,SAAU,KACpCC,EAAYrZ,EAAKuC,yBAA2B4W,UAE7CpC,UAAY1mB,SAASwQ,cAAc,YACnCkW,UAAUuC,UAAYzW,EAAU0W,SAEjCJ,KACA3e,KAAKuc,WAAWxM,SAAS4O,KAG3B3e,KAAKuc,WAAWyC,SAASnpB,SAASC,QAElCkK,KAAKsI,UAAU4B,GAAG9B,EAAM+U,cAAe,SAACnb,GACpCid,EAAKvC,uBACFA,sBAAuB,EAG1B1a,EAAMI,SAAWJ,EAAMwR,gBAGG,WAA1ByL,EAAKzS,QAAQoS,WACVtW,SAAS+C,UAETyG,UAIL+M,KACGpO,OAAOzQ,KAAKuc,aAGjBvc,KAAKuc,WAAWxM,SAAS1H,EAAUgB,OAEhCpH,aAIA4c,oBAKH7e,KAAKuc,WACJ7V,IAAIlB,EAAKmB,eAAgB1E,GACzB6F,qBAjW4B,UAmW1B,IAAK9H,KAAKwc,UAAYxc,KAAKuc,UAAW,GACzCvc,KAAKuc,WAAWnT,YAAYf,EAAUgB,UAElC6V,EAAiB,aAChBR,kBACDzc,QAKFuD,EAAKuC,yBACNlC,EAAE7F,KAAKsI,UAAUgB,SAASjB,EAAUkB,QACnCvJ,KAAKuc,WACJ7V,IAAIlB,EAAKmB,eAAgBuY,GACzBpX,qBAjX0B,cAsXtB7F,UAWb8a,cAjZiB,eAkZToC,EACJnf,KAAKsI,SAAS8W,aAAevpB,SAASS,gBAAgB4D,cAEnD8F,KAAKyc,oBAAsB0C,SACzB7W,SAAS7G,MAAM4d,YAAiBrf,KAAK4c,gBAA1C,MAGE5c,KAAKyc,qBAAuB0C,SACzB7W,SAAS7G,MAAM6d,aAAkBtf,KAAK4c,gBAA3C,SAIJ2B,kBA9ZiB,gBA+ZVjW,SAAS7G,MAAM4d,YAAc,QAC7B/W,SAAS7G,MAAM6d,aAAe,MAGrCzC,gBAnaiB,eAoaT1kB,EAAOtC,SAASC,KAAK+D,6BACtB4iB,mBAAqBtkB,EAAKO,KAAOP,EAAKQ,MAAQtD,OAAOgG,gBACrDuhB,gBAAkB5c,KAAKuf,wBAG9BzC,cAzaiB,yBA0aX9c,KAAKyc,mBAAoB,GAKzBjS,EAASgV,eAAe3V,KAAK,SAAC1F,EAAOlP,OAC/BwqB,EAAgB5Z,EAAE5Q,GAAS,GAAGwM,MAAM6d,aACpCI,EAAoB7Z,EAAE5Q,GAASG,IAAI,mBACvCH,GAASqK,KAAK,gBAAiBmgB,GAAerqB,IAAI,gBAAoBoI,WAAWkiB,GAAqBC,EAAK/C,gBAA7G,UAIApS,EAASoV,gBAAgB/V,KAAK,SAAC1F,EAAOlP,OAChC4qB,EAAeha,EAAE5Q,GAAS,GAAGwM,MAAM9D,YACnCmiB,EAAmBja,EAAE5Q,GAASG,IAAI,kBACtCH,GAASqK,KAAK,eAAgBugB,GAAczqB,IAAI,eAAmBoI,WAAWsiB,GAAoBH,EAAK/C,gBAAzG,UAIApS,EAASuV,gBAAgBlW,KAAK,SAAC1F,EAAOlP,OAChC4qB,EAAeha,EAAE5Q,GAAS,GAAGwM,MAAM9D,YACnCmiB,EAAmBja,EAAE5Q,GAASG,IAAI,kBACtCH,GAASqK,KAAK,eAAgBugB,GAAczqB,IAAI,eAAmBoI,WAAWsiB,GAAoBH,EAAK/C,gBAAzG,YAII6C,EAAgB5pB,SAASC,KAAK2L,MAAM6d,aACpCI,EAAoB7Z,EAAE,QAAQzQ,IAAI,mBACtC,QAAQkK,KAAK,gBAAiBmgB,GAAerqB,IAAI,gBAAoBoI,WAAWkiB,GAAqB1f,KAAK4c,gBAA5G,UAIJ4B,gBA1ciB,aA4cbhU,EAASgV,eAAe3V,KAAK,SAAC1F,EAAOlP,OAC/B0G,EAAUkK,EAAE5Q,GAASqK,KAAK,iBACT,oBAAZ3D,KACP1G,GAASG,IAAI,gBAAiBuG,GAASmN,WAAW,qBAKnD0B,EAASoV,eAAd,KAAiCpV,EAASuV,gBAAkBlW,KAAK,SAAC1F,EAAOlP,OACjE+qB,EAASna,EAAE5Q,GAASqK,KAAK,gBACT,oBAAX0gB,KACP/qB,GAASG,IAAI,eAAgB4qB,GAAQlX,WAAW,sBAKhDnN,EAAUkK,EAAE,QAAQvG,KAAK,iBACR,oBAAZ3D,KACP,QAAQvG,IAAI,gBAAiBuG,GAASmN,WAAW,oBAIvDyW,mBAleiB,eAmeTU,EAAYpqB,SAASwQ,cAAc,SAC/ByY,UAAYzW,EAAU6X,4BACvBpqB,KAAK+nB,YAAYoC,OACpBE,EAAiBF,EAAUpmB,wBAAwBJ,MAAQwmB,EAAUhmB,4BAClEnE,KAAKgM,YAAYme,GACnBE,KAMFvW,iBA9eU,SA8eOtC,EAAQiI,UACvBvP,KAAK6J,KAAK,eACXvK,EAAYuG,EAAE7F,MAAMV,KAreO,YAsezBkN,EAAU3G,EAAEqI,UAEhBkO,EAAMrQ,QACNlG,EAAE7F,MAAMV,OACU,iBAAXgI,GAAuBA,MAG3BhI,MACI,IAAI8c,EAAMpc,KAAMwM,KACrBxM,MAAMV,KA/eqB,WA+eNA,IAGH,iBAAXgI,EAAqB,IACF,oBAAjBhI,EAAKgI,SACR,IAAIO,MAAJ,oBAA8BP,EAA9B,OAEHA,GAAQiI,QACJ/C,EAAQuF,QACZA,KAAKxC,oDAzfmB,sDAmF1BxD,oBAobTlW,UAAUqU,GAAG9B,EAAM+B,eAAgBK,EAASI,YAAa,SAAU5I,OAC/DI,SACE4E,EAAWxB,EAAKuD,uBAAuB/I,MAEzCgH,MACOnB,EAAEmB,GAAU,QAGjBM,EAASzB,EAAEzD,GAAQ9C,KA9gBU,YA+gBjC,SAAWuG,EAAEqI,UAAWrI,EAAEzD,GAAQ9C,OAAQuG,EAAE7F,MAAMV,QAE/B,MAAjBU,KAAK4O,SAAoC,SAAjB5O,KAAK4O,WACzB3E,qBAGFyJ,EAAU7N,EAAEzD,GAAQsE,IAAI0B,EAAMiB,KAAM,SAACgR,GACrCA,EAAU1R,wBAKNjC,IAAI0B,EAAM2K,OAAQ,WACpBlN,EAAAA,GAAQC,GAAG,eACRuF,cAKLzB,iBAAiB7U,KAAK8Q,EAAEzD,GAASkF,EAAQtH,UAU/CH,GAAGqI,GAAoBkU,EAAMxS,mBAC7B/J,GAAGqI,GAAMmC,YAAc+R,IACvBvc,GAAGqI,GAAMoC,WAAc,oBACrBzK,GAAGqI,GAAQC,EACNiU,EAAMxS,kBAGRwS,EA9jBM,GCCTgE,GAAW,cAMO,oBAAXlH,SACH,IAAIrR,MAAM,oEAUZK,EAAsB,UAGtB4D,EAAAA,cACA3D,EAAsBtC,EAAEhG,GAAGqI,GAG3BmY,EAAqB,IAAIzY,OAAJ,wBAAyC,KAE9DoE,aACkB,mBACA,eACA,oCACA,eACA,uBACA,mBACA,6BACA,2BACA,4BACA,6CACA,kBAGlB0N,QACK,WACA,YACA,eACA,cACA,QAGL3N,cACkB,WACA,+GAGA,oBACA,SACA,QACA,YACA,YACA,aACA,aACA,oBACA,QAGlBuU,QACG,WACA,OAGHlY,eACgB0D,kBACEA,cACFA,gBACCA,sBACGA,gBACHA,oBACEA,sBACCA,0BACEA,0BACAA,GAGtBzD,QACG,YACA,QAGHmC,WACY,yBACA,uBACA,UAGZ+V,SACK,cACA,cACA,eACA,UAULH,wBAEQnrB,EAASqS,QAGdkZ,YAAiB,OACjBC,SAAiB,OACjBC,YAAiB,QACjBC,uBACAhH,QAAiB,UAGjB1kB,QAAUA,OACVqS,OAAUtH,KAAKyM,WAAWnF,QAC1BsZ,IAAU,UAEVC,2CAsCPC,OAjKmB,gBAkKZN,YAAa,KAGpBO,QArKmB,gBAsKZP,YAAa,KAGpBQ,cAzKmB,gBA0KZR,YAAcxgB,KAAKwgB,cAG1B/V,OA7KmB,SA6KZzI,MACAhC,KAAKwgB,cAINxe,EAAO,KACHif,EAAUjhB,KAAK8a,YAAYjP,SAC7B4P,EAAU5V,EAAE7D,EAAMwR,eAAelU,KAAK2hB,GAErCxF,MACO,IAAIzb,KAAK8a,YACjB9Y,EAAMwR,cACNxT,KAAKkhB,wBAELlf,EAAMwR,eAAelU,KAAK2hB,EAASxF,MAG/BkF,eAAeQ,OAAS1F,EAAQkF,eAAeQ,MAEnD1F,EAAQ2F,yBACFC,OAAO,KAAM5F,KAEb6F,OAAO,KAAM7F,OAGlB,IAED5V,EAAE7F,KAAKuhB,iBAAiBjY,SAASjB,EAAUgB,uBACxCiY,OAAO,KAAMthB,WAIfqhB,OAAO,KAAMrhB,UAItB6I,QAjNmB,wBAkNJ7I,KAAKygB,YAEhB3X,WAAW9I,KAAK/K,QAAS+K,KAAK8a,YAAYjP,YAE1C7L,KAAK/K,SAASgZ,IAAIjO,KAAK8a,YAAYhP,aACnC9L,KAAK/K,SAAS+T,QAAQ,UAAUiF,IAAI,iBAElCjO,KAAK4gB,OACL5gB,KAAK4gB,KAAKjX,cAGT6W,WAAiB,UACjBC,SAAiB,UACjBC,YAAiB,UACjBC,eAAiB,KACD,OAAjB3gB,KAAK2Z,cACFA,QAAQjY,eAGViY,QAAU,UACV1kB,QAAU,UACVqS,OAAU,UACVsZ,IAAU,QAGjB7O,KA3OmB,yBA4OsB,SAAnClM,EAAE7F,KAAK/K,SAASG,IAAI,iBAChB,IAAIyS,MAAM,2CAGZwS,EAAYxU,EAAEuC,MAAMpI,KAAK8a,YAAY1S,MAAMiB,SAC7CrJ,KAAKwhB,iBAAmBxhB,KAAKwgB,WAAY,GACzCxgB,KAAK/K,SAASkS,QAAQkT,OAElBoH,EAAa5b,EAAEpO,SACnBuI,KAAK/K,QAAQysB,cAAcprB,gBAC3B0J,KAAK/K,YAGHolB,EAAU1R,uBAAyB8Y,aAIjCb,EAAQ5gB,KAAKuhB,gBACbI,EAAQnc,EAAKoc,OAAO5hB,KAAK8a,YAAY5S,QAEvC1E,aAAa,KAAMme,QAClB1sB,QAAQuO,aAAa,mBAAoBme,QAEzCE,aAED7hB,KAAKsH,OAAOwa,aACZlB,GAAK7Q,SAAS1H,EAAUkB,UAGtBpN,EAA8C,mBAA1B6D,KAAKsH,OAAOnL,UACpC6D,KAAKsH,OAAOnL,UAAUpH,KAAKiL,KAAM4gB,EAAK5gB,KAAK/K,SAC3C+K,KAAKsH,OAAOnL,UAER4lB,EAAa/hB,KAAKgiB,eAAe7lB,QAClC8lB,mBAAmBF,OAElBG,GAAsC,IAA1BliB,KAAKsH,OAAO4a,UAAsBrsB,SAASC,KAAO+P,EAAE7F,KAAKsH,OAAO4a,aAEhFtB,GAAKthB,KAAKU,KAAK8a,YAAYjP,SAAU7L,MAElC6F,EAAEpO,SAASuI,KAAK/K,QAAQysB,cAAcprB,gBAAiB0J,KAAK4gB,QAC7DA,GAAK5B,SAASkD,KAGhBliB,KAAK/K,SAASkS,QAAQnH,KAAK8a,YAAY1S,MAAM+Z,eAE1CxI,QAAU,IAAIT,GAAOlZ,KAAK/K,QAAS2rB,aAC3BmB,4BAGC/hB,KAAKsH,OAAO5C,uBAGV1E,KAAKsH,OAAO8a,kCAGb5X,EAAS6X,iBAGZ,SAAC/iB,GACLA,EAAKkB,oBAAsBlB,EAAKnD,aAC7BmmB,6BAA6BhjB,aAG3B,SAACA,KACLgjB,6BAA6BhjB,QAIpCshB,GAAK7Q,SAAS1H,EAAUgB,MAMtB,iBAAkBxT,SAASS,mBAC3B,QAAQkE,WAAW0P,GAAG,YAAa,KAAMrE,EAAE8U,UAGzChI,EAAW,WACX9L,EAAKS,OAAOwa,aACTS,qBAEDC,EAAiB3b,EAAK6Z,cACvBA,YAAkB,OAErB7Z,EAAK5R,SAASkS,QAAQN,EAAKiU,YAAY1S,MAAMwK,OAE3C4P,IAAmBlC,EAAWmC,OAC3BnB,OAAO,KAAZza,IAIArB,EAAKuC,yBAA2BlC,EAAE7F,KAAK4gB,KAAKtX,SAASjB,EAAUkB,QAC/DvJ,KAAK4gB,KACJla,IAAIlB,EAAKmB,eAAgBgM,GACzB7K,qBAAqBsY,EAAQsC,8BAOtC5Q,KAnVmB,SAmVd7P,cACG2e,EAAY5gB,KAAKuhB,gBACjB5F,EAAY9V,EAAEuC,MAAMpI,KAAK8a,YAAY1S,MAAM0K,MAC3CH,EAAY,WACZrE,EAAKoS,cAAgBJ,EAAWjX,MAAQuX,EAAInrB,cAC1CA,WAAWqM,YAAY8e,KAGxB+B,mBACA1tB,QAAQ0M,gBAAgB,sBAC3B2M,EAAKrZ,SAASkS,QAAQmH,EAAKwM,YAAY1S,MAAM2K,QAC1B,OAAjBzE,EAAKqL,WACFA,QAAQjY,UAGXO,UAKJjC,KAAK/K,SAASkS,QAAQwU,GAEpBA,EAAUhT,yBAIZiY,GAAKxX,YAAYf,EAAUgB,MAIzB,iBAAkBxT,SAASS,mBAC3B,QAAQkE,WAAWyT,IAAI,YAAa,KAAMpI,EAAE8U,WAG3CgG,eAAeJ,EAAQ3F,QAAS,OAChC+F,eAAeJ,EAAQ5U,QAAS,OAChCgV,eAAeJ,EAAQqC,QAAS,EAEjCpd,EAAKuC,yBACLlC,EAAE7F,KAAK4gB,KAAKtX,SAASjB,EAAUkB,QAE/BqX,GACCla,IAAIlB,EAAKmB,eAAgBgM,GACzB7K,qBAxWmB,cA8WnB4Y,YAAc,OAIrB3gB,OAxYmB,WAyYI,OAAjBC,KAAK2Z,cACFA,QAAQ9W,oBAMjB2e,cAhZmB,kBAiZVpa,QAAQpH,KAAK6iB,eAGtBZ,mBApZmB,SAoZAF,KACf/hB,KAAKuhB,iBAAiBxR,SAAY+S,cAAgBf,MAGtDR,cAxZmB,uBAyZZX,IAAM5gB,KAAK4gB,KAAO/a,EAAE7F,KAAKsH,OAAOyb,UAAU,GACxC/iB,KAAK4gB,OAGdiB,WA7ZmB,eA8ZXmB,EAAOnd,EAAE7F,KAAKuhB,sBACf0B,kBAAkBD,EAAKvkB,KAAK+L,EAAS0Y,eAAgBljB,KAAK6iB,cAC1DzZ,YAAef,EAAUkB,KAA9B,IAAsClB,EAAUgB,SAGlD4Z,kBAnamB,SAmaDnZ,EAAUqZ,OACpBnrB,EAAOgI,KAAKsH,OAAOtP,KACF,iBAAZmrB,IAAyBA,EAAQhuB,UAAYguB,EAAQhQ,QAE1Dnb,EACG6N,EAAEsd,GAAS1oB,SAASqL,GAAGgE,MACjBsZ,QAAQC,OAAOF,KAGjBG,KAAKzd,EAAEsd,GAASG,UAGlBtrB,EAAO,OAAS,QAAQmrB,MAIrCN,SAnbmB,eAobbU,EAAQvjB,KAAK/K,QAAQgS,aAAa,8BAEjCsc,MACkC,mBAAtBvjB,KAAKsH,OAAOic,MACzBvjB,KAAKsH,OAAOic,MAAMxuB,KAAKiL,KAAK/K,SAC5B+K,KAAKsH,OAAOic,OAGTA,KAMTvB,eAlcmB,SAkcJ7lB,UACNud,EAAcvd,EAAUkF,kBAGjCwf,cAtcmB,sBAucA7gB,KAAKsH,OAAOH,QAAQlO,MAAM,KAElCwG,QAAQ,SAAC0H,MACA,UAAZA,IACAuJ,EAAKzb,SAASiV,GACdwG,EAAKoK,YAAY1S,MAAMwS,MACvBlK,EAAKpJ,OAAON,SACZ,SAAChF,UAAU0O,EAAKjG,OAAOzI,UAGpB,GAAImF,IAAYoZ,EAAQiD,OAAQ,KAC/BC,EAAWtc,IAAYoZ,EAAQqC,MACnClS,EAAKoK,YAAY1S,MAAMoG,WACvBkC,EAAKoK,YAAY1S,MAAMqV,QACnBiG,EAAWvc,IAAYoZ,EAAQqC,MACnClS,EAAKoK,YAAY1S,MAAMqG,WACvBiC,EAAKoK,YAAY1S,MAAMub,WAEvBjT,EAAKzb,SACJiV,GACCuZ,EACA/S,EAAKpJ,OAAON,SACZ,SAAChF,UAAU0O,EAAK2Q,OAAOrf,KAExBkI,GACCwZ,EACAhT,EAAKpJ,OAAON,SACZ,SAAChF,UAAU0O,EAAK4Q,OAAOtf,OAI3B0O,EAAKzb,SAAS+T,QAAQ,UAAUkB,GAChC,gBACA,kBAAMwG,EAAKoB,WAIX9R,KAAKsH,OAAON,cACTM,OAASzB,EAAEqI,UAAWlO,KAAKsH,gBACnB,kBACA,UAGRsc,eAITA,UAtfmB,eAufXC,SAAmB7jB,KAAK/K,QAAQgS,aAAa,wBAC/CjH,KAAK/K,QAAQgS,aAAa,UACb,WAAd4c,UACI5uB,QAAQuO,aACX,sBACAxD,KAAK/K,QAAQgS,aAAa,UAAY,SAEnChS,QAAQuO,aAAa,QAAS,QAIvC6d,OAlgBmB,SAkgBZrf,EAAOyZ,OACNwF,EAAUjhB,KAAK8a,YAAYjP,YAEvB4P,GAAW5V,EAAE7D,EAAMwR,eAAelU,KAAK2hB,QAGrC,IAAIjhB,KAAK8a,YACjB9Y,EAAMwR,cACNxT,KAAKkhB,wBAELlf,EAAMwR,eAAelU,KAAK2hB,EAASxF,IAGnCzZ,MACM2e,eACS,YAAf3e,EAAM+I,KAAqBwV,EAAQ5U,MAAQ4U,EAAQqC,QACjD,GAGF/c,EAAE4V,EAAQ8F,iBAAiBjY,SAASjB,EAAUgB,OAC/CoS,EAAQiF,cAAgBJ,EAAWjX,OAC5BqX,YAAcJ,EAAWjX,mBAItBoS,EAAQgF,YAEbC,YAAcJ,EAAWjX,KAE5BoS,EAAQnU,OAAOwc,OAAUrI,EAAQnU,OAAOwc,MAAM/R,OAK3C0O,SAAW9R,WAAW,WACxB8M,EAAQiF,cAAgBJ,EAAWjX,QAC7B0I,QAET0J,EAAQnU,OAAOwc,MAAM/R,QARdA,WAWZuP,OA3iBmB,SA2iBZtf,EAAOyZ,OACNwF,EAAUjhB,KAAK8a,YAAYjP,YAEvB4P,GAAW5V,EAAE7D,EAAMwR,eAAelU,KAAK2hB,QAGrC,IAAIjhB,KAAK8a,YACjB9Y,EAAMwR,cACNxT,KAAKkhB,wBAELlf,EAAMwR,eAAelU,KAAK2hB,EAASxF,IAGnCzZ,MACM2e,eACS,aAAf3e,EAAM+I,KAAsBwV,EAAQ5U,MAAQ4U,EAAQqC,QAClD,GAGFnH,EAAQ2F,sCAIC3F,EAAQgF,YAEbC,YAAcJ,EAAWmC,IAE5BhH,EAAQnU,OAAOwc,OAAUrI,EAAQnU,OAAOwc,MAAMhS,OAK3C2O,SAAW9R,WAAW,WACxB8M,EAAQiF,cAAgBJ,EAAWmC,OAC7B3Q,QAET2J,EAAQnU,OAAOwc,MAAMhS,QARdA,WAWZsP,qBAllBmB,eAmlBZ,IAAMja,KAAWnH,KAAK2gB,kBACrB3gB,KAAK2gB,eAAexZ,UACf,SAIJ,KAGTsF,WA5lBmB,SA4lBRnF,SAQmB,mBAPnBzB,EAAEqI,UAETlO,KAAK8a,YAAY/O,QACjBlG,EAAE7F,KAAK/K,SAASqK,OAChBgI,IAGgBwc,UACTA,YACExc,EAAOwc,WACPxc,EAAOwc,QAIU,iBAAjBxc,EAAOic,UACTA,MAAQjc,EAAOic,MAAMzuB,YAGA,iBAAnBwS,EAAO6b,YACTA,QAAU7b,EAAO6b,QAAQruB,cAG7BqZ,gBACHjG,EACAZ,EACAtH,KAAK8a,YAAY9O,aAGZ1E,KAGT4Z,mBA5nBmB,eA6nBX5Z,QAEFtH,KAAKsH,WACF,IAAM5K,KAAOsD,KAAKsH,OACjBtH,KAAK8a,YAAY/O,QAAQrP,KAASsD,KAAKsH,OAAO5K,OACzCA,GAAOsD,KAAKsH,OAAO5K,WAKzB4K,KAGTqb,eA1oBmB,eA2oBXK,EAAOnd,EAAE7F,KAAKuhB,iBACdwC,EAAWf,EAAKvQ,KAAK,SAASvT,MAAMmhB,GACzB,OAAb0D,GAAqBA,EAASjsB,OAAS,KACpCsR,YAAY2a,EAASC,KAAK,QAInC1B,6BAlpBmB,SAkpBUhjB,QACtBqjB,sBACAV,mBAAmBjiB,KAAKgiB,eAAe1iB,EAAKnD,eAGnDomB,eAvpBmB,eAwpBX3B,EAAsB5gB,KAAKuhB,gBAC3B0C,EAAsBjkB,KAAKsH,OAAOwa,UACA,OAApClB,EAAI3Z,aAAa,mBAGnB2Z,GAAKxX,YAAYf,EAAUkB,WACxBjC,OAAOwa,WAAY,OACnBhQ,YACAC,YACAzK,OAAOwa,UAAYmC,MAKnBra,iBAtqBY,SAsqBKtC,UACftH,KAAK6J,KAAK,eACXvK,EAAYuG,EAAE7F,MAAMV,KArpBF,cAspBhBkN,EAA4B,iBAAXlF,GAAuBA,MAEzChI,IAAQ,eAAepJ,KAAKoR,MAI5BhI,MACI,IAAI8gB,EAAQpgB,KAAMwM,KACvBxM,MAAMV,KA9pBY,aA8pBGA,IAGH,iBAAXgI,GAAqB,IACF,oBAAjBhI,EAAKgI,SACR,IAAIO,MAAJ,oBAA8BP,EAA9B,OAEHA,uDAtqBe,sDAqHjByE,sCAIA7D,yCAxHiB,kDAgIjBE,2CAIA0D,6CAIAE,oBA0iBTnM,GAAGqI,GAAoBkY,EAAQxW,mBAC/B/J,GAAGqI,GAAMmC,YAAc+V,IACvBvgB,GAAGqI,GAAMoC,WAAc,oBACrBzK,GAAGqI,GAAQC,EACNiY,EAAQxW,kBAGVwW,EA5sBQ,GCDX8D,GAAW,eASThc,EAAsB,UAGtB4D,EAAAA,cACA3D,EAAsBtC,EAAEhG,GAAGqI,GAE3BmY,EAAsB,IAAIzY,OAAJ,wBAAyC,KAE/DmE,EAAUlG,EAAEqI,UAAWkS,GAAQrU,mBACvB,gBACA,gBACA,YACA,wIAMRC,EAAcnG,EAAEqI,UAAWkS,GAAQpU,qBAC7B,8BAGN3D,QACG,YACA,QAGHmC,SACM,0BACA,iBAGNpC,eACgB0D,kBACEA,cACFA,gBACCA,sBACGA,gBACHA,oBACEA,sBACCA,0BACEA,0BACAA,GAUtBoY,kGAoCJ1C,cAjGmB,kBAkGVxhB,KAAK6iB,YAAc7iB,KAAKmkB,iBAGjClC,mBArGmB,SAqGAF,KACf/hB,KAAKuhB,iBAAiBxR,SAAY+S,cAAgBf,MAGtDR,cAzGmB,uBA0GZX,IAAM5gB,KAAK4gB,KAAO/a,EAAE7F,KAAKsH,OAAOyb,UAAU,GACxC/iB,KAAK4gB,OAGdiB,WA9GmB,eA+GXmB,EAAOnd,EAAE7F,KAAKuhB,sBAGf0B,kBAAkBD,EAAKvkB,KAAK+L,EAAS4Z,OAAQpkB,KAAK6iB,iBAClDI,kBAAkBD,EAAKvkB,KAAK+L,EAAS6Z,SAAUrkB,KAAKmkB,iBAEpD/a,YAAef,EAAUkB,KAA9B,IAAsClB,EAAUgB,SAKlD8a,YA1HmB,kBA2HVnkB,KAAK/K,QAAQgS,aAAa,kBACI,mBAAxBjH,KAAKsH,OAAO6b,QACjBnjB,KAAKsH,OAAO6b,QAAQpuB,KAAKiL,KAAK/K,SAC9B+K,KAAKsH,OAAO6b,YAGtBR,eAjImB,eAkIXK,EAAOnd,EAAE7F,KAAKuhB,iBACdwC,EAAWf,EAAKvQ,KAAK,SAASvT,MAAMmhB,GACzB,OAAb0D,GAAqBA,EAASjsB,OAAS,KACpCsR,YAAY2a,EAASC,KAAK,QAO5Bpa,iBA5IY,SA4IKtC,UACftH,KAAK6J,KAAK,eACXvK,EAAYuG,EAAE7F,MAAMV,KAnIF,cAoIhBkN,EAA4B,iBAAXlF,EAAsBA,EAAS,SAEjDhI,IAAQ,eAAepJ,KAAKoR,MAI5BhI,MACI,IAAI4kB,EAAQlkB,KAAMwM,KACvBxM,MAAMV,KA5IY,aA4IGA,IAGH,iBAAXgI,GAAqB,IACF,oBAAjBhI,EAAKgI,SACR,IAAIO,MAAJ,oBAA8BP,EAA9B,OAEHA,uDApJe,sDA6DjByE,sCAIA7D,yCAhEiB,kDAwEjBE,2CAIA0D,6CAIAE,SA9BWoU,aA8GpBvgB,GAAGqI,GAAoBgc,EAAQta,mBAC/B/J,GAAGqI,GAAMmC,YAAc6Z,IACvBrkB,GAAGqI,GAAMoC,WAAc,oBACrBzK,GAAGqI,GAAQC,EACN+b,EAAQta,kBAGVsa,EAlLQ,GCAXI,GAAa,eASXpc,EAAqB,YAKrBC,EAAqBtC,EAAEhG,GAAGqI,GAE1B6D,UACK,UACA,cACA,IAGLC,UACK,gBACA,gBACA,oBAGL5D,6GAMAC,iBACY,8BACA,uBACA,UAGZmC,YACc,6BACA,yBACA,8BACA,sBACA,uBACA,4BACA,2BACA,iCACA,oBAGd+Z,UACO,kBACA,YAUPD,wBAEQrvB,EAASqS,mBACdgB,SAAiBrT,OACjBuvB,eAAqC,SAApBvvB,EAAQ2Z,QAAqBvZ,OAASJ,OACvDuX,QAAiBxM,KAAKyM,WAAWnF,QACjCmd,UAAoBzkB,KAAKwM,QAAQpK,OAAhB,IAA0BoI,EAASka,UAAnC,IACG1kB,KAAKwM,QAAQpK,OADhB,IAC0BoI,EAASma,WADnC,IAEG3kB,KAAKwM,QAAQpK,OAFhB,IAE0BoI,EAASoa,oBACpDC,iBACAC,iBACAC,cAAiB,UACjBC,cAAiB,IAEpBhlB,KAAKwkB,gBAAgBta,GAAG9B,EAAM6c,OAAQ,SAACjjB,UAAU6E,EAAKqe,SAASljB,UAE5DmjB,eACAD,sCAiBPC,QAlGqB,sBAmGbC,EAAaplB,KAAKwkB,iBAAmBxkB,KAAKwkB,eAAenvB,OAC7DkvB,EAAac,SAAWd,EAAae,OAEjCC,EAAuC,SAAxBvlB,KAAKwM,QAAQgZ,OAChCJ,EAAaplB,KAAKwM,QAAQgZ,OAEtBC,EAAaF,IAAiBhB,EAAac,SAC/CrlB,KAAK0lB,gBAAkB,OAEpBb,iBACAC,iBAEAE,cAAgBhlB,KAAK2lB,mBAEV9f,EAAEiJ,UAAUjJ,EAAE7F,KAAKykB,YAGhChoB,IAAI,SAACxH,OACAmN,EACEwjB,EAAiBpgB,EAAKuD,uBAAuB9T,MAE/C2wB,MACO/f,EAAE+f,GAAgB,IAGzBxjB,EAAQ,KACJyjB,EAAYzjB,EAAOvI,2BACrBgsB,EAAUpsB,OAASosB,EAAUrsB,cAG7BqM,EAAEzD,GAAQmjB,KAAgB/sB,IAAMitB,EAChCG,UAIC,OAER5oB,OAAO,SAAC8oB,UAAUA,IAClBlpB,KAAK,SAACC,EAAGC,UAASD,EAAE,GAAKC,EAAE,KAC3B2C,QAAQ,SAACqmB,KACHjB,SAAStiB,KAAKujB,EAAK,MACnBhB,SAASviB,KAAKujB,EAAK,SAI9Bjd,QAhJqB,aAiJjBC,WAAW9I,KAAKsI,SAtIK,kBAuIrBtI,KAAKwkB,gBAAgBvW,IAtIrBnC,sBAwIGxD,SAAiB,UACjBkc,eAAiB,UACjBhY,QAAiB,UACjBiY,UAAiB,UACjBI,SAAiB,UACjBC,SAAiB,UACjBC,cAAiB,UACjBC,cAAiB,QAMxBvY,WAjKqB,SAiKVnF,MAGoB,mBAFpBzB,EAAEqI,UAAWnC,EAASzE,IAEblF,OAAqB,KACjCoP,EAAK3L,EAAEyB,EAAOlF,QAAQqQ,KAAK,MAC1BjB,MACEhM,EAAKoc,OAAO1Z,KACfZ,EAAOlF,QAAQqQ,KAAK,KAAMjB,MAEvBpP,OAAP,IAAoBoP,WAGjBrD,gBAAgBjG,EAAMZ,EAAQ0E,GAE5B1E,KAGToe,cAlLqB,kBAmLZ1lB,KAAKwkB,iBAAmBnvB,OAC3B2K,KAAKwkB,eAAeuB,YAAc/lB,KAAKwkB,eAAensB,aAG5DstB,iBAvLqB,kBAwLZ3lB,KAAKwkB,eAAepF,cAAgBhmB,KAAKC,IAC9CxD,SAASC,KAAKspB,aACdvpB,SAASS,gBAAgB8oB,iBAI7B4G,iBA9LqB,kBA+LZhmB,KAAKwkB,iBAAmBnvB,OAC3BA,OAAOiG,YAAc0E,KAAKwkB,eAAe3qB,wBAAwBL,UAGvE0rB,SAnMqB,eAoMb7sB,EAAe2H,KAAK0lB,gBAAkB1lB,KAAKwM,QAAQ9H,OACnD0a,EAAepf,KAAK2lB,mBACpBM,EAAejmB,KAAKwM,QAAQ9H,OAC9B0a,EACApf,KAAKgmB,sBAELhmB,KAAKglB,gBAAkB5F,QACpB+F,UAGH9sB,GAAa4tB,OACT7jB,EAASpC,KAAK8kB,SAAS9kB,KAAK8kB,SAAShtB,OAAS,GAEhDkI,KAAK+kB,gBAAkB3iB,QACpB8jB,UAAU9jB,WAKfpC,KAAK+kB,eAAiB1sB,EAAY2H,KAAK6kB,SAAS,IAAM7kB,KAAK6kB,SAAS,GAAK,cACtEE,cAAgB,eAChBoB,aAIF,IAAI7kB,EAAItB,KAAK6kB,SAAS/sB,OAAQwJ,KACVtB,KAAK+kB,gBAAkB/kB,KAAK8kB,SAASxjB,IACrDjJ,GAAa2H,KAAK6kB,SAASvjB,KACM,oBAAzBtB,KAAK6kB,SAASvjB,EAAI,IACzBjJ,EAAY2H,KAAK6kB,SAASvjB,EAAI,UAG/B4kB,UAAUlmB,KAAK8kB,SAASxjB,QAKnC4kB,UAzOqB,SAyOX9jB,QACH2iB,cAAgB3iB,OAEhB+jB,aAEDC,EAAUpmB,KAAKykB,UAAUxrB,MAAM,OAErBmtB,EAAQ3pB,IAAI,SAACuK,UACfA,EAAH,iBAA4B5E,EAA5B,MACG4E,EADH,UACqB5E,EADrB,WAIHikB,EAAQxgB,EAAEugB,EAAQpC,KAAK,MAEzBqC,EAAM/c,SAASjB,EAAUie,kBACrBtd,QAAQwB,EAAS+b,UAAU9nB,KAAK+L,EAASgc,iBAAiBzW,SAAS1H,EAAU4C,UAC7E8E,SAAS1H,EAAU4C,YAGnB8E,SAAS1H,EAAU4C,UAGnBwb,QAAQjc,EAASkc,gBAAgBxZ,KAAQ1C,EAASka,UAAxD,KAAsEla,EAASma,YAAc5U,SAAS1H,EAAU4C,UAE1Gwb,QAAQjc,EAASkc,gBAAgBxZ,KAAK1C,EAASmc,WAAWnsB,SAASgQ,EAASka,WAAW3U,SAAS1H,EAAU4C,WAGhHjL,KAAKwkB,gBAAgBrd,QAAQiB,EAAMwe,wBACpBxkB,OAInB+jB,OAzQqB,aA0QjBnmB,KAAKykB,WAAWznB,OAAOwN,EAASS,QAAQ7B,YAAYf,EAAU4C,WAM3DrB,iBAhRc,SAgRGtC,UACftH,KAAK6J,KAAK,eACXvK,EAAYuG,EAAE7F,MAAMV,KAvQH,gBAwQfkN,EAA4B,iBAAXlF,GAAuBA,KAEzChI,MACI,IAAIglB,EAAUtkB,KAAMwM,KACzBxM,MAAMV,KA5QW,eA4QIA,IAGH,iBAAXgI,EAAqB,IACF,oBAAjBhI,EAAKgI,SACR,IAAIO,MAAJ,oBAA8BP,EAA9B,OAEHA,uDApRc,sDAkFhByE,oBAiNT1W,QAAQ6U,GAAG9B,EAAM6I,cAAe,eAG3B,IAFC4V,EAAahhB,EAAEiJ,UAAUjJ,EAAE2E,EAASsc,WAEjCxlB,EAAIulB,EAAW/uB,OAAQwJ,KAAM,KAC9BylB,EAAOlhB,EAAEghB,EAAWvlB,MAChBsI,iBAAiB7U,KAAKgyB,EAAMA,EAAKznB,aAW7CO,GAAGqI,GAAoBoc,EAAU1a,mBACjC/J,GAAGqI,GAAMmC,YAAcia,IACvBzkB,GAAGqI,GAAMoC,WAAc,oBACrBzK,GAAGqI,GAAQC,EACNmc,EAAU1a,kBAGZ0a,EApUU,GCAb0C,GAAO,eAcL7e,EAAsBtC,EAAEhG,GAAF,IAGtBuI,6HAQAC,iBACY,uBACA,kBACA,gBACA,YACA,QAGZmC,YACoB,2BACA,2BACA,oBACA,6BACA,kFACA,yCACA,4BAUpBwc,wBAEQ/xB,QACLqT,SAAWrT,6BAalB8c,KAlEe,2BAmET/R,KAAKsI,SAAS7S,YACduK,KAAKsI,SAAS7S,WAAWN,WAAa6B,KAAK4mB,cAC3C/X,EAAE7F,KAAKsI,UAAUgB,SAASjB,EAAU4C,SACpCpF,EAAE7F,KAAKsI,UAAUgB,SAASjB,EAAU4R,gBAIpC7X,EACA6kB,EACEC,EAAcrhB,EAAE7F,KAAKsI,UAAUU,QAAQwB,EAASkc,gBAAgB,GAChE1f,EAAcxB,EAAKuD,uBAAuB/I,KAAKsI,aAEjD4e,EAAa,KACTC,EAAwC,OAAzBD,EAAY1xB,SAAoBgV,EAAS4c,UAAY5c,EAASS,SACxEpF,EAAEiJ,UAAUjJ,EAAEqhB,GAAazoB,KAAK0oB,MAChCF,EAASA,EAASnvB,OAAS,OAGlC6jB,EAAY9V,EAAEuC,MAAMA,EAAM0K,oBACf9S,KAAKsI,WAGhB+R,EAAYxU,EAAEuC,MAAMA,EAAMiB,oBACf4d,OAGbA,KACAA,GAAU9f,QAAQwU,KAGpB3b,KAAKsI,UAAUnB,QAAQkT,IAErBA,EAAU1R,uBACXgT,EAAUhT,sBAIT3B,MACOnB,EAAEmB,GAAU,SAGlBkf,UACHlmB,KAAKsI,SACL4e,OAGIvU,EAAW,eACT0U,EAAcxhB,EAAEuC,MAAMA,EAAM2K,sBACjBlM,EAAKyB,WAGhB0V,EAAanY,EAAEuC,MAAMA,EAAMwK,qBAChBqU,MAGfA,GAAU9f,QAAQkgB,KAClBxgB,EAAKyB,UAAUnB,QAAQ6W,IAGvB5b,OACG8jB,UAAU9jB,EAAQA,EAAO3M,WAAYkd,YAM9C9J,QArIe,aAsIXC,WAAW9I,KAAKsI,SA3HM,eA4HnBA,SAAW,QAMlB4d,UA7Ie,SA6ILjxB,EAASitB,EAAWjgB,OACxBqlB,SAOEC,KANqB,OAAvBrF,EAAU1sB,SACKqQ,EAAEqc,GAAWzjB,KAAK+L,EAAS4c,WAE3BvhB,EAAEqc,GAAW1nB,SAASgQ,EAASS,SAGX,GACjC+H,EAAkB/Q,GACnBuD,EAAKuC,yBACJwf,GAAU1hB,EAAE0hB,GAAQje,SAASjB,EAAUkB,MAEvCoJ,EAAW,kBAAMrE,EAAKkZ,oBAC1BvyB,EACAsyB,EACAvU,EACA/Q,IAGEslB,GAAUvU,IACVuU,GACC7gB,IAAIlB,EAAKmB,eAAgBgM,GACzB7K,qBArJmB,SA2JpByf,KACAA,GAAQne,YAAYf,EAAUgB,SAIpCme,oBA/Ke,SA+KKvyB,EAASsyB,EAAQvU,EAAiB/Q,MAChDslB,EAAQ,GACRA,GAAQne,YAAYf,EAAU4C,YAE1Bwc,EAAgB5hB,EAAE0hB,EAAO9xB,YAAYgJ,KACzC+L,EAASkd,uBACT,GAEED,KACAA,GAAere,YAAYf,EAAU4C,QAGL,QAAhCsc,EAAOtgB,aAAa,WACfzD,aAAa,iBAAiB,QAIvCvO,GAAS8a,SAAS1H,EAAU4C,QACO,QAAjChW,EAAQgS,aAAa,WACfzD,aAAa,iBAAiB,GAGpCwP,KACGvC,OAAOxb,KACVA,GAAS8a,SAAS1H,EAAUgB,SAE5BpU,GAASmU,YAAYf,EAAUkB,MAG/BtU,EAAQQ,YACRoQ,EAAE5Q,EAAQQ,YAAY6T,SAASjB,EAAUsf,eAAgB,KAErDC,EAAkB/hB,EAAE5Q,GAAS+T,QAAQwB,EAAS+b,UAAU,GAC1DqB,KACAA,GAAiBnpB,KAAK+L,EAASgc,iBAAiBzW,SAAS1H,EAAU4C,UAG/DzH,aAAa,iBAAiB,GAGpCvB,UAQC2H,iBA/NQ,SA+NStC,UACftH,KAAK6J,KAAK,eACT0J,EAAQ1N,EAAE7F,MACZV,EAAUiU,EAAMjU,KAvNE,aAyNjBA,MACI,IAAI0nB,EAAIhnB,QACTV,KA3Nc,SA2NCA,IAGD,iBAAXgI,EAAqB,IACF,oBAAjBhI,EAAKgI,SACR,IAAIO,MAAJ,oBAA8BP,EAA9B,OAEHA,uDAnOe,iCAiP1BzR,UACCqU,GAAG9B,EAAM+B,eAAgBK,EAASI,YAAa,SAAU5I,KAClDiI,mBACFL,iBAAiB7U,KAAK8Q,EAAE7F,MAAO,YAUrCH,GAAF,IAAyBmnB,EAAIpd,mBAC3B/J,GAAF,IAAWwK,YAAc2c,IACvBnnB,GAAF,IAAWyK,WAAc,oBACrBzK,GAAF,IAAasI,EACN6e,EAAIpd,kBAGNod,EA/QI,UCSb,cACmB,oBAANnhB,QACH,IAAIgC,MAAM,sGAGZggB,EAAUhiB,EAAEhG,GAAGsT,OAAOla,MAAM,KAAK,GAAGA,MAAM,QAO5C4uB,EAAQ,GALK,GAKWA,EAAQ,GAJnB,GAFA,IAMoCA,EAAQ,IAJ5C,IAI+DA,EAAQ,IAAmBA,EAAQ,GAHlG,GAGmHA,EAAQ,IAF3H,QAGT,IAAIhgB,MAAM,+EAbpB", "sourcesContent": ["/**!\n * @fileOverview Kickass library to create and place poppers near their reference elements.\n * @version 1.12.5\n * @license\n * Copyright (c) 2016 <PERSON> and contributors\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\nvar nativeHints = ['native code', '[object MutationObserverConstructor]'];\n\n/**\n * Determine if a function is implemented natively (as opposed to a polyfill).\n * @method\n * @memberof Popper.Utils\n * @argument {Function | undefined} fn the function to check\n * @returns {Boolean}\n */\nvar isNative = (function (fn) {\n  return nativeHints.some(function (hint) {\n    return (fn || '').toString().indexOf(hint) > -1;\n  });\n});\n\nvar isBrowser = typeof window !== 'undefined';\nvar longerTimeoutBrowsers = ['Edge', 'Trident', 'Firefox'];\nvar timeoutDuration = 0;\nfor (var i = 0; i < longerTimeoutBrowsers.length; i += 1) {\n  if (isBrowser && navigator.userAgent.indexOf(longerTimeoutBrowsers[i]) >= 0) {\n    timeoutDuration = 1;\n    break;\n  }\n}\n\nfunction microtaskDebounce(fn) {\n  var scheduled = false;\n  var i = 0;\n  var elem = document.createElement('span');\n\n  // MutationObserver provides a mechanism for scheduling microtasks, which\n  // are scheduled *before* the next task. This gives us a way to debounce\n  // a function but ensure it's called *before* the next paint.\n  var observer = new MutationObserver(function () {\n    fn();\n    scheduled = false;\n  });\n\n  observer.observe(elem, { attributes: true });\n\n  return function () {\n    if (!scheduled) {\n      scheduled = true;\n      elem.setAttribute('x-index', i);\n      i = i + 1; // don't use compund (+=) because it doesn't get optimized in V8\n    }\n  };\n}\n\nfunction taskDebounce(fn) {\n  var scheduled = false;\n  return function () {\n    if (!scheduled) {\n      scheduled = true;\n      setTimeout(function () {\n        scheduled = false;\n        fn();\n      }, timeoutDuration);\n    }\n  };\n}\n\n// It's common for MutationObserver polyfills to be seen in the wild, however\n// these rely on Mutation Events which only occur when an element is connected\n// to the DOM. The algorithm used in this module does not use a connected element,\n// and so we must ensure that a *native* MutationObserver is available.\nvar supportsNativeMutationObserver = isBrowser && isNative(window.MutationObserver);\n\n/**\n* Create a debounced version of a method, that's asynchronously deferred\n* but called in the minimum time possible.\n*\n* @method\n* @memberof Popper.Utils\n* @argument {Function} fn\n* @returns {Function}\n*/\nvar debounce = supportsNativeMutationObserver ? microtaskDebounce : taskDebounce;\n\n/**\n * Check if the given variable is a function\n * @method\n * @memberof Popper.Utils\n * @argument {Any} functionToCheck - variable to check\n * @returns {Boolean} answer to: is a function?\n */\nfunction isFunction(functionToCheck) {\n  var getType = {};\n  return functionToCheck && getType.toString.call(functionToCheck) === '[object Function]';\n}\n\n/**\n * Get CSS computed property of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Eement} element\n * @argument {String} property\n */\nfunction getStyleComputedProperty(element, property) {\n  if (element.nodeType !== 1) {\n    return [];\n  }\n  // NOTE: 1 DOM access here\n  var css = window.getComputedStyle(element, null);\n  return property ? css[property] : css;\n}\n\n/**\n * Returns the parentNode or the host of the element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} parent\n */\nfunction getParentNode(element) {\n  if (element.nodeName === 'HTML') {\n    return element;\n  }\n  return element.parentNode || element.host;\n}\n\n/**\n * Returns the scrolling parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} scroll parent\n */\nfunction getScrollParent(element) {\n  // Return body, `getScroll` will take care to get the correct `scrollTop` from it\n  if (!element || ['HTML', 'BODY', '#document'].indexOf(element.nodeName) !== -1) {\n    return window.document.body;\n  }\n\n  // Firefox want us to check `-x` and `-y` variations as well\n\n  var _getStyleComputedProp = getStyleComputedProperty(element),\n      overflow = _getStyleComputedProp.overflow,\n      overflowX = _getStyleComputedProp.overflowX,\n      overflowY = _getStyleComputedProp.overflowY;\n\n  if (/(auto|scroll)/.test(overflow + overflowY + overflowX)) {\n    return element;\n  }\n\n  return getScrollParent(getParentNode(element));\n}\n\n/**\n * Returns the offset parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} offset parent\n */\nfunction getOffsetParent(element) {\n  // NOTE: 1 DOM access here\n  var offsetParent = element && element.offsetParent;\n  var nodeName = offsetParent && offsetParent.nodeName;\n\n  if (!nodeName || nodeName === 'BODY' || nodeName === 'HTML') {\n    return window.document.documentElement;\n  }\n\n  // .offsetParent will return the closest TD or TABLE in case\n  // no offsetParent is present, I hate this job...\n  if (['TD', 'TABLE'].indexOf(offsetParent.nodeName) !== -1 && getStyleComputedProperty(offsetParent, 'position') === 'static') {\n    return getOffsetParent(offsetParent);\n  }\n\n  return offsetParent;\n}\n\nfunction isOffsetContainer(element) {\n  var nodeName = element.nodeName;\n\n  if (nodeName === 'BODY') {\n    return false;\n  }\n  return nodeName === 'HTML' || getOffsetParent(element.firstElementChild) === element;\n}\n\n/**\n * Finds the root node (document, shadowDOM root) of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} node\n * @returns {Element} root node\n */\nfunction getRoot(node) {\n  if (node.parentNode !== null) {\n    return getRoot(node.parentNode);\n  }\n\n  return node;\n}\n\n/**\n * Finds the offset parent common to the two provided nodes\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element1\n * @argument {Element} element2\n * @returns {Element} common offset parent\n */\nfunction findCommonOffsetParent(element1, element2) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element1 || !element1.nodeType || !element2 || !element2.nodeType) {\n    return window.document.documentElement;\n  }\n\n  // Here we make sure to give as \"start\" the element that comes first in the DOM\n  var order = element1.compareDocumentPosition(element2) & Node.DOCUMENT_POSITION_FOLLOWING;\n  var start = order ? element1 : element2;\n  var end = order ? element2 : element1;\n\n  // Get common ancestor container\n  var range = document.createRange();\n  range.setStart(start, 0);\n  range.setEnd(end, 0);\n  var commonAncestorContainer = range.commonAncestorContainer;\n\n  // Both nodes are inside #document\n\n  if (element1 !== commonAncestorContainer && element2 !== commonAncestorContainer || start.contains(end)) {\n    if (isOffsetContainer(commonAncestorContainer)) {\n      return commonAncestorContainer;\n    }\n\n    return getOffsetParent(commonAncestorContainer);\n  }\n\n  // one of the nodes is inside shadowDOM, find which one\n  var element1root = getRoot(element1);\n  if (element1root.host) {\n    return findCommonOffsetParent(element1root.host, element2);\n  } else {\n    return findCommonOffsetParent(element1, getRoot(element2).host);\n  }\n}\n\n/**\n * Gets the scroll value of the given element in the given side (top and left)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {String} side `top` or `left`\n * @returns {number} amount of scrolled pixels\n */\nfunction getScroll(element) {\n  var side = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'top';\n\n  var upperSide = side === 'top' ? 'scrollTop' : 'scrollLeft';\n  var nodeName = element.nodeName;\n\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    var html = window.document.documentElement;\n    var scrollingElement = window.document.scrollingElement || html;\n    return scrollingElement[upperSide];\n  }\n\n  return element[upperSide];\n}\n\n/*\n * Sum or subtract the element scroll values (left and top) from a given rect object\n * @method\n * @memberof Popper.Utils\n * @param {Object} rect - Rect object you want to change\n * @param {HTMLElement} element - The element from the function reads the scroll values\n * @param {Boolean} subtract - set to true if you want to subtract the scroll values\n * @return {Object} rect - The modifier rect object\n */\nfunction includeScroll(rect, element) {\n  var subtract = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n\n  var scrollTop = getScroll(element, 'top');\n  var scrollLeft = getScroll(element, 'left');\n  var modifier = subtract ? -1 : 1;\n  rect.top += scrollTop * modifier;\n  rect.bottom += scrollTop * modifier;\n  rect.left += scrollLeft * modifier;\n  rect.right += scrollLeft * modifier;\n  return rect;\n}\n\n/*\n * Helper to detect borders of a given element\n * @method\n * @memberof Popper.Utils\n * @param {CSSStyleDeclaration} styles\n * Result of `getStyleComputedProperty` on the given element\n * @param {String} axis - `x` or `y`\n * @return {number} borders - The borders size of the given axis\n */\n\nfunction getBordersSize(styles, axis) {\n  var sideA = axis === 'x' ? 'Left' : 'Top';\n  var sideB = sideA === 'Left' ? 'Right' : 'Bottom';\n\n  return +styles['border' + sideA + 'Width'].split('px')[0] + +styles['border' + sideB + 'Width'].split('px')[0];\n}\n\n/**\n * Tells if you are running Internet Explorer 10\n * @method\n * @memberof Popper.Utils\n * @returns {Boolean} isIE10\n */\nvar isIE10 = undefined;\n\nvar isIE10$1 = function () {\n  if (isIE10 === undefined) {\n    isIE10 = navigator.appVersion.indexOf('MSIE 10') !== -1;\n  }\n  return isIE10;\n};\n\nfunction getSize(axis, body, html, computedStyle) {\n  return Math.max(body['offset' + axis], body['scroll' + axis], html['client' + axis], html['offset' + axis], html['scroll' + axis], isIE10$1() ? html['offset' + axis] + computedStyle['margin' + (axis === 'Height' ? 'Top' : 'Left')] + computedStyle['margin' + (axis === 'Height' ? 'Bottom' : 'Right')] : 0);\n}\n\nfunction getWindowSizes() {\n  var body = window.document.body;\n  var html = window.document.documentElement;\n  var computedStyle = isIE10$1() && window.getComputedStyle(html);\n\n  return {\n    height: getSize('Height', body, html, computedStyle),\n    width: getSize('Width', body, html, computedStyle)\n  };\n}\n\nvar classCallCheck = function (instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n};\n\nvar createClass = function () {\n  function defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n\n  return function (Constructor, protoProps, staticProps) {\n    if (protoProps) defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) defineProperties(Constructor, staticProps);\n    return Constructor;\n  };\n}();\n\n\n\n\n\nvar defineProperty = function (obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n};\n\nvar _extends = Object.assign || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n\n  return target;\n};\n\n/**\n * Given element offsets, generate an output similar to getBoundingClientRect\n * @method\n * @memberof Popper.Utils\n * @argument {Object} offsets\n * @returns {Object} ClientRect like output\n */\nfunction getClientRect(offsets) {\n  return _extends({}, offsets, {\n    right: offsets.left + offsets.width,\n    bottom: offsets.top + offsets.height\n  });\n}\n\n/**\n * Get bounding client rect of given element\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} element\n * @return {Object} client rect\n */\nfunction getBoundingClientRect(element) {\n  var rect = {};\n\n  // IE10 10 FIX: Please, don't ask, the element isn't\n  // considered in DOM in some circumstances...\n  // This isn't reproducible in IE10 compatibility mode of IE11\n  if (isIE10$1()) {\n    try {\n      rect = element.getBoundingClientRect();\n      var scrollTop = getScroll(element, 'top');\n      var scrollLeft = getScroll(element, 'left');\n      rect.top += scrollTop;\n      rect.left += scrollLeft;\n      rect.bottom += scrollTop;\n      rect.right += scrollLeft;\n    } catch (err) {}\n  } else {\n    rect = element.getBoundingClientRect();\n  }\n\n  var result = {\n    left: rect.left,\n    top: rect.top,\n    width: rect.right - rect.left,\n    height: rect.bottom - rect.top\n  };\n\n  // subtract scrollbar size from sizes\n  var sizes = element.nodeName === 'HTML' ? getWindowSizes() : {};\n  var width = sizes.width || element.clientWidth || result.right - result.left;\n  var height = sizes.height || element.clientHeight || result.bottom - result.top;\n\n  var horizScrollbar = element.offsetWidth - width;\n  var vertScrollbar = element.offsetHeight - height;\n\n  // if an hypothetical scrollbar is detected, we must be sure it's not a `border`\n  // we make this check conditional for performance reasons\n  if (horizScrollbar || vertScrollbar) {\n    var styles = getStyleComputedProperty(element);\n    horizScrollbar -= getBordersSize(styles, 'x');\n    vertScrollbar -= getBordersSize(styles, 'y');\n\n    result.width -= horizScrollbar;\n    result.height -= vertScrollbar;\n  }\n\n  return getClientRect(result);\n}\n\nfunction getOffsetRectRelativeToArbitraryNode(children, parent) {\n  var isIE10 = isIE10$1();\n  var isHTML = parent.nodeName === 'HTML';\n  var childrenRect = getBoundingClientRect(children);\n  var parentRect = getBoundingClientRect(parent);\n  var scrollParent = getScrollParent(children);\n\n  var styles = getStyleComputedProperty(parent);\n  var borderTopWidth = +styles.borderTopWidth.split('px')[0];\n  var borderLeftWidth = +styles.borderLeftWidth.split('px')[0];\n\n  var offsets = getClientRect({\n    top: childrenRect.top - parentRect.top - borderTopWidth,\n    left: childrenRect.left - parentRect.left - borderLeftWidth,\n    width: childrenRect.width,\n    height: childrenRect.height\n  });\n  offsets.marginTop = 0;\n  offsets.marginLeft = 0;\n\n  // Subtract margins of documentElement in case it's being used as parent\n  // we do this only on HTML because it's the only element that behaves\n  // differently when margins are applied to it. The margins are included in\n  // the box of the documentElement, in the other cases not.\n  if (!isIE10 && isHTML) {\n    var marginTop = +styles.marginTop.split('px')[0];\n    var marginLeft = +styles.marginLeft.split('px')[0];\n\n    offsets.top -= borderTopWidth - marginTop;\n    offsets.bottom -= borderTopWidth - marginTop;\n    offsets.left -= borderLeftWidth - marginLeft;\n    offsets.right -= borderLeftWidth - marginLeft;\n\n    // Attach marginTop and marginLeft because in some circumstances we may need them\n    offsets.marginTop = marginTop;\n    offsets.marginLeft = marginLeft;\n  }\n\n  if (isIE10 ? parent.contains(scrollParent) : parent === scrollParent && scrollParent.nodeName !== 'BODY') {\n    offsets = includeScroll(offsets, parent);\n  }\n\n  return offsets;\n}\n\nfunction getViewportOffsetRectRelativeToArtbitraryNode(element) {\n  var html = window.document.documentElement;\n  var relativeOffset = getOffsetRectRelativeToArbitraryNode(element, html);\n  var width = Math.max(html.clientWidth, window.innerWidth || 0);\n  var height = Math.max(html.clientHeight, window.innerHeight || 0);\n\n  var scrollTop = getScroll(html);\n  var scrollLeft = getScroll(html, 'left');\n\n  var offset = {\n    top: scrollTop - relativeOffset.top + relativeOffset.marginTop,\n    left: scrollLeft - relativeOffset.left + relativeOffset.marginLeft,\n    width: width,\n    height: height\n  };\n\n  return getClientRect(offset);\n}\n\n/**\n * Check if the given element is fixed or is inside a fixed parent\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {Element} customContainer\n * @returns {Boolean} answer to \"isFixed?\"\n */\nfunction isFixed(element) {\n  var nodeName = element.nodeName;\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    return false;\n  }\n  if (getStyleComputedProperty(element, 'position') === 'fixed') {\n    return true;\n  }\n  return isFixed(getParentNode(element));\n}\n\n/**\n * Computed the boundaries limits and return them\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} popper\n * @param {HTMLElement} reference\n * @param {number} padding\n * @param {HTMLElement} boundariesElement - Element used to define the boundaries\n * @returns {Object} Coordinates of the boundaries\n */\nfunction getBoundaries(popper, reference, padding, boundariesElement) {\n  // NOTE: 1 DOM access here\n  var boundaries = { top: 0, left: 0 };\n  var offsetParent = findCommonOffsetParent(popper, reference);\n\n  // Handle viewport case\n  if (boundariesElement === 'viewport') {\n    boundaries = getViewportOffsetRectRelativeToArtbitraryNode(offsetParent);\n  } else {\n    // Handle other cases based on DOM element used as boundaries\n    var boundariesNode = void 0;\n    if (boundariesElement === 'scrollParent') {\n      boundariesNode = getScrollParent(getParentNode(popper));\n      if (boundariesNode.nodeName === 'BODY') {\n        boundariesNode = window.document.documentElement;\n      }\n    } else if (boundariesElement === 'window') {\n      boundariesNode = window.document.documentElement;\n    } else {\n      boundariesNode = boundariesElement;\n    }\n\n    var offsets = getOffsetRectRelativeToArbitraryNode(boundariesNode, offsetParent);\n\n    // In case of HTML, we need a different computation\n    if (boundariesNode.nodeName === 'HTML' && !isFixed(offsetParent)) {\n      var _getWindowSizes = getWindowSizes(),\n          height = _getWindowSizes.height,\n          width = _getWindowSizes.width;\n\n      boundaries.top += offsets.top - offsets.marginTop;\n      boundaries.bottom = height + offsets.top;\n      boundaries.left += offsets.left - offsets.marginLeft;\n      boundaries.right = width + offsets.left;\n    } else {\n      // for all the other DOM elements, this one is good\n      boundaries = offsets;\n    }\n  }\n\n  // Add paddings\n  boundaries.left += padding;\n  boundaries.top += padding;\n  boundaries.right -= padding;\n  boundaries.bottom -= padding;\n\n  return boundaries;\n}\n\nfunction getArea(_ref) {\n  var width = _ref.width,\n      height = _ref.height;\n\n  return width * height;\n}\n\n/**\n * Utility used to transform the `auto` placement to the placement with more\n * available space.\n * @method\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction computeAutoPlacement(placement, refRect, popper, reference, boundariesElement) {\n  var padding = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : 0;\n\n  if (placement.indexOf('auto') === -1) {\n    return placement;\n  }\n\n  var boundaries = getBoundaries(popper, reference, padding, boundariesElement);\n\n  var rects = {\n    top: {\n      width: boundaries.width,\n      height: refRect.top - boundaries.top\n    },\n    right: {\n      width: boundaries.right - refRect.right,\n      height: boundaries.height\n    },\n    bottom: {\n      width: boundaries.width,\n      height: boundaries.bottom - refRect.bottom\n    },\n    left: {\n      width: refRect.left - boundaries.left,\n      height: boundaries.height\n    }\n  };\n\n  var sortedAreas = Object.keys(rects).map(function (key) {\n    return _extends({\n      key: key\n    }, rects[key], {\n      area: getArea(rects[key])\n    });\n  }).sort(function (a, b) {\n    return b.area - a.area;\n  });\n\n  var filteredAreas = sortedAreas.filter(function (_ref2) {\n    var width = _ref2.width,\n        height = _ref2.height;\n    return width >= popper.clientWidth && height >= popper.clientHeight;\n  });\n\n  var computedPlacement = filteredAreas.length > 0 ? filteredAreas[0].key : sortedAreas[0].key;\n\n  var variation = placement.split('-')[1];\n\n  return computedPlacement + (variation ? '-' + variation : '');\n}\n\n/**\n * Get offsets to the reference element\n * @method\n * @memberof Popper.Utils\n * @param {Object} state\n * @param {Element} popper - the popper element\n * @param {Element} reference - the reference element (the popper will be relative to this)\n * @returns {Object} An object containing the offsets which will be applied to the popper\n */\nfunction getReferenceOffsets(state, popper, reference) {\n  var commonOffsetParent = findCommonOffsetParent(popper, reference);\n  return getOffsetRectRelativeToArbitraryNode(reference, commonOffsetParent);\n}\n\n/**\n * Get the outer sizes of the given element (offset size + margins)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Object} object containing width and height properties\n */\nfunction getOuterSizes(element) {\n  var styles = window.getComputedStyle(element);\n  var x = parseFloat(styles.marginTop) + parseFloat(styles.marginBottom);\n  var y = parseFloat(styles.marginLeft) + parseFloat(styles.marginRight);\n  var result = {\n    width: element.offsetWidth + y,\n    height: element.offsetHeight + x\n  };\n  return result;\n}\n\n/**\n * Get the opposite placement of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement\n * @returns {String} flipped placement\n */\nfunction getOppositePlacement(placement) {\n  var hash = { left: 'right', right: 'left', bottom: 'top', top: 'bottom' };\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}\n\n/**\n * Get offsets to the popper\n * @method\n * @memberof Popper.Utils\n * @param {Object} position - CSS position the Popper will get applied\n * @param {HTMLElement} popper - the popper element\n * @param {Object} referenceOffsets - the reference offsets (the popper will be relative to this)\n * @param {String} placement - one of the valid placement options\n * @returns {Object} popperOffsets - An object containing the offsets which will be applied to the popper\n */\nfunction getPopperOffsets(popper, referenceOffsets, placement) {\n  placement = placement.split('-')[0];\n\n  // Get popper node sizes\n  var popperRect = getOuterSizes(popper);\n\n  // Add position, width and height to our offsets object\n  var popperOffsets = {\n    width: popperRect.width,\n    height: popperRect.height\n  };\n\n  // depending by the popper placement we have to compute its offsets slightly differently\n  var isHoriz = ['right', 'left'].indexOf(placement) !== -1;\n  var mainSide = isHoriz ? 'top' : 'left';\n  var secondarySide = isHoriz ? 'left' : 'top';\n  var measurement = isHoriz ? 'height' : 'width';\n  var secondaryMeasurement = !isHoriz ? 'height' : 'width';\n\n  popperOffsets[mainSide] = referenceOffsets[mainSide] + referenceOffsets[measurement] / 2 - popperRect[measurement] / 2;\n  if (placement === secondarySide) {\n    popperOffsets[secondarySide] = referenceOffsets[secondarySide] - popperRect[secondaryMeasurement];\n  } else {\n    popperOffsets[secondarySide] = referenceOffsets[getOppositePlacement(secondarySide)];\n  }\n\n  return popperOffsets;\n}\n\n/**\n * Mimics the `find` method of Array\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nfunction find(arr, check) {\n  // use native find if supported\n  if (Array.prototype.find) {\n    return arr.find(check);\n  }\n\n  // use `filter` to obtain the same behavior of `find`\n  return arr.filter(check)[0];\n}\n\n/**\n * Return the index of the matching object\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nfunction findIndex(arr, prop, value) {\n  // use native findIndex if supported\n  if (Array.prototype.findIndex) {\n    return arr.findIndex(function (cur) {\n      return cur[prop] === value;\n    });\n  }\n\n  // use `find` + `indexOf` if `findIndex` isn't supported\n  var match = find(arr, function (obj) {\n    return obj[prop] === value;\n  });\n  return arr.indexOf(match);\n}\n\n/**\n * Loop trough the list of modifiers and run them in order,\n * each of them will then edit the data object.\n * @method\n * @memberof Popper.Utils\n * @param {dataObject} data\n * @param {Array} modifiers\n * @param {String} ends - Optional modifier name used as stopper\n * @returns {dataObject}\n */\nfunction runModifiers(modifiers, data, ends) {\n  var modifiersToRun = ends === undefined ? modifiers : modifiers.slice(0, findIndex(modifiers, 'name', ends));\n\n  modifiersToRun.forEach(function (modifier) {\n    if (modifier.function) {\n      console.warn('`modifier.function` is deprecated, use `modifier.fn`!');\n    }\n    var fn = modifier.function || modifier.fn;\n    if (modifier.enabled && isFunction(fn)) {\n      // Add properties to offsets to make them a complete clientRect object\n      // we do this before each modifier to make sure the previous one doesn't\n      // mess with these values\n      data.offsets.popper = getClientRect(data.offsets.popper);\n      data.offsets.reference = getClientRect(data.offsets.reference);\n\n      data = fn(data, modifier);\n    }\n  });\n\n  return data;\n}\n\n/**\n * Updates the position of the popper, computing the new offsets and applying\n * the new style.<br />\n * Prefer `scheduleUpdate` over `update` because of performance reasons.\n * @method\n * @memberof Popper\n */\nfunction update() {\n  // if popper is destroyed, don't perform any further update\n  if (this.state.isDestroyed) {\n    return;\n  }\n\n  var data = {\n    instance: this,\n    styles: {},\n    arrowStyles: {},\n    attributes: {},\n    flipped: false,\n    offsets: {}\n  };\n\n  // compute reference element offsets\n  data.offsets.reference = getReferenceOffsets(this.state, this.popper, this.reference);\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  data.placement = computeAutoPlacement(this.options.placement, data.offsets.reference, this.popper, this.reference, this.options.modifiers.flip.boundariesElement, this.options.modifiers.flip.padding);\n\n  // store the computed placement inside `originalPlacement`\n  data.originalPlacement = data.placement;\n\n  // compute the popper offsets\n  data.offsets.popper = getPopperOffsets(this.popper, data.offsets.reference, data.placement);\n  data.offsets.popper.position = 'absolute';\n\n  // run the modifiers\n  data = runModifiers(this.modifiers, data);\n\n  // the first `update` will call `onCreate` callback\n  // the other ones will call `onUpdate` callback\n  if (!this.state.isCreated) {\n    this.state.isCreated = true;\n    this.options.onCreate(data);\n  } else {\n    this.options.onUpdate(data);\n  }\n}\n\n/**\n * Helper used to know if the given modifier is enabled.\n * @method\n * @memberof Popper.Utils\n * @returns {Boolean}\n */\nfunction isModifierEnabled(modifiers, modifierName) {\n  return modifiers.some(function (_ref) {\n    var name = _ref.name,\n        enabled = _ref.enabled;\n    return enabled && name === modifierName;\n  });\n}\n\n/**\n * Get the prefixed supported property name\n * @method\n * @memberof Popper.Utils\n * @argument {String} property (camelCase)\n * @returns {String} prefixed property (camelCase or PascalCase, depending on the vendor prefix)\n */\nfunction getSupportedPropertyName(property) {\n  var prefixes = [false, 'ms', 'Webkit', 'Moz', 'O'];\n  var upperProp = property.charAt(0).toUpperCase() + property.slice(1);\n\n  for (var i = 0; i < prefixes.length - 1; i++) {\n    var prefix = prefixes[i];\n    var toCheck = prefix ? '' + prefix + upperProp : property;\n    if (typeof window.document.body.style[toCheck] !== 'undefined') {\n      return toCheck;\n    }\n  }\n  return null;\n}\n\n/**\n * Destroy the popper\n * @method\n * @memberof Popper\n */\nfunction destroy() {\n  this.state.isDestroyed = true;\n\n  // touch DOM only if `applyStyle` modifier is enabled\n  if (isModifierEnabled(this.modifiers, 'applyStyle')) {\n    this.popper.removeAttribute('x-placement');\n    this.popper.style.left = '';\n    this.popper.style.position = '';\n    this.popper.style.top = '';\n    this.popper.style[getSupportedPropertyName('transform')] = '';\n  }\n\n  this.disableEventListeners();\n\n  // remove the popper if user explicity asked for the deletion on destroy\n  // do not use `remove` because IE11 doesn't support it\n  if (this.options.removeOnDestroy) {\n    this.popper.parentNode.removeChild(this.popper);\n  }\n  return this;\n}\n\nfunction attachToScrollParents(scrollParent, event, callback, scrollParents) {\n  var isBody = scrollParent.nodeName === 'BODY';\n  var target = isBody ? window : scrollParent;\n  target.addEventListener(event, callback, { passive: true });\n\n  if (!isBody) {\n    attachToScrollParents(getScrollParent(target.parentNode), event, callback, scrollParents);\n  }\n  scrollParents.push(target);\n}\n\n/**\n * Setup needed event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nfunction setupEventListeners(reference, options, state, updateBound) {\n  // Resize event listener on window\n  state.updateBound = updateBound;\n  window.addEventListener('resize', state.updateBound, { passive: true });\n\n  // Scroll event listener on scroll parents\n  var scrollElement = getScrollParent(reference);\n  attachToScrollParents(scrollElement, 'scroll', state.updateBound, state.scrollParents);\n  state.scrollElement = scrollElement;\n  state.eventsEnabled = true;\n\n  return state;\n}\n\n/**\n * It will add resize/scroll events and start recalculating\n * position of the popper element when they are triggered.\n * @method\n * @memberof Popper\n */\nfunction enableEventListeners() {\n  if (!this.state.eventsEnabled) {\n    this.state = setupEventListeners(this.reference, this.options, this.state, this.scheduleUpdate);\n  }\n}\n\n/**\n * Remove event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nfunction removeEventListeners(reference, state) {\n  // Remove resize event listener on window\n  window.removeEventListener('resize', state.updateBound);\n\n  // Remove scroll event listener on scroll parents\n  state.scrollParents.forEach(function (target) {\n    target.removeEventListener('scroll', state.updateBound);\n  });\n\n  // Reset state\n  state.updateBound = null;\n  state.scrollParents = [];\n  state.scrollElement = null;\n  state.eventsEnabled = false;\n  return state;\n}\n\n/**\n * It will remove resize/scroll events and won't recalculate popper position\n * when they are triggered. It also won't trigger onUpdate callback anymore,\n * unless you call `update` method manually.\n * @method\n * @memberof Popper\n */\nfunction disableEventListeners() {\n  if (this.state.eventsEnabled) {\n    window.cancelAnimationFrame(this.scheduleUpdate);\n    this.state = removeEventListeners(this.reference, this.state);\n  }\n}\n\n/**\n * Tells if a given input is a number\n * @method\n * @memberof Popper.Utils\n * @param {*} input to check\n * @return {Boolean}\n */\nfunction isNumeric(n) {\n  return n !== '' && !isNaN(parseFloat(n)) && isFinite(n);\n}\n\n/**\n * Set the style to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the style to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nfunction setStyles(element, styles) {\n  Object.keys(styles).forEach(function (prop) {\n    var unit = '';\n    // add unit if the value is numeric and is one of the following\n    if (['width', 'height', 'top', 'right', 'bottom', 'left'].indexOf(prop) !== -1 && isNumeric(styles[prop])) {\n      unit = 'px';\n    }\n    element.style[prop] = styles[prop] + unit;\n  });\n}\n\n/**\n * Set the attributes to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the attributes to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nfunction setAttributes(element, attributes) {\n  Object.keys(attributes).forEach(function (prop) {\n    var value = attributes[prop];\n    if (value !== false) {\n      element.setAttribute(prop, attributes[prop]);\n    } else {\n      element.removeAttribute(prop);\n    }\n  });\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} data.styles - List of style properties - values to apply to popper element\n * @argument {Object} data.attributes - List of attribute properties - values to apply to popper element\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The same data object\n */\nfunction applyStyle(data) {\n  // any property present in `data.styles` will be applied to the popper,\n  // in this way we can make the 3rd party modifiers add custom styles to it\n  // Be aware, modifiers could override the properties defined in the previous\n  // lines of this modifier!\n  setStyles(data.instance.popper, data.styles);\n\n  // any property present in `data.attributes` will be applied to the popper,\n  // they will be set as HTML attributes of the element\n  setAttributes(data.instance.popper, data.attributes);\n\n  // if arrowElement is defined and arrowStyles has some properties\n  if (data.arrowElement && Object.keys(data.arrowStyles).length) {\n    setStyles(data.arrowElement, data.arrowStyles);\n  }\n\n  return data;\n}\n\n/**\n * Set the x-placement attribute before everything else because it could be used\n * to add margins to the popper margins needs to be calculated to get the\n * correct popper offsets.\n * @method\n * @memberof Popper.modifiers\n * @param {HTMLElement} reference - The reference element used to position the popper\n * @param {HTMLElement} popper - The HTML element used as popper.\n * @param {Object} options - Popper.js options\n */\nfunction applyStyleOnLoad(reference, popper, options, modifierOptions, state) {\n  // compute reference element offsets\n  var referenceOffsets = getReferenceOffsets(state, popper, reference);\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  var placement = computeAutoPlacement(options.placement, referenceOffsets, popper, reference, options.modifiers.flip.boundariesElement, options.modifiers.flip.padding);\n\n  popper.setAttribute('x-placement', placement);\n\n  // Apply `position` to popper before anything else because\n  // without the position applied we can't guarantee correct computations\n  setStyles(popper, { position: 'absolute' });\n\n  return options;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction computeStyle(data, options) {\n  var x = options.x,\n      y = options.y;\n  var popper = data.offsets.popper;\n\n  // Remove this legacy support in Popper.js v2\n\n  var legacyGpuAccelerationOption = find(data.instance.modifiers, function (modifier) {\n    return modifier.name === 'applyStyle';\n  }).gpuAcceleration;\n  if (legacyGpuAccelerationOption !== undefined) {\n    console.warn('WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!');\n  }\n  var gpuAcceleration = legacyGpuAccelerationOption !== undefined ? legacyGpuAccelerationOption : options.gpuAcceleration;\n\n  var offsetParent = getOffsetParent(data.instance.popper);\n  var offsetParentRect = getBoundingClientRect(offsetParent);\n\n  // Styles\n  var styles = {\n    position: popper.position\n  };\n\n  // floor sides to avoid blurry text\n  var offsets = {\n    left: Math.floor(popper.left),\n    top: Math.floor(popper.top),\n    bottom: Math.floor(popper.bottom),\n    right: Math.floor(popper.right)\n  };\n\n  var sideA = x === 'bottom' ? 'top' : 'bottom';\n  var sideB = y === 'right' ? 'left' : 'right';\n\n  // if gpuAcceleration is set to `true` and transform is supported,\n  //  we use `translate3d` to apply the position to the popper we\n  // automatically use the supported prefixed version if needed\n  var prefixedProperty = getSupportedPropertyName('transform');\n\n  // now, let's make a step back and look at this code closely (wtf?)\n  // If the content of the popper grows once it's been positioned, it\n  // may happen that the popper gets misplaced because of the new content\n  // overflowing its reference element\n  // To avoid this problem, we provide two options (x and y), which allow\n  // the consumer to define the offset origin.\n  // If we position a popper on top of a reference element, we can set\n  // `x` to `top` to make the popper grow towards its top instead of\n  // its bottom.\n  var left = void 0,\n      top = void 0;\n  if (sideA === 'bottom') {\n    top = -offsetParentRect.height + offsets.bottom;\n  } else {\n    top = offsets.top;\n  }\n  if (sideB === 'right') {\n    left = -offsetParentRect.width + offsets.right;\n  } else {\n    left = offsets.left;\n  }\n  if (gpuAcceleration && prefixedProperty) {\n    styles[prefixedProperty] = 'translate3d(' + left + 'px, ' + top + 'px, 0)';\n    styles[sideA] = 0;\n    styles[sideB] = 0;\n    styles.willChange = 'transform';\n  } else {\n    // othwerise, we use the standard `top`, `left`, `bottom` and `right` properties\n    var invertTop = sideA === 'bottom' ? -1 : 1;\n    var invertLeft = sideB === 'right' ? -1 : 1;\n    styles[sideA] = top * invertTop;\n    styles[sideB] = left * invertLeft;\n    styles.willChange = sideA + ', ' + sideB;\n  }\n\n  // Attributes\n  var attributes = {\n    'x-placement': data.placement\n  };\n\n  // Update `data` attributes, styles and arrowStyles\n  data.attributes = _extends({}, attributes, data.attributes);\n  data.styles = _extends({}, styles, data.styles);\n  data.arrowStyles = _extends({}, data.offsets.arrow, data.arrowStyles);\n\n  return data;\n}\n\n/**\n * Helper used to know if the given modifier depends from another one.<br />\n * It checks if the needed modifier is listed and enabled.\n * @method\n * @memberof Popper.Utils\n * @param {Array} modifiers - list of modifiers\n * @param {String} requestingName - name of requesting modifier\n * @param {String} requestedName - name of requested modifier\n * @returns {Boolean}\n */\nfunction isModifierRequired(modifiers, requestingName, requestedName) {\n  var requesting = find(modifiers, function (_ref) {\n    var name = _ref.name;\n    return name === requestingName;\n  });\n\n  var isRequired = !!requesting && modifiers.some(function (modifier) {\n    return modifier.name === requestedName && modifier.enabled && modifier.order < requesting.order;\n  });\n\n  if (!isRequired) {\n    var _requesting = '`' + requestingName + '`';\n    var requested = '`' + requestedName + '`';\n    console.warn(requested + ' modifier is required by ' + _requesting + ' modifier in order to work, be sure to include it before ' + _requesting + '!');\n  }\n  return isRequired;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction arrow(data, options) {\n  // arrow depends on keepTogether in order to work\n  if (!isModifierRequired(data.instance.modifiers, 'arrow', 'keepTogether')) {\n    return data;\n  }\n\n  var arrowElement = options.element;\n\n  // if arrowElement is a string, suppose it's a CSS selector\n  if (typeof arrowElement === 'string') {\n    arrowElement = data.instance.popper.querySelector(arrowElement);\n\n    // if arrowElement is not found, don't run the modifier\n    if (!arrowElement) {\n      return data;\n    }\n  } else {\n    // if the arrowElement isn't a query selector we must check that the\n    // provided DOM node is child of its popper node\n    if (!data.instance.popper.contains(arrowElement)) {\n      console.warn('WARNING: `arrow.element` must be child of its popper element!');\n      return data;\n    }\n  }\n\n  var placement = data.placement.split('-')[0];\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var isVertical = ['left', 'right'].indexOf(placement) !== -1;\n\n  var len = isVertical ? 'height' : 'width';\n  var sideCapitalized = isVertical ? 'Top' : 'Left';\n  var side = sideCapitalized.toLowerCase();\n  var altSide = isVertical ? 'left' : 'top';\n  var opSide = isVertical ? 'bottom' : 'right';\n  var arrowElementSize = getOuterSizes(arrowElement)[len];\n\n  //\n  // extends keepTogether behavior making sure the popper and its\n  // reference have enough pixels in conjuction\n  //\n\n  // top/left side\n  if (reference[opSide] - arrowElementSize < popper[side]) {\n    data.offsets.popper[side] -= popper[side] - (reference[opSide] - arrowElementSize);\n  }\n  // bottom/right side\n  if (reference[side] + arrowElementSize > popper[opSide]) {\n    data.offsets.popper[side] += reference[side] + arrowElementSize - popper[opSide];\n  }\n\n  // compute center of the popper\n  var center = reference[side] + reference[len] / 2 - arrowElementSize / 2;\n\n  // Compute the sideValue using the updated popper offsets\n  // take popper margin in account because we don't have this info available\n  var popperMarginSide = getStyleComputedProperty(data.instance.popper, 'margin' + sideCapitalized).replace('px', '');\n  var sideValue = center - getClientRect(data.offsets.popper)[side] - popperMarginSide;\n\n  // prevent arrowElement from being placed not contiguously to its popper\n  sideValue = Math.max(Math.min(popper[len] - arrowElementSize, sideValue), 0);\n\n  data.arrowElement = arrowElement;\n  data.offsets.arrow = {};\n  data.offsets.arrow[side] = Math.round(sideValue);\n  data.offsets.arrow[altSide] = ''; // make sure to unset any eventual altSide value from the DOM node\n\n  return data;\n}\n\n/**\n * Get the opposite placement variation of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement variation\n * @returns {String} flipped placement variation\n */\nfunction getOppositeVariation(variation) {\n  if (variation === 'end') {\n    return 'start';\n  } else if (variation === 'start') {\n    return 'end';\n  }\n  return variation;\n}\n\n/**\n * List of accepted placements to use as values of the `placement` option.<br />\n * Valid placements are:\n * - `auto`\n * - `top`\n * - `right`\n * - `bottom`\n * - `left`\n *\n * Each placement can have a variation from this list:\n * - `-start`\n * - `-end`\n *\n * Variations are interpreted easily if you think of them as the left to right\n * written languages. Horizontally (`top` and `bottom`), `start` is left and `end`\n * is right.<br />\n * Vertically (`left` and `right`), `start` is top and `end` is bottom.\n *\n * Some valid examples are:\n * - `top-end` (on top of reference, right aligned)\n * - `right-start` (on right of reference, top aligned)\n * - `bottom` (on bottom, centered)\n * - `auto-right` (on the side with more space available, alignment depends by placement)\n *\n * @static\n * @type {Array}\n * @enum {String}\n * @readonly\n * @method placements\n * @memberof Popper\n */\nvar placements = ['auto-start', 'auto', 'auto-end', 'top-start', 'top', 'top-end', 'right-start', 'right', 'right-end', 'bottom-end', 'bottom', 'bottom-start', 'left-end', 'left', 'left-start'];\n\n// Get rid of `auto` `auto-start` and `auto-end`\nvar validPlacements = placements.slice(3);\n\n/**\n * Given an initial placement, returns all the subsequent placements\n * clockwise (or counter-clockwise).\n *\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement - A valid placement (it accepts variations)\n * @argument {Boolean} counter - Set to true to walk the placements counterclockwise\n * @returns {Array} placements including their variations\n */\nfunction clockwise(placement) {\n  var counter = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n  var index = validPlacements.indexOf(placement);\n  var arr = validPlacements.slice(index + 1).concat(validPlacements.slice(0, index));\n  return counter ? arr.reverse() : arr;\n}\n\nvar BEHAVIORS = {\n  FLIP: 'flip',\n  CLOCKWISE: 'clockwise',\n  COUNTERCLOCKWISE: 'counterclockwise'\n};\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction flip(data, options) {\n  // if `inner` modifier is enabled, we can't use the `flip` modifier\n  if (isModifierEnabled(data.instance.modifiers, 'inner')) {\n    return data;\n  }\n\n  if (data.flipped && data.placement === data.originalPlacement) {\n    // seems like flip is trying to loop, probably there's not enough space on any of the flippable sides\n    return data;\n  }\n\n  var boundaries = getBoundaries(data.instance.popper, data.instance.reference, options.padding, options.boundariesElement);\n\n  var placement = data.placement.split('-')[0];\n  var placementOpposite = getOppositePlacement(placement);\n  var variation = data.placement.split('-')[1] || '';\n\n  var flipOrder = [];\n\n  switch (options.behavior) {\n    case BEHAVIORS.FLIP:\n      flipOrder = [placement, placementOpposite];\n      break;\n    case BEHAVIORS.CLOCKWISE:\n      flipOrder = clockwise(placement);\n      break;\n    case BEHAVIORS.COUNTERCLOCKWISE:\n      flipOrder = clockwise(placement, true);\n      break;\n    default:\n      flipOrder = options.behavior;\n  }\n\n  flipOrder.forEach(function (step, index) {\n    if (placement !== step || flipOrder.length === index + 1) {\n      return data;\n    }\n\n    placement = data.placement.split('-')[0];\n    placementOpposite = getOppositePlacement(placement);\n\n    var popperOffsets = data.offsets.popper;\n    var refOffsets = data.offsets.reference;\n\n    // using floor because the reference offsets may contain decimals we are not going to consider here\n    var floor = Math.floor;\n    var overlapsRef = placement === 'left' && floor(popperOffsets.right) > floor(refOffsets.left) || placement === 'right' && floor(popperOffsets.left) < floor(refOffsets.right) || placement === 'top' && floor(popperOffsets.bottom) > floor(refOffsets.top) || placement === 'bottom' && floor(popperOffsets.top) < floor(refOffsets.bottom);\n\n    var overflowsLeft = floor(popperOffsets.left) < floor(boundaries.left);\n    var overflowsRight = floor(popperOffsets.right) > floor(boundaries.right);\n    var overflowsTop = floor(popperOffsets.top) < floor(boundaries.top);\n    var overflowsBottom = floor(popperOffsets.bottom) > floor(boundaries.bottom);\n\n    var overflowsBoundaries = placement === 'left' && overflowsLeft || placement === 'right' && overflowsRight || placement === 'top' && overflowsTop || placement === 'bottom' && overflowsBottom;\n\n    // flip the variation if required\n    var isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n    var flippedVariation = !!options.flipVariations && (isVertical && variation === 'start' && overflowsLeft || isVertical && variation === 'end' && overflowsRight || !isVertical && variation === 'start' && overflowsTop || !isVertical && variation === 'end' && overflowsBottom);\n\n    if (overlapsRef || overflowsBoundaries || flippedVariation) {\n      // this boolean to detect any flip loop\n      data.flipped = true;\n\n      if (overlapsRef || overflowsBoundaries) {\n        placement = flipOrder[index + 1];\n      }\n\n      if (flippedVariation) {\n        variation = getOppositeVariation(variation);\n      }\n\n      data.placement = placement + (variation ? '-' + variation : '');\n\n      // this object contains `position`, we want to preserve it along with\n      // any additional property we may add in the future\n      data.offsets.popper = _extends({}, data.offsets.popper, getPopperOffsets(data.instance.popper, data.offsets.reference, data.placement));\n\n      data = runModifiers(data.instance.modifiers, data, 'flip');\n    }\n  });\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction keepTogether(data) {\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var placement = data.placement.split('-')[0];\n  var floor = Math.floor;\n  var isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n  var side = isVertical ? 'right' : 'bottom';\n  var opSide = isVertical ? 'left' : 'top';\n  var measurement = isVertical ? 'width' : 'height';\n\n  if (popper[side] < floor(reference[opSide])) {\n    data.offsets.popper[opSide] = floor(reference[opSide]) - popper[measurement];\n  }\n  if (popper[opSide] > floor(reference[side])) {\n    data.offsets.popper[opSide] = floor(reference[side]);\n  }\n\n  return data;\n}\n\n/**\n * Converts a string containing value + unit into a px value number\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} str - Value + unit string\n * @argument {String} measurement - `height` or `width`\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @returns {Number|String}\n * Value in pixels, or original string if no values were extracted\n */\nfunction toValue(str, measurement, popperOffsets, referenceOffsets) {\n  // separate value from unit\n  var split = str.match(/((?:\\-|\\+)?\\d*\\.?\\d*)(.*)/);\n  var value = +split[1];\n  var unit = split[2];\n\n  // If it's not a number it's an operator, I guess\n  if (!value) {\n    return str;\n  }\n\n  if (unit.indexOf('%') === 0) {\n    var element = void 0;\n    switch (unit) {\n      case '%p':\n        element = popperOffsets;\n        break;\n      case '%':\n      case '%r':\n      default:\n        element = referenceOffsets;\n    }\n\n    var rect = getClientRect(element);\n    return rect[measurement] / 100 * value;\n  } else if (unit === 'vh' || unit === 'vw') {\n    // if is a vh or vw, we calculate the size based on the viewport\n    var size = void 0;\n    if (unit === 'vh') {\n      size = Math.max(document.documentElement.clientHeight, window.innerHeight || 0);\n    } else {\n      size = Math.max(document.documentElement.clientWidth, window.innerWidth || 0);\n    }\n    return size / 100 * value;\n  } else {\n    // if is an explicit pixel unit, we get rid of the unit and keep the value\n    // if is an implicit unit, it's px, and we return just the value\n    return value;\n  }\n}\n\n/**\n * Parse an `offset` string to extrapolate `x` and `y` numeric offsets.\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} offset\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @argument {String} basePlacement\n * @returns {Array} a two cells array with x and y offsets in numbers\n */\nfunction parseOffset(offset, popperOffsets, referenceOffsets, basePlacement) {\n  var offsets = [0, 0];\n\n  // Use height if placement is left or right and index is 0 otherwise use width\n  // in this way the first offset will use an axis and the second one\n  // will use the other one\n  var useHeight = ['right', 'left'].indexOf(basePlacement) !== -1;\n\n  // Split the offset string to obtain a list of values and operands\n  // The regex addresses values with the plus or minus sign in front (+10, -20, etc)\n  var fragments = offset.split(/(\\+|\\-)/).map(function (frag) {\n    return frag.trim();\n  });\n\n  // Detect if the offset string contains a pair of values or a single one\n  // they could be separated by comma or space\n  var divider = fragments.indexOf(find(fragments, function (frag) {\n    return frag.search(/,|\\s/) !== -1;\n  }));\n\n  if (fragments[divider] && fragments[divider].indexOf(',') === -1) {\n    console.warn('Offsets separated by white space(s) are deprecated, use a comma (,) instead.');\n  }\n\n  // If divider is found, we divide the list of values and operands to divide\n  // them by ofset X and Y.\n  var splitRegex = /\\s*,\\s*|\\s+/;\n  var ops = divider !== -1 ? [fragments.slice(0, divider).concat([fragments[divider].split(splitRegex)[0]]), [fragments[divider].split(splitRegex)[1]].concat(fragments.slice(divider + 1))] : [fragments];\n\n  // Convert the values with units to absolute pixels to allow our computations\n  ops = ops.map(function (op, index) {\n    // Most of the units rely on the orientation of the popper\n    var measurement = (index === 1 ? !useHeight : useHeight) ? 'height' : 'width';\n    var mergeWithPrevious = false;\n    return op\n    // This aggregates any `+` or `-` sign that aren't considered operators\n    // e.g.: 10 + +5 => [10, +, +5]\n    .reduce(function (a, b) {\n      if (a[a.length - 1] === '' && ['+', '-'].indexOf(b) !== -1) {\n        a[a.length - 1] = b;\n        mergeWithPrevious = true;\n        return a;\n      } else if (mergeWithPrevious) {\n        a[a.length - 1] += b;\n        mergeWithPrevious = false;\n        return a;\n      } else {\n        return a.concat(b);\n      }\n    }, [])\n    // Here we convert the string values into number values (in px)\n    .map(function (str) {\n      return toValue(str, measurement, popperOffsets, referenceOffsets);\n    });\n  });\n\n  // Loop trough the offsets arrays and execute the operations\n  ops.forEach(function (op, index) {\n    op.forEach(function (frag, index2) {\n      if (isNumeric(frag)) {\n        offsets[index] += frag * (op[index2 - 1] === '-' ? -1 : 1);\n      }\n    });\n  });\n  return offsets;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @argument {Number|String} options.offset=0\n * The offset value as described in the modifier description\n * @returns {Object} The data object, properly modified\n */\nfunction offset(data, _ref) {\n  var offset = _ref.offset;\n  var placement = data.placement,\n      _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var basePlacement = placement.split('-')[0];\n\n  var offsets = void 0;\n  if (isNumeric(+offset)) {\n    offsets = [+offset, 0];\n  } else {\n    offsets = parseOffset(offset, popper, reference, basePlacement);\n  }\n\n  if (basePlacement === 'left') {\n    popper.top += offsets[0];\n    popper.left -= offsets[1];\n  } else if (basePlacement === 'right') {\n    popper.top += offsets[0];\n    popper.left += offsets[1];\n  } else if (basePlacement === 'top') {\n    popper.left += offsets[0];\n    popper.top -= offsets[1];\n  } else if (basePlacement === 'bottom') {\n    popper.left += offsets[0];\n    popper.top += offsets[1];\n  }\n\n  data.popper = popper;\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction preventOverflow(data, options) {\n  var boundariesElement = options.boundariesElement || getOffsetParent(data.instance.popper);\n\n  // If offsetParent is the reference element, we really want to\n  // go one step up and use the next offsetParent as reference to\n  // avoid to make this modifier completely useless and look like broken\n  if (data.instance.reference === boundariesElement) {\n    boundariesElement = getOffsetParent(boundariesElement);\n  }\n\n  var boundaries = getBoundaries(data.instance.popper, data.instance.reference, options.padding, boundariesElement);\n  options.boundaries = boundaries;\n\n  var order = options.priority;\n  var popper = data.offsets.popper;\n\n  var check = {\n    primary: function primary(placement) {\n      var value = popper[placement];\n      if (popper[placement] < boundaries[placement] && !options.escapeWithReference) {\n        value = Math.max(popper[placement], boundaries[placement]);\n      }\n      return defineProperty({}, placement, value);\n    },\n    secondary: function secondary(placement) {\n      var mainSide = placement === 'right' ? 'left' : 'top';\n      var value = popper[mainSide];\n      if (popper[placement] > boundaries[placement] && !options.escapeWithReference) {\n        value = Math.min(popper[mainSide], boundaries[placement] - (placement === 'right' ? popper.width : popper.height));\n      }\n      return defineProperty({}, mainSide, value);\n    }\n  };\n\n  order.forEach(function (placement) {\n    var side = ['left', 'top'].indexOf(placement) !== -1 ? 'primary' : 'secondary';\n    popper = _extends({}, popper, check[side](placement));\n  });\n\n  data.offsets.popper = popper;\n\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction shift(data) {\n  var placement = data.placement;\n  var basePlacement = placement.split('-')[0];\n  var shiftvariation = placement.split('-')[1];\n\n  // if shift shiftvariation is specified, run the modifier\n  if (shiftvariation) {\n    var _data$offsets = data.offsets,\n        reference = _data$offsets.reference,\n        popper = _data$offsets.popper;\n\n    var isVertical = ['bottom', 'top'].indexOf(basePlacement) !== -1;\n    var side = isVertical ? 'left' : 'top';\n    var measurement = isVertical ? 'width' : 'height';\n\n    var shiftOffsets = {\n      start: defineProperty({}, side, reference[side]),\n      end: defineProperty({}, side, reference[side] + reference[measurement] - popper[measurement])\n    };\n\n    data.offsets.popper = _extends({}, popper, shiftOffsets[shiftvariation]);\n  }\n\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction hide(data) {\n  if (!isModifierRequired(data.instance.modifiers, 'hide', 'preventOverflow')) {\n    return data;\n  }\n\n  var refRect = data.offsets.reference;\n  var bound = find(data.instance.modifiers, function (modifier) {\n    return modifier.name === 'preventOverflow';\n  }).boundaries;\n\n  if (refRect.bottom < bound.top || refRect.left > bound.right || refRect.top > bound.bottom || refRect.right < bound.left) {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === true) {\n      return data;\n    }\n\n    data.hide = true;\n    data.attributes['x-out-of-boundaries'] = '';\n  } else {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === false) {\n      return data;\n    }\n\n    data.hide = false;\n    data.attributes['x-out-of-boundaries'] = false;\n  }\n\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction inner(data) {\n  var placement = data.placement;\n  var basePlacement = placement.split('-')[0];\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var isHoriz = ['left', 'right'].indexOf(basePlacement) !== -1;\n\n  var subtractLength = ['top', 'left'].indexOf(basePlacement) === -1;\n\n  popper[isHoriz ? 'left' : 'top'] = reference[basePlacement] - (subtractLength ? popper[isHoriz ? 'width' : 'height'] : 0);\n\n  data.placement = getOppositePlacement(placement);\n  data.offsets.popper = getClientRect(popper);\n\n  return data;\n}\n\n/**\n * Modifier function, each modifier can have a function of this type assigned\n * to its `fn` property.<br />\n * These functions will be called on each update, this means that you must\n * make sure they are performant enough to avoid performance bottlenecks.\n *\n * @function ModifierFn\n * @argument {dataObject} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {dataObject} The data object, properly modified\n */\n\n/**\n * Modifiers are plugins used to alter the behavior of your poppers.<br />\n * Popper.js uses a set of 9 modifiers to provide all the basic functionalities\n * needed by the library.\n *\n * Usually you don't want to override the `order`, `fn` and `onLoad` props.\n * All the other properties are configurations that could be tweaked.\n * @namespace modifiers\n */\nvar modifiers = {\n  /**\n   * Modifier used to shift the popper on the start or end of its reference\n   * element.<br />\n   * It will read the variation of the `placement` property.<br />\n   * It can be one either `-end` or `-start`.\n   * @memberof modifiers\n   * @inner\n   */\n  shift: {\n    /** @prop {number} order=100 - Index used to define the order of execution */\n    order: 100,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: shift\n  },\n\n  /**\n   * The `offset` modifier can shift your popper on both its axis.\n   *\n   * It accepts the following units:\n   * - `px` or unitless, interpreted as pixels\n   * - `%` or `%r`, percentage relative to the length of the reference element\n   * - `%p`, percentage relative to the length of the popper element\n   * - `vw`, CSS viewport width unit\n   * - `vh`, CSS viewport height unit\n   *\n   * For length is intended the main axis relative to the placement of the popper.<br />\n   * This means that if the placement is `top` or `bottom`, the length will be the\n   * `width`. In case of `left` or `right`, it will be the height.\n   *\n   * You can provide a single value (as `Number` or `String`), or a pair of values\n   * as `String` divided by a comma or one (or more) white spaces.<br />\n   * The latter is a deprecated method because it leads to confusion and will be\n   * removed in v2.<br />\n   * Additionally, it accepts additions and subtractions between different units.\n   * Note that multiplications and divisions aren't supported.\n   *\n   * Valid examples are:\n   * ```\n   * 10\n   * '10%'\n   * '10, 10'\n   * '10%, 10'\n   * '10 + 10%'\n   * '10 - 5vh + 3%'\n   * '-10px + 5vh, 5px - 6%'\n   * ```\n   * > **NB**: If you desire to apply offsets to your poppers in a way that may make them overlap\n   * > with their reference element, unfortunately, you will have to disable the `flip` modifier.\n   * > More on this [reading this issue](https://github.com/FezVrasta/popper.js/issues/373)\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  offset: {\n    /** @prop {number} order=200 - Index used to define the order of execution */\n    order: 200,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: offset,\n    /** @prop {Number|String} offset=0\n     * The offset value as described in the modifier description\n     */\n    offset: 0\n  },\n\n  /**\n   * Modifier used to prevent the popper from being positioned outside the boundary.\n   *\n   * An scenario exists where the reference itself is not within the boundaries.<br />\n   * We can say it has \"escaped the boundaries\" — or just \"escaped\".<br />\n   * In this case we need to decide whether the popper should either:\n   *\n   * - detach from the reference and remain \"trapped\" in the boundaries, or\n   * - if it should ignore the boundary and \"escape with its reference\"\n   *\n   * When `escapeWithReference` is set to`true` and reference is completely\n   * outside its boundaries, the popper will overflow (or completely leave)\n   * the boundaries in order to remain attached to the edge of the reference.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  preventOverflow: {\n    /** @prop {number} order=300 - Index used to define the order of execution */\n    order: 300,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: preventOverflow,\n    /**\n     * @prop {Array} [priority=['left','right','top','bottom']]\n     * Popper will try to prevent overflow following these priorities by default,\n     * then, it could overflow on the left and on top of the `boundariesElement`\n     */\n    priority: ['left', 'right', 'top', 'bottom'],\n    /**\n     * @prop {number} padding=5\n     * Amount of pixel used to define a minimum distance between the boundaries\n     * and the popper this makes sure the popper has always a little padding\n     * between the edges of its container\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='scrollParent'\n     * Boundaries used by the modifier, can be `scrollParent`, `window`,\n     * `viewport` or any DOM element.\n     */\n    boundariesElement: 'scrollParent'\n  },\n\n  /**\n   * Modifier used to make sure the reference and its popper stay near eachothers\n   * without leaving any gap between the two. Expecially useful when the arrow is\n   * enabled and you want to assure it to point to its reference element.\n   * It cares only about the first axis, you can still have poppers with margin\n   * between the popper and its reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  keepTogether: {\n    /** @prop {number} order=400 - Index used to define the order of execution */\n    order: 400,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: keepTogether\n  },\n\n  /**\n   * This modifier is used to move the `arrowElement` of the popper to make\n   * sure it is positioned between the reference element and its popper element.\n   * It will read the outer size of the `arrowElement` node to detect how many\n   * pixels of conjuction are needed.\n   *\n   * It has no effect if no `arrowElement` is provided.\n   * @memberof modifiers\n   * @inner\n   */\n  arrow: {\n    /** @prop {number} order=500 - Index used to define the order of execution */\n    order: 500,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: arrow,\n    /** @prop {String|HTMLElement} element='[x-arrow]' - Selector or node used as arrow */\n    element: '[x-arrow]'\n  },\n\n  /**\n   * Modifier used to flip the popper's placement when it starts to overlap its\n   * reference element.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   *\n   * **NOTE:** this modifier will interrupt the current update cycle and will\n   * restart it if it detects the need to flip the placement.\n   * @memberof modifiers\n   * @inner\n   */\n  flip: {\n    /** @prop {number} order=600 - Index used to define the order of execution */\n    order: 600,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: flip,\n    /**\n     * @prop {String|Array} behavior='flip'\n     * The behavior used to change the popper's placement. It can be one of\n     * `flip`, `clockwise`, `counterclockwise` or an array with a list of valid\n     * placements (with optional variations).\n     */\n    behavior: 'flip',\n    /**\n     * @prop {number} padding=5\n     * The popper will flip if it hits the edges of the `boundariesElement`\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='viewport'\n     * The element which will define the boundaries of the popper position,\n     * the popper will never be placed outside of the defined boundaries\n     * (except if keepTogether is enabled)\n     */\n    boundariesElement: 'viewport'\n  },\n\n  /**\n   * Modifier used to make the popper flow toward the inner of the reference element.\n   * By default, when this modifier is disabled, the popper will be placed outside\n   * the reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  inner: {\n    /** @prop {number} order=700 - Index used to define the order of execution */\n    order: 700,\n    /** @prop {Boolean} enabled=false - Whether the modifier is enabled or not */\n    enabled: false,\n    /** @prop {ModifierFn} */\n    fn: inner\n  },\n\n  /**\n   * Modifier used to hide the popper when its reference element is outside of the\n   * popper boundaries. It will set a `x-out-of-boundaries` attribute which can\n   * be used to hide with a CSS selector the popper when its reference is\n   * out of boundaries.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   * @memberof modifiers\n   * @inner\n   */\n  hide: {\n    /** @prop {number} order=800 - Index used to define the order of execution */\n    order: 800,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: hide\n  },\n\n  /**\n   * Computes the style that will be applied to the popper element to gets\n   * properly positioned.\n   *\n   * Note that this modifier will not touch the DOM, it just prepares the styles\n   * so that `applyStyle` modifier can apply it. This separation is useful\n   * in case you need to replace `applyStyle` with a custom implementation.\n   *\n   * This modifier has `850` as `order` value to maintain backward compatibility\n   * with previous versions of Popper.js. Expect the modifiers ordering method\n   * to change in future major versions of the library.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  computeStyle: {\n    /** @prop {number} order=850 - Index used to define the order of execution */\n    order: 850,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: computeStyle,\n    /**\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3d transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties.\n     */\n    gpuAcceleration: true,\n    /**\n     * @prop {string} [x='bottom']\n     * Where to anchor the X axis (`bottom` or `top`). AKA X offset origin.\n     * Change this if your popper should grow in a direction different from `bottom`\n     */\n    x: 'bottom',\n    /**\n     * @prop {string} [x='left']\n     * Where to anchor the Y axis (`left` or `right`). AKA Y offset origin.\n     * Change this if your popper should grow in a direction different from `right`\n     */\n    y: 'right'\n  },\n\n  /**\n   * Applies the computed styles to the popper element.\n   *\n   * All the DOM manipulations are limited to this modifier. This is useful in case\n   * you want to integrate Popper.js inside a framework or view library and you\n   * want to delegate all the DOM manipulations to it.\n   *\n   * Note that if you disable this modifier, you must make sure the popper element\n   * has its position set to `absolute` before Popper.js can do its work!\n   *\n   * Just disable this modifier and define you own to achieve the desired effect.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  applyStyle: {\n    /** @prop {number} order=900 - Index used to define the order of execution */\n    order: 900,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: applyStyle,\n    /** @prop {Function} */\n    onLoad: applyStyleOnLoad,\n    /**\n     * @deprecated since version 1.10.0, the property moved to `computeStyle` modifier\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3d transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties.\n     */\n    gpuAcceleration: undefined\n  }\n};\n\n/**\n * The `dataObject` is an object containing all the informations used by Popper.js\n * this object get passed to modifiers and to the `onCreate` and `onUpdate` callbacks.\n * @name dataObject\n * @property {Object} data.instance The Popper.js instance\n * @property {String} data.placement Placement applied to popper\n * @property {String} data.originalPlacement Placement originally defined on init\n * @property {Boolean} data.flipped True if popper has been flipped by flip modifier\n * @property {Boolean} data.hide True if the reference element is out of boundaries, useful to know when to hide the popper.\n * @property {HTMLElement} data.arrowElement Node used as arrow by arrow modifier\n * @property {Object} data.styles Any CSS property defined here will be applied to the popper, it expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.arrowStyles Any CSS property defined here will be applied to the popper arrow, it expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.boundaries Offsets of the popper boundaries\n * @property {Object} data.offsets The measurements of popper, reference and arrow elements.\n * @property {Object} data.offsets.popper `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.reference `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.arrow] `top` and `left` offsets, only one of them will be different from 0\n */\n\n/**\n * Default options provided to Popper.js constructor.<br />\n * These can be overriden using the `options` argument of Popper.js.<br />\n * To override an option, simply pass as 3rd argument an object with the same\n * structure of this object, example:\n * ```\n * new Popper(ref, pop, {\n *   modifiers: {\n *     preventOverflow: { enabled: false }\n *   }\n * })\n * ```\n * @type {Object}\n * @static\n * @memberof Popper\n */\nvar Defaults = {\n  /**\n   * Popper's placement\n   * @prop {Popper.placements} placement='bottom'\n   */\n  placement: 'bottom',\n\n  /**\n   * Whether events (resize, scroll) are initially enabled\n   * @prop {Boolean} eventsEnabled=true\n   */\n  eventsEnabled: true,\n\n  /**\n   * Set to true if you want to automatically remove the popper when\n   * you call the `destroy` method.\n   * @prop {Boolean} removeOnDestroy=false\n   */\n  removeOnDestroy: false,\n\n  /**\n   * Callback called when the popper is created.<br />\n   * By default, is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onCreate}\n   */\n  onCreate: function onCreate() {},\n\n  /**\n   * Callback called when the popper is updated, this callback is not called\n   * on the initialization/creation of the popper, but only on subsequent\n   * updates.<br />\n   * By default, is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onUpdate}\n   */\n  onUpdate: function onUpdate() {},\n\n  /**\n   * List of modifiers used to modify the offsets before they are applied to the popper.\n   * They provide most of the functionalities of Popper.js\n   * @prop {modifiers}\n   */\n  modifiers: modifiers\n};\n\n/**\n * @callback onCreate\n * @param {dataObject} data\n */\n\n/**\n * @callback onUpdate\n * @param {dataObject} data\n */\n\n// Utils\n// Methods\nvar Popper = function () {\n  /**\n   * Create a new Popper.js instance\n   * @class Popper\n   * @param {HTMLElement|referenceObject} reference - The reference element used to position the popper\n   * @param {HTMLElement} popper - The HTML element used as popper.\n   * @param {Object} options - Your custom options to override the ones defined in [Defaults](#defaults)\n   * @return {Object} instance - The generated Popper.js instance\n   */\n  function Popper(reference, popper) {\n    var _this = this;\n\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    classCallCheck(this, Popper);\n\n    this.scheduleUpdate = function () {\n      return requestAnimationFrame(_this.update);\n    };\n\n    // make update() debounced, so that it only runs at most once-per-tick\n    this.update = debounce(this.update.bind(this));\n\n    // with {} we create a new object with the options inside it\n    this.options = _extends({}, Popper.Defaults, options);\n\n    // init state\n    this.state = {\n      isDestroyed: false,\n      isCreated: false,\n      scrollParents: []\n    };\n\n    // get reference and popper elements (allow jQuery wrappers)\n    this.reference = reference.jquery ? reference[0] : reference;\n    this.popper = popper.jquery ? popper[0] : popper;\n\n    // Deep merge modifiers options\n    this.options.modifiers = {};\n    Object.keys(_extends({}, Popper.Defaults.modifiers, options.modifiers)).forEach(function (name) {\n      _this.options.modifiers[name] = _extends({}, Popper.Defaults.modifiers[name] || {}, options.modifiers ? options.modifiers[name] : {});\n    });\n\n    // Refactoring modifiers' list (Object => Array)\n    this.modifiers = Object.keys(this.options.modifiers).map(function (name) {\n      return _extends({\n        name: name\n      }, _this.options.modifiers[name]);\n    })\n    // sort the modifiers by order\n    .sort(function (a, b) {\n      return a.order - b.order;\n    });\n\n    // modifiers have the ability to execute arbitrary code when Popper.js get inited\n    // such code is executed in the same order of its modifier\n    // they could add new properties to their options configuration\n    // BE AWARE: don't add options to `options.modifiers.name` but to `modifierOptions`!\n    this.modifiers.forEach(function (modifierOptions) {\n      if (modifierOptions.enabled && isFunction(modifierOptions.onLoad)) {\n        modifierOptions.onLoad(_this.reference, _this.popper, _this.options, modifierOptions, _this.state);\n      }\n    });\n\n    // fire the first update to position the popper in the right place\n    this.update();\n\n    var eventsEnabled = this.options.eventsEnabled;\n    if (eventsEnabled) {\n      // setup event listeners, they will take care of update the position in specific situations\n      this.enableEventListeners();\n    }\n\n    this.state.eventsEnabled = eventsEnabled;\n  }\n\n  // We can't use class properties because they don't get listed in the\n  // class prototype and break stuff like Sinon stubs\n\n\n  createClass(Popper, [{\n    key: 'update',\n    value: function update$$1() {\n      return update.call(this);\n    }\n  }, {\n    key: 'destroy',\n    value: function destroy$$1() {\n      return destroy.call(this);\n    }\n  }, {\n    key: 'enableEventListeners',\n    value: function enableEventListeners$$1() {\n      return enableEventListeners.call(this);\n    }\n  }, {\n    key: 'disableEventListeners',\n    value: function disableEventListeners$$1() {\n      return disableEventListeners.call(this);\n    }\n\n    /**\n     * Schedule an update, it will run on the next UI update available\n     * @method scheduleUpdate\n     * @memberof Popper\n     */\n\n\n    /**\n     * Collection of utilities useful when writing custom modifiers.\n     * Starting from version 1.7, this method is available only if you\n     * include `popper-utils.js` before `popper.js`.\n     *\n     * **DEPRECATION**: This way to access PopperUtils is deprecated\n     * and will be removed in v2! Use the PopperUtils module directly instead.\n     * Due to the high instability of the methods contained in Utils, we can't\n     * guarantee them to follow semver. Use them at your own risk!\n     * @static\n     * @private\n     * @type {Object}\n     * @deprecated since version 1.8\n     * @member Utils\n     * @memberof Popper\n     */\n\n  }]);\n  return Popper;\n}();\n\n/**\n * The `referenceObject` is an object that provides an interface compatible with Popper.js\n * and lets you use it as replacement of a real DOM node.<br />\n * You can use this method to position a popper relatively to a set of coordinates\n * in case you don't have a DOM node to use as reference.\n *\n * ```\n * new Popper(referenceObject, popperNode);\n * ```\n *\n * NB: This feature isn't supported in Internet Explorer 10\n * @name referenceObject\n * @property {Function} data.getBoundingClientRect\n * A function that returns a set of coordinates compatible with the native `getBoundingClientRect` method.\n * @property {number} data.clientWidth\n * An ES6 getter that will return the width of the virtual reference element.\n * @property {number} data.clientHeight\n * An ES6 getter that will return the height of the virtual reference element.\n */\n\n\nPopper.Utils = (typeof window !== 'undefined' ? window : global).PopperUtils;\nPopper.placements = placements;\nPopper.Defaults = Defaults;\n\nexport default Popper;\n//# sourceMappingURL=popper.js.map\n", "import $ from 'jquery'\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON>trap (v4.0.0-beta.2): util.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Util = (() => {\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Private TransitionEnd Helpers\n   * ------------------------------------------------------------------------\n   */\n\n  let transition = false\n\n  const MAX_UID = 1000000\n\n  const TransitionEndEvent = {\n    WebkitTransition : 'webkitTransitionEnd',\n    MozTransition    : 'transitionend',\n    OTransition      : 'oTransitionEnd otransitionend',\n    transition       : 'transitionend'\n  }\n\n  // shoutout AngusCroll (https://goo.gl/pxwQGp)\n  function toType(obj) {\n    return {}.toString.call(obj).match(/\\s([a-zA-Z]+)/)[1].toLowerCase()\n  }\n\n  function getSpecialTransitionEndEvent() {\n    return {\n      bindType: transition.end,\n      delegateType: transition.end,\n      handle(event) {\n        if ($(event.target).is(this)) {\n          return event.handleObj.handler.apply(this, arguments) // eslint-disable-line prefer-rest-params\n        }\n        return undefined // eslint-disable-line no-undefined\n      }\n    }\n  }\n\n  function transitionEndTest() {\n    if (window.QUnit) {\n      return false\n    }\n\n    const el = document.createElement('bootstrap')\n\n    for (const name in TransitionEndEvent) {\n      if (typeof el.style[name] !== 'undefined') {\n        return {\n          end: TransitionEndEvent[name]\n        }\n      }\n    }\n\n    return false\n  }\n\n  function transitionEndEmulator(duration) {\n    let called = false\n\n    $(this).one(Util.TRANSITION_END, () => {\n      called = true\n    })\n\n    setTimeout(() => {\n      if (!called) {\n        Util.triggerTransitionEnd(this)\n      }\n    }, duration)\n\n    return this\n  }\n\n  function setTransitionEndSupport() {\n    transition = transitionEndTest()\n\n    $.fn.emulateTransitionEnd = transitionEndEmulator\n\n    if (Util.supportsTransitionEnd()) {\n      $.event.special[Util.TRANSITION_END] = getSpecialTransitionEndEvent()\n    }\n  }\n\n\n  /**\n   * --------------------------------------------------------------------------\n   * Public Util Api\n   * --------------------------------------------------------------------------\n   */\n\n  const Util = {\n\n    TRANSITION_END: 'bsTransitionEnd',\n\n    getUID(prefix) {\n      do {\n        // eslint-disable-next-line no-bitwise\n        prefix += ~~(Math.random() * MAX_UID) // \"~~\" acts like a faster Math.floor() here\n      } while (document.getElementById(prefix))\n      return prefix\n    },\n\n    getSelectorFromElement(element) {\n      let selector = element.getAttribute('data-target')\n      if (!selector || selector === '#') {\n        selector = element.getAttribute('href') || ''\n      }\n\n      try {\n        const $selector = $(document).find(selector)\n        return $selector.length > 0 ? selector : null\n      } catch (error) {\n        return null\n      }\n    },\n\n    reflow(element) {\n      return element.offsetHeight\n    },\n\n    triggerTransitionEnd(element) {\n      $(element).trigger(transition.end)\n    },\n\n    supportsTransitionEnd() {\n      return Boolean(transition)\n    },\n\n    isElement(obj) {\n      return (obj[0] || obj).nodeType\n    },\n\n    typeCheckConfig(componentName, config, configTypes) {\n      for (const property in configTypes) {\n        if (Object.prototype.hasOwnProperty.call(configTypes, property)) {\n          const expectedTypes = configTypes[property]\n          const value         = config[property]\n          const valueType     = value && Util.isElement(value) ?\n                                'element' : toType(value)\n\n          if (!new RegExp(expectedTypes).test(valueType)) {\n            throw new Error(\n              `${componentName.toUpperCase()}: ` +\n              `Option \"${property}\" provided type \"${valueType}\" ` +\n              `but expected type \"${expectedTypes}\".`)\n          }\n        }\n      }\n    }\n  }\n\n  setTransitionEndSupport()\n\n  return Util\n\n})($)\n\nexport default Util\n", "import $ from 'jquery'\nimport Util from './util'\n\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON><PERSON><PERSON> (v4.0.0-beta.2): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Alert = (() => {\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                = 'alert'\n  const VERSION             = '4.0.0-beta.2'\n  const DATA_KEY            = 'bs.alert'\n  const EVENT_KEY           = `.${DATA_KEY}`\n  const DATA_API_KEY        = '.data-api'\n  const JQUERY_NO_CONFLICT  = $.fn[NAME]\n  const TRANSITION_DURATION = 150\n\n  const Selector = {\n    DISMISS : '[data-dismiss=\"alert\"]'\n  }\n\n  const Event = {\n    CLOSE          : `close${EVENT_KEY}`,\n    CLOSED         : `closed${EVENT_KEY}`,\n    CLICK_DATA_API : `click${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  const ClassName = {\n    ALERT : 'alert',\n    FADE  : 'fade',\n    SHOW  : 'show'\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Alert {\n\n    constructor(element) {\n      this._element = element\n    }\n\n\n    // getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n\n    // public\n\n    close(element) {\n      element = element || this._element\n\n      const rootElement = this._getRootElement(element)\n      const customEvent = this._triggerCloseEvent(rootElement)\n\n      if (customEvent.isDefaultPrevented()) {\n        return\n      }\n\n      this._removeElement(rootElement)\n    }\n\n    dispose() {\n      $.removeData(this._element, DATA_KEY)\n      this._element = null\n    }\n\n\n    // private\n\n    _getRootElement(element) {\n      const selector = Util.getSelectorFromElement(element)\n      let parent     = false\n\n      if (selector) {\n        parent = $(selector)[0]\n      }\n\n      if (!parent) {\n        parent = $(element).closest(`.${ClassName.ALERT}`)[0]\n      }\n\n      return parent\n    }\n\n    _triggerCloseEvent(element) {\n      const closeEvent = $.Event(Event.CLOSE)\n\n      $(element).trigger(closeEvent)\n      return closeEvent\n    }\n\n    _removeElement(element) {\n      $(element).removeClass(ClassName.SHOW)\n\n      if (!Util.supportsTransitionEnd() ||\n          !$(element).hasClass(ClassName.FADE)) {\n        this._destroyElement(element)\n        return\n      }\n\n      $(element)\n        .one(Util.TRANSITION_END, (event) => this._destroyElement(element, event))\n        .emulateTransitionEnd(TRANSITION_DURATION)\n    }\n\n    _destroyElement(element) {\n      $(element)\n        .detach()\n        .trigger(Event.CLOSED)\n        .remove()\n    }\n\n\n    // static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        const $element = $(this)\n        let data       = $element.data(DATA_KEY)\n\n        if (!data) {\n          data = new Alert(this)\n          $element.data(DATA_KEY, data)\n        }\n\n        if (config === 'close') {\n          data[config](this)\n        }\n      })\n    }\n\n    static _handleDismiss(alertInstance) {\n      return function (event) {\n        if (event) {\n          event.preventDefault()\n        }\n\n        alertInstance.close(this)\n      }\n    }\n\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(document).on(\n    Event.CLICK_DATA_API,\n    Selector.DISMISS,\n    Alert._handleDismiss(new Alert())\n  )\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME]             = Alert._jQueryInterface\n  $.fn[NAME].Constructor = Alert\n  $.fn[NAME].noConflict  = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Alert._jQueryInterface\n  }\n\n  return Alert\n\n})($)\n\nexport default Alert\n", "import $ from 'jquery'\n/**\n * --------------------------------------------------------------------------\n * <PERSON>trap (v4.0.0-beta.2): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Button = (() => {\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                = 'button'\n  const VERSION             = '4.0.0-beta.2'\n  const DATA_KEY            = 'bs.button'\n  const EVENT_KEY           = `.${DATA_KEY}`\n  const DATA_API_KEY        = '.data-api'\n  const JQUERY_NO_CONFLICT  = $.fn[NAME]\n\n  const ClassName = {\n    ACTIVE : 'active',\n    BUTTON : 'btn',\n    FOCUS  : 'focus'\n  }\n\n  const Selector = {\n    DATA_TOGGLE_CARROT : '[data-toggle^=\"button\"]',\n    DATA_TOGGLE        : '[data-toggle=\"buttons\"]',\n    INPUT              : 'input',\n    ACTIVE             : '.active',\n    BUTTON             : '.btn'\n  }\n\n  const Event = {\n    CLICK_DATA_API      : `click${EVENT_KEY}${DATA_API_KEY}`,\n    FOCUS_BLUR_DATA_API : `focus${EVENT_KEY}${DATA_API_KEY} `\n                        + `blur${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Button {\n\n    constructor(element) {\n      this._element = element\n    }\n\n\n    // getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n\n    // public\n\n    toggle() {\n      let triggerChangeEvent = true\n      let addAriaPressed = true\n      const rootElement      = $(this._element).closest(\n        Selector.DATA_TOGGLE\n      )[0]\n\n      if (rootElement) {\n        const input = $(this._element).find(Selector.INPUT)[0]\n\n        if (input) {\n          if (input.type === 'radio') {\n            if (input.checked &&\n              $(this._element).hasClass(ClassName.ACTIVE)) {\n              triggerChangeEvent = false\n\n            } else {\n              const activeElement = $(rootElement).find(Selector.ACTIVE)[0]\n\n              if (activeElement) {\n                $(activeElement).removeClass(ClassName.ACTIVE)\n              }\n            }\n          }\n\n          if (triggerChangeEvent) {\n            if (input.hasAttribute('disabled') ||\n              rootElement.hasAttribute('disabled') ||\n              input.classList.contains('disabled') ||\n              rootElement.classList.contains('disabled')) {\n              return\n            }\n            input.checked = !$(this._element).hasClass(ClassName.ACTIVE)\n            $(input).trigger('change')\n          }\n\n          input.focus()\n          addAriaPressed = false\n        }\n\n      }\n\n      if (addAriaPressed) {\n        this._element.setAttribute('aria-pressed',\n          !$(this._element).hasClass(ClassName.ACTIVE))\n      }\n\n      if (triggerChangeEvent) {\n        $(this._element).toggleClass(ClassName.ACTIVE)\n      }\n    }\n\n    dispose() {\n      $.removeData(this._element, DATA_KEY)\n      this._element = null\n    }\n\n\n    // static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        let data = $(this).data(DATA_KEY)\n\n        if (!data) {\n          data = new Button(this)\n          $(this).data(DATA_KEY, data)\n        }\n\n        if (config === 'toggle') {\n          data[config]()\n        }\n      })\n    }\n\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(document)\n    .on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE_CARROT, (event) => {\n      event.preventDefault()\n\n      let button = event.target\n\n      if (!$(button).hasClass(ClassName.BUTTON)) {\n        button = $(button).closest(Selector.BUTTON)\n      }\n\n      Button._jQueryInterface.call($(button), 'toggle')\n    })\n    .on(Event.FOCUS_BLUR_DATA_API, Selector.DATA_TOGGLE_CARROT, (event) => {\n      const button = $(event.target).closest(Selector.BUTTON)[0]\n      $(button).toggleClass(ClassName.FOCUS, /^focus(in)?$/.test(event.type))\n    })\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME]             = Button._jQueryInterface\n  $.fn[NAME].Constructor = Button\n  $.fn[NAME].noConflict  = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Button._jQueryInterface\n  }\n\n  return Button\n\n})($)\n\nexport default Button\n", "import $ from 'jquery'\nimport Util from './util'\n\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.0.0-beta.2): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Carousel = (() => {\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                   = 'carousel'\n  const VERSION                = '4.0.0-beta.2'\n  const DATA_KEY               = 'bs.carousel'\n  const EVENT_KEY              = `.${DATA_KEY}`\n  const DATA_API_KEY           = '.data-api'\n  const JQUERY_NO_CONFLICT     = $.fn[NAME]\n  const TRANSITION_DURATION    = 600\n  const ARROW_LEFT_KEYCODE     = 37 // KeyboardEvent.which value for left arrow key\n  const ARROW_RIGHT_KEYCODE    = 39 // KeyboardEvent.which value for right arrow key\n  const TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\n\n  const Default = {\n    interval : 5000,\n    keyboard : true,\n    slide    : false,\n    pause    : 'hover',\n    wrap     : true\n  }\n\n  const DefaultType = {\n    interval : '(number|boolean)',\n    keyboard : 'boolean',\n    slide    : '(boolean|string)',\n    pause    : '(string|boolean)',\n    wrap     : 'boolean'\n  }\n\n  const Direction = {\n    NEXT     : 'next',\n    PREV     : 'prev',\n    LEFT     : 'left',\n    RIGHT    : 'right'\n  }\n\n  const Event = {\n    SLIDE          : `slide${EVENT_KEY}`,\n    SLID           : `slid${EVENT_KEY}`,\n    KEYDOWN        : `keydown${EVENT_KEY}`,\n    MOUSEENTER     : `mouseenter${EVENT_KEY}`,\n    MOUSELEAVE     : `mouseleave${EVENT_KEY}`,\n    TOUCHEND       : `touchend${EVENT_KEY}`,\n    LOAD_DATA_API  : `load${EVENT_KEY}${DATA_API_KEY}`,\n    CLICK_DATA_API : `click${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  const ClassName = {\n    CAROUSEL : 'carousel',\n    ACTIVE   : 'active',\n    SLIDE    : 'slide',\n    RIGHT    : 'carousel-item-right',\n    LEFT     : 'carousel-item-left',\n    NEXT     : 'carousel-item-next',\n    PREV     : 'carousel-item-prev',\n    ITEM     : 'carousel-item'\n  }\n\n  const Selector = {\n    ACTIVE      : '.active',\n    ACTIVE_ITEM : '.active.carousel-item',\n    ITEM        : '.carousel-item',\n    NEXT_PREV   : '.carousel-item-next, .carousel-item-prev',\n    INDICATORS  : '.carousel-indicators',\n    DATA_SLIDE  : '[data-slide], [data-slide-to]',\n    DATA_RIDE   : '[data-ride=\"carousel\"]'\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Carousel {\n\n    constructor(element, config) {\n      this._items             = null\n      this._interval          = null\n      this._activeElement     = null\n\n      this._isPaused          = false\n      this._isSliding         = false\n\n      this.touchTimeout       = null\n\n      this._config            = this._getConfig(config)\n      this._element           = $(element)[0]\n      this._indicatorsElement = $(this._element).find(Selector.INDICATORS)[0]\n\n      this._addEventListeners()\n    }\n\n\n    // getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    static get Default() {\n      return Default\n    }\n\n\n    // public\n\n    next() {\n      if (!this._isSliding) {\n        this._slide(Direction.NEXT)\n      }\n    }\n\n    nextWhenVisible() {\n      // Don't call next when the page isn't visible\n      // or the carousel or its parent isn't visible\n      if (!document.hidden &&\n        ($(this._element).is(':visible') && $(this._element).css('visibility') !== 'hidden')) {\n        this.next()\n      }\n    }\n\n    prev() {\n      if (!this._isSliding) {\n        this._slide(Direction.PREV)\n      }\n    }\n\n    pause(event) {\n      if (!event) {\n        this._isPaused = true\n      }\n\n      if ($(this._element).find(Selector.NEXT_PREV)[0] &&\n        Util.supportsTransitionEnd()) {\n        Util.triggerTransitionEnd(this._element)\n        this.cycle(true)\n      }\n\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    cycle(event) {\n      if (!event) {\n        this._isPaused = false\n      }\n\n      if (this._interval) {\n        clearInterval(this._interval)\n        this._interval = null\n      }\n\n      if (this._config.interval && !this._isPaused) {\n        this._interval = setInterval(\n          (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n          this._config.interval\n        )\n      }\n    }\n\n    to(index) {\n      this._activeElement = $(this._element).find(Selector.ACTIVE_ITEM)[0]\n\n      const activeIndex = this._getItemIndex(this._activeElement)\n\n      if (index > this._items.length - 1 || index < 0) {\n        return\n      }\n\n      if (this._isSliding) {\n        $(this._element).one(Event.SLID, () => this.to(index))\n        return\n      }\n\n      if (activeIndex === index) {\n        this.pause()\n        this.cycle()\n        return\n      }\n\n      const direction = index > activeIndex ?\n        Direction.NEXT :\n        Direction.PREV\n\n      this._slide(direction, this._items[index])\n    }\n\n    dispose() {\n      $(this._element).off(EVENT_KEY)\n      $.removeData(this._element, DATA_KEY)\n\n      this._items             = null\n      this._config            = null\n      this._element           = null\n      this._interval          = null\n      this._isPaused          = null\n      this._isSliding         = null\n      this._activeElement     = null\n      this._indicatorsElement = null\n    }\n\n\n    // private\n\n    _getConfig(config) {\n      config = $.extend({}, Default, config)\n      Util.typeCheckConfig(NAME, config, DefaultType)\n      return config\n    }\n\n    _addEventListeners() {\n      if (this._config.keyboard) {\n        $(this._element)\n          .on(Event.KEYDOWN, (event) => this._keydown(event))\n      }\n\n      if (this._config.pause === 'hover') {\n        $(this._element)\n          .on(Event.MOUSEENTER, (event) => this.pause(event))\n          .on(Event.MOUSELEAVE, (event) => this.cycle(event))\n        if ('ontouchstart' in document.documentElement) {\n          // if it's a touch-enabled device, mouseenter/leave are fired as\n          // part of the mouse compatibility events on first tap - the carousel\n          // would stop cycling until user tapped out of it;\n          // here, we listen for touchend, explicitly pause the carousel\n          // (as if it's the second time we tap on it, mouseenter compat event\n          // is NOT fired) and after a timeout (to allow for mouse compatibility\n          // events to fire) we explicitly restart cycling\n          $(this._element).on(Event.TOUCHEND, () => {\n            this.pause()\n            if (this.touchTimeout) {\n              clearTimeout(this.touchTimeout)\n            }\n            this.touchTimeout = setTimeout((event) => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n          })\n        }\n      }\n    }\n\n    _keydown(event) {\n      if (/input|textarea/i.test(event.target.tagName)) {\n        return\n      }\n\n      switch (event.which) {\n        case ARROW_LEFT_KEYCODE:\n          event.preventDefault()\n          this.prev()\n          break\n        case ARROW_RIGHT_KEYCODE:\n          event.preventDefault()\n          this.next()\n          break\n        default:\n          return\n      }\n    }\n\n    _getItemIndex(element) {\n      this._items = $.makeArray($(element).parent().find(Selector.ITEM))\n      return this._items.indexOf(element)\n    }\n\n    _getItemByDirection(direction, activeElement) {\n      const isNextDirection = direction === Direction.NEXT\n      const isPrevDirection = direction === Direction.PREV\n      const activeIndex     = this._getItemIndex(activeElement)\n      const lastItemIndex   = this._items.length - 1\n      const isGoingToWrap   = isPrevDirection && activeIndex === 0 ||\n                              isNextDirection && activeIndex === lastItemIndex\n\n      if (isGoingToWrap && !this._config.wrap) {\n        return activeElement\n      }\n\n      const delta     = direction === Direction.PREV ? -1 : 1\n      const itemIndex = (activeIndex + delta) % this._items.length\n\n      return itemIndex === -1 ?\n        this._items[this._items.length - 1] : this._items[itemIndex]\n    }\n\n\n    _triggerSlideEvent(relatedTarget, eventDirectionName) {\n      const targetIndex = this._getItemIndex(relatedTarget)\n      const fromIndex = this._getItemIndex($(this._element).find(Selector.ACTIVE_ITEM)[0])\n      const slideEvent = $.Event(Event.SLIDE, {\n        relatedTarget,\n        direction: eventDirectionName,\n        from: fromIndex,\n        to: targetIndex\n      })\n\n      $(this._element).trigger(slideEvent)\n\n      return slideEvent\n    }\n\n    _setActiveIndicatorElement(element) {\n      if (this._indicatorsElement) {\n        $(this._indicatorsElement)\n          .find(Selector.ACTIVE)\n          .removeClass(ClassName.ACTIVE)\n\n        const nextIndicator = this._indicatorsElement.children[\n          this._getItemIndex(element)\n        ]\n\n        if (nextIndicator) {\n          $(nextIndicator).addClass(ClassName.ACTIVE)\n        }\n      }\n    }\n\n    _slide(direction, element) {\n      const activeElement = $(this._element).find(Selector.ACTIVE_ITEM)[0]\n      const activeElementIndex = this._getItemIndex(activeElement)\n      const nextElement   = element || activeElement &&\n        this._getItemByDirection(direction, activeElement)\n      const nextElementIndex = this._getItemIndex(nextElement)\n      const isCycling = Boolean(this._interval)\n\n      let directionalClassName\n      let orderClassName\n      let eventDirectionName\n\n      if (direction === Direction.NEXT) {\n        directionalClassName = ClassName.LEFT\n        orderClassName = ClassName.NEXT\n        eventDirectionName = Direction.LEFT\n      } else {\n        directionalClassName = ClassName.RIGHT\n        orderClassName = ClassName.PREV\n        eventDirectionName = Direction.RIGHT\n      }\n\n      if (nextElement && $(nextElement).hasClass(ClassName.ACTIVE)) {\n        this._isSliding = false\n        return\n      }\n\n      const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n      if (slideEvent.isDefaultPrevented()) {\n        return\n      }\n\n      if (!activeElement || !nextElement) {\n        // some weirdness is happening, so we bail\n        return\n      }\n\n      this._isSliding = true\n\n      if (isCycling) {\n        this.pause()\n      }\n\n      this._setActiveIndicatorElement(nextElement)\n\n      const slidEvent = $.Event(Event.SLID, {\n        relatedTarget: nextElement,\n        direction: eventDirectionName,\n        from: activeElementIndex,\n        to: nextElementIndex\n      })\n\n      if (Util.supportsTransitionEnd() &&\n        $(this._element).hasClass(ClassName.SLIDE)) {\n\n        $(nextElement).addClass(orderClassName)\n\n        Util.reflow(nextElement)\n\n        $(activeElement).addClass(directionalClassName)\n        $(nextElement).addClass(directionalClassName)\n\n        $(activeElement)\n          .one(Util.TRANSITION_END, () => {\n            $(nextElement)\n              .removeClass(`${directionalClassName} ${orderClassName}`)\n              .addClass(ClassName.ACTIVE)\n\n            $(activeElement).removeClass(`${ClassName.ACTIVE} ${orderClassName} ${directionalClassName}`)\n\n            this._isSliding = false\n\n            setTimeout(() => $(this._element).trigger(slidEvent), 0)\n\n          })\n          .emulateTransitionEnd(TRANSITION_DURATION)\n\n      } else {\n        $(activeElement).removeClass(ClassName.ACTIVE)\n        $(nextElement).addClass(ClassName.ACTIVE)\n\n        this._isSliding = false\n        $(this._element).trigger(slidEvent)\n      }\n\n      if (isCycling) {\n        this.cycle()\n      }\n    }\n\n\n    // static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        let data      = $(this).data(DATA_KEY)\n        const _config = $.extend({}, Default, $(this).data())\n\n        if (typeof config === 'object') {\n          $.extend(_config, config)\n        }\n\n        const action = typeof config === 'string' ? config : _config.slide\n\n        if (!data) {\n          data = new Carousel(this, _config)\n          $(this).data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'number') {\n          data.to(config)\n        } else if (typeof action === 'string') {\n          if (typeof data[action] === 'undefined') {\n            throw new Error(`No method named \"${action}\"`)\n          }\n          data[action]()\n        } else if (_config.interval) {\n          data.pause()\n          data.cycle()\n        }\n      })\n    }\n\n    static _dataApiClickHandler(event) {\n      const selector = Util.getSelectorFromElement(this)\n\n      if (!selector) {\n        return\n      }\n\n      const target = $(selector)[0]\n\n      if (!target || !$(target).hasClass(ClassName.CAROUSEL)) {\n        return\n      }\n\n      const config     = $.extend({}, $(target).data(), $(this).data())\n      const slideIndex = this.getAttribute('data-slide-to')\n\n      if (slideIndex) {\n        config.interval = false\n      }\n\n      Carousel._jQueryInterface.call($(target), config)\n\n      if (slideIndex) {\n        $(target).data(DATA_KEY).to(slideIndex)\n      }\n\n      event.preventDefault()\n    }\n\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(document)\n    .on(Event.CLICK_DATA_API, Selector.DATA_SLIDE, Carousel._dataApiClickHandler)\n\n  $(window).on(Event.LOAD_DATA_API, () => {\n    $(Selector.DATA_RIDE).each(function () {\n      const $carousel = $(this)\n      Carousel._jQueryInterface.call($carousel, $carousel.data())\n    })\n  })\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME]             = Carousel._jQueryInterface\n  $.fn[NAME].Constructor = Carousel\n  $.fn[NAME].noConflict  = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Carousel._jQueryInterface\n  }\n\n  return Carousel\n\n})($)\n\nexport default Carousel\n", "import $ from 'jquery'\nimport Util from './util'\n\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON><PERSON><PERSON> (v4.0.0-beta.2): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Collapse = (() => {\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                = 'collapse'\n  const VERSION             = '4.0.0-beta.2'\n  const DATA_KEY            = 'bs.collapse'\n  const EVENT_KEY           = `.${DATA_KEY}`\n  const DATA_API_KEY        = '.data-api'\n  const JQUERY_NO_CONFLICT  = $.fn[NAME]\n  const TRANSITION_DURATION = 600\n\n  const Default = {\n    toggle : true,\n    parent : ''\n  }\n\n  const DefaultType = {\n    toggle : 'boolean',\n    parent : '(string|element)'\n  }\n\n  const Event = {\n    SHOW           : `show${EVENT_KEY}`,\n    SHOWN          : `shown${EVENT_KEY}`,\n    HIDE           : `hide${EVENT_KEY}`,\n    HIDDEN         : `hidden${EVENT_KEY}`,\n    CLICK_DATA_API : `click${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  const ClassName = {\n    SHOW       : 'show',\n    COLLAPSE   : 'collapse',\n    COLLAPSING : 'collapsing',\n    COLLAPSED  : 'collapsed'\n  }\n\n  const Dimension = {\n    WIDTH  : 'width',\n    HEIGHT : 'height'\n  }\n\n  const Selector = {\n    ACTIVES     : '.show, .collapsing',\n    DATA_TOGGLE : '[data-toggle=\"collapse\"]'\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Collapse {\n\n    constructor(element, config) {\n      this._isTransitioning = false\n      this._element         = element\n      this._config          = this._getConfig(config)\n      this._triggerArray    = $.makeArray($(\n        `[data-toggle=\"collapse\"][href=\"#${element.id}\"],` +\n        `[data-toggle=\"collapse\"][data-target=\"#${element.id}\"]`\n      ))\n      const tabToggles = $(Selector.DATA_TOGGLE)\n      for (let i = 0; i < tabToggles.length; i++) {\n        const elem = tabToggles[i]\n        const selector = Util.getSelectorFromElement(elem)\n        if (selector !== null && $(selector).filter(element).length > 0) {\n          this._triggerArray.push(elem)\n        }\n      }\n\n      this._parent = this._config.parent ? this._getParent() : null\n\n      if (!this._config.parent) {\n        this._addAriaAndCollapsedClass(this._element, this._triggerArray)\n      }\n\n      if (this._config.toggle) {\n        this.toggle()\n      }\n    }\n\n\n    // getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    static get Default() {\n      return Default\n    }\n\n\n    // public\n\n    toggle() {\n      if ($(this._element).hasClass(ClassName.SHOW)) {\n        this.hide()\n      } else {\n        this.show()\n      }\n    }\n\n    show() {\n      if (this._isTransitioning ||\n        $(this._element).hasClass(ClassName.SHOW)) {\n        return\n      }\n\n      let actives\n      let activesData\n\n      if (this._parent) {\n        actives = $.makeArray($(this._parent).children().children(Selector.ACTIVES))\n        if (!actives.length) {\n          actives = null\n        }\n      }\n\n      if (actives) {\n        activesData = $(actives).data(DATA_KEY)\n        if (activesData && activesData._isTransitioning) {\n          return\n        }\n      }\n\n      const startEvent = $.Event(Event.SHOW)\n      $(this._element).trigger(startEvent)\n      if (startEvent.isDefaultPrevented()) {\n        return\n      }\n\n      if (actives) {\n        Collapse._jQueryInterface.call($(actives), 'hide')\n        if (!activesData) {\n          $(actives).data(DATA_KEY, null)\n        }\n      }\n\n      const dimension = this._getDimension()\n\n      $(this._element)\n        .removeClass(ClassName.COLLAPSE)\n        .addClass(ClassName.COLLAPSING)\n\n      this._element.style[dimension] = 0\n\n      if (this._triggerArray.length) {\n        $(this._triggerArray)\n          .removeClass(ClassName.COLLAPSED)\n          .attr('aria-expanded', true)\n      }\n\n      this.setTransitioning(true)\n\n      const complete = () => {\n        $(this._element)\n          .removeClass(ClassName.COLLAPSING)\n          .addClass(ClassName.COLLAPSE)\n          .addClass(ClassName.SHOW)\n\n        this._element.style[dimension] = ''\n\n        this.setTransitioning(false)\n\n        $(this._element).trigger(Event.SHOWN)\n      }\n\n      if (!Util.supportsTransitionEnd()) {\n        complete()\n        return\n      }\n\n      const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n      const scrollSize           = `scroll${capitalizedDimension}`\n\n      $(this._element)\n        .one(Util.TRANSITION_END, complete)\n        .emulateTransitionEnd(TRANSITION_DURATION)\n\n      this._element.style[dimension] = `${this._element[scrollSize]}px`\n    }\n\n    hide() {\n      if (this._isTransitioning ||\n        !$(this._element).hasClass(ClassName.SHOW)) {\n        return\n      }\n\n      const startEvent = $.Event(Event.HIDE)\n      $(this._element).trigger(startEvent)\n      if (startEvent.isDefaultPrevented()) {\n        return\n      }\n\n      const dimension       = this._getDimension()\n\n      this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n      Util.reflow(this._element)\n\n      $(this._element)\n        .addClass(ClassName.COLLAPSING)\n        .removeClass(ClassName.COLLAPSE)\n        .removeClass(ClassName.SHOW)\n\n      if (this._triggerArray.length) {\n        for (let i = 0; i < this._triggerArray.length; i++) {\n          const trigger = this._triggerArray[i]\n          const selector = Util.getSelectorFromElement(trigger)\n          if (selector !== null) {\n            const $elem = $(selector)\n            if (!$elem.hasClass(ClassName.SHOW)) {\n              $(trigger).addClass(ClassName.COLLAPSED)\n                   .attr('aria-expanded', false)\n            }\n          }\n        }\n      }\n\n      this.setTransitioning(true)\n\n      const complete = () => {\n        this.setTransitioning(false)\n        $(this._element)\n          .removeClass(ClassName.COLLAPSING)\n          .addClass(ClassName.COLLAPSE)\n          .trigger(Event.HIDDEN)\n      }\n\n      this._element.style[dimension] = ''\n\n      if (!Util.supportsTransitionEnd()) {\n        complete()\n        return\n      }\n\n      $(this._element)\n        .one(Util.TRANSITION_END, complete)\n        .emulateTransitionEnd(TRANSITION_DURATION)\n    }\n\n    setTransitioning(isTransitioning) {\n      this._isTransitioning = isTransitioning\n    }\n\n    dispose() {\n      $.removeData(this._element, DATA_KEY)\n\n      this._config          = null\n      this._parent          = null\n      this._element         = null\n      this._triggerArray    = null\n      this._isTransitioning = null\n    }\n\n\n    // private\n\n    _getConfig(config) {\n      config = $.extend({}, Default, config)\n      config.toggle = Boolean(config.toggle) // coerce string values\n      Util.typeCheckConfig(NAME, config, DefaultType)\n      return config\n    }\n\n    _getDimension() {\n      const hasWidth = $(this._element).hasClass(Dimension.WIDTH)\n      return hasWidth ? Dimension.WIDTH : Dimension.HEIGHT\n    }\n\n    _getParent() {\n      let parent = null\n      if (Util.isElement(this._config.parent)) {\n        parent = this._config.parent\n\n        // it's a jQuery object\n        if (typeof this._config.parent.jquery !== 'undefined') {\n          parent = this._config.parent[0]\n        }\n      } else {\n        parent = $(this._config.parent)[0]\n      }\n\n      const selector =\n        `[data-toggle=\"collapse\"][data-parent=\"${this._config.parent}\"]`\n\n      $(parent).find(selector).each((i, element) => {\n        this._addAriaAndCollapsedClass(\n          Collapse._getTargetFromElement(element),\n          [element]\n        )\n      })\n\n      return parent\n    }\n\n    _addAriaAndCollapsedClass(element, triggerArray) {\n      if (element) {\n        const isOpen = $(element).hasClass(ClassName.SHOW)\n\n        if (triggerArray.length) {\n          $(triggerArray)\n            .toggleClass(ClassName.COLLAPSED, !isOpen)\n            .attr('aria-expanded', isOpen)\n        }\n      }\n    }\n\n\n    // static\n\n    static _getTargetFromElement(element) {\n      const selector = Util.getSelectorFromElement(element)\n      return selector ? $(selector)[0] : null\n    }\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        const $this   = $(this)\n        let data      = $this.data(DATA_KEY)\n        const _config = $.extend(\n          {},\n          Default,\n          $this.data(),\n          typeof config === 'object' && config\n        )\n\n        if (!data && _config.toggle && /show|hide/.test(config)) {\n          _config.toggle = false\n        }\n\n        if (!data) {\n          data = new Collapse(this, _config)\n          $this.data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'string') {\n          if (typeof data[config] === 'undefined') {\n            throw new Error(`No method named \"${config}\"`)\n          }\n          data[config]()\n        }\n      })\n    }\n\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(document).on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE, function (event) {\n    // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n    if (event.currentTarget.tagName === 'A') {\n      event.preventDefault()\n    }\n\n    const $trigger = $(this)\n    const selector = Util.getSelectorFromElement(this)\n    $(selector).each(function () {\n      const $target = $(this)\n      const data    = $target.data(DATA_KEY)\n      const config  = data ? 'toggle' : $trigger.data()\n      Collapse._jQueryInterface.call($target, config)\n    })\n  })\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME]             = Collapse._jQueryInterface\n  $.fn[NAME].Constructor = Collapse\n  $.fn[NAME].noConflict  = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Collapse._jQueryInterface\n  }\n\n  return Collapse\n\n})($)\n\nexport default Collapse\n", "import $ from 'jquery'\nimport Popper from 'popper.js'\nimport Util from './util'\n\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON><PERSON>p (v4.0.0-beta.2): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Dropdown = (() => {\n\n  /**\n   * Check for Popper dependency\n   * Popper - https://popper.js.org\n   */\n  if (typeof Popper === 'undefined') {\n    throw new Error('Bootstrap dropdown require Popper.js (https://popper.js.org)')\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                     = 'dropdown'\n  const VERSION                  = '4.0.0-beta.2'\n  const DATA_KEY                 = 'bs.dropdown'\n  const EVENT_KEY                = `.${DATA_KEY}`\n  const DATA_API_KEY             = '.data-api'\n  const JQUERY_NO_CONFLICT       = $.fn[NAME]\n  const ESCAPE_KEYCODE           = 27 // KeyboardEvent.which value for Escape (Esc) key\n  const SPACE_KEYCODE            = 32 // KeyboardEvent.which value for space key\n  const TAB_KEYCODE              = 9 // KeyboardEvent.which value for tab key\n  const ARROW_UP_KEYCODE         = 38 // KeyboardEvent.which value for up arrow key\n  const ARROW_DOWN_KEYCODE       = 40 // KeyboardEvent.which value for down arrow key\n  const RIGHT_MOUSE_BUTTON_WHICH = 3 // MouseEvent.which value for the right button (assuming a right-handed mouse)\n  const REGEXP_KEYDOWN           = new RegExp(`${ARROW_UP_KEYCODE}|${ARROW_DOWN_KEYCODE}|${ESCAPE_KEYCODE}`)\n\n  const Event = {\n    HIDE             : `hide${EVENT_KEY}`,\n    HIDDEN           : `hidden${EVENT_KEY}`,\n    SHOW             : `show${EVENT_KEY}`,\n    SHOWN            : `shown${EVENT_KEY}`,\n    CLICK            : `click${EVENT_KEY}`,\n    CLICK_DATA_API   : `click${EVENT_KEY}${DATA_API_KEY}`,\n    KEYDOWN_DATA_API : `keydown${EVENT_KEY}${DATA_API_KEY}`,\n    KEYUP_DATA_API   : `keyup${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  const ClassName = {\n    DISABLED  : 'disabled',\n    SHOW      : 'show',\n    DROPUP    : 'dropup',\n    MENURIGHT : 'dropdown-menu-right',\n    MENULEFT  : 'dropdown-menu-left'\n  }\n\n  const Selector = {\n    DATA_TOGGLE   : '[data-toggle=\"dropdown\"]',\n    FORM_CHILD    : '.dropdown form',\n    MENU          : '.dropdown-menu',\n    NAVBAR_NAV    : '.navbar-nav',\n    VISIBLE_ITEMS : '.dropdown-menu .dropdown-item:not(.disabled)'\n  }\n\n  const AttachmentMap = {\n    TOP       : 'top-start',\n    TOPEND    : 'top-end',\n    BOTTOM    : 'bottom-start',\n    BOTTOMEND : 'bottom-end'\n  }\n\n  const Default = {\n    offset      : 0,\n    flip        : true\n  }\n\n  const DefaultType = {\n    offset      : '(number|string|function)',\n    flip        : 'boolean'\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Dropdown {\n\n    constructor(element, config) {\n      this._element  = element\n      this._popper   = null\n      this._config   = this._getConfig(config)\n      this._menu     = this._getMenuElement()\n      this._inNavbar = this._detectNavbar()\n\n      this._addEventListeners()\n    }\n\n\n    // getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    static get Default() {\n      return Default\n    }\n\n    static get DefaultType() {\n      return DefaultType\n    }\n\n    // public\n\n    toggle() {\n      if (this._element.disabled || $(this._element).hasClass(ClassName.DISABLED)) {\n        return\n      }\n\n      const parent   = Dropdown._getParentFromElement(this._element)\n      const isActive = $(this._menu).hasClass(ClassName.SHOW)\n\n      Dropdown._clearMenus()\n\n      if (isActive) {\n        return\n      }\n\n      const relatedTarget = {\n        relatedTarget : this._element\n      }\n      const showEvent = $.Event(Event.SHOW, relatedTarget)\n\n      $(parent).trigger(showEvent)\n\n      if (showEvent.isDefaultPrevented()) {\n        return\n      }\n\n      let element = this._element\n      // for dropup with alignment we use the parent as popper container\n      if ($(parent).hasClass(ClassName.DROPUP)) {\n        if ($(this._menu).hasClass(ClassName.MENULEFT) || $(this._menu).hasClass(ClassName.MENURIGHT)) {\n          element = parent\n        }\n      }\n      this._popper = new Popper(element, this._menu, this._getPopperConfig())\n\n      // if this is a touch-enabled device we add extra\n      // empty mouseover listeners to the body's immediate children;\n      // only needed because of broken event delegation on iOS\n      // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n      if ('ontouchstart' in document.documentElement &&\n         !$(parent).closest(Selector.NAVBAR_NAV).length) {\n        $('body').children().on('mouseover', null, $.noop)\n      }\n\n      this._element.focus()\n      this._element.setAttribute('aria-expanded', true)\n\n      $(this._menu).toggleClass(ClassName.SHOW)\n      $(parent)\n        .toggleClass(ClassName.SHOW)\n        .trigger($.Event(Event.SHOWN, relatedTarget))\n    }\n\n    dispose() {\n      $.removeData(this._element, DATA_KEY)\n      $(this._element).off(EVENT_KEY)\n      this._element = null\n      this._menu = null\n      if (this._popper !== null) {\n        this._popper.destroy()\n      }\n      this._popper = null\n    }\n\n    update() {\n      this._inNavbar = this._detectNavbar()\n      if (this._popper !== null) {\n        this._popper.scheduleUpdate()\n      }\n    }\n\n    // private\n\n    _addEventListeners() {\n      $(this._element).on(Event.CLICK, (event) => {\n        event.preventDefault()\n        event.stopPropagation()\n        this.toggle()\n      })\n    }\n\n    _getConfig(config) {\n      config = $.extend(\n        {},\n        this.constructor.Default,\n        $(this._element).data(),\n        config\n      )\n\n      Util.typeCheckConfig(\n        NAME,\n        config,\n        this.constructor.DefaultType\n      )\n\n      return config\n    }\n\n    _getMenuElement() {\n      if (!this._menu) {\n        const parent = Dropdown._getParentFromElement(this._element)\n        this._menu = $(parent).find(Selector.MENU)[0]\n      }\n      return this._menu\n    }\n\n    _getPlacement() {\n      const $parentDropdown = $(this._element).parent()\n      let placement = AttachmentMap.BOTTOM\n\n      // Handle dropup\n      if ($parentDropdown.hasClass(ClassName.DROPUP)) {\n        placement = AttachmentMap.TOP\n        if ($(this._menu).hasClass(ClassName.MENURIGHT)) {\n          placement = AttachmentMap.TOPEND\n        }\n      } else if ($(this._menu).hasClass(ClassName.MENURIGHT)) {\n        placement = AttachmentMap.BOTTOMEND\n      }\n      return placement\n    }\n\n    _detectNavbar() {\n      return $(this._element).closest('.navbar').length > 0\n    }\n\n    _getPopperConfig() {\n      const offsetConf = {}\n      if (typeof this._config.offset === 'function') {\n        offsetConf.fn = (data) => {\n          data.offsets = $.extend({}, data.offsets, this._config.offset(data.offsets) || {})\n          return data\n        }\n      } else {\n        offsetConf.offset = this._config.offset\n      }\n      const popperConfig = {\n        placement : this._getPlacement(),\n        modifiers : {\n          offset : offsetConf,\n          flip : {\n            enabled : this._config.flip\n          }\n        }\n      }\n\n      // Disable Popper.js for Dropdown in Navbar\n      if (this._inNavbar) {\n        popperConfig.modifiers.applyStyle = {\n          enabled: !this._inNavbar\n        }\n      }\n      return popperConfig\n    }\n\n    // static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        let data = $(this).data(DATA_KEY)\n        const _config = typeof config === 'object' ? config : null\n\n        if (!data) {\n          data = new Dropdown(this, _config)\n          $(this).data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'string') {\n          if (typeof data[config] === 'undefined') {\n            throw new Error(`No method named \"${config}\"`)\n          }\n          data[config]()\n        }\n      })\n    }\n\n    static _clearMenus(event) {\n      if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH ||\n        event.type === 'keyup' && event.which !== TAB_KEYCODE)) {\n        return\n      }\n\n      const toggles = $.makeArray($(Selector.DATA_TOGGLE))\n      for (let i = 0; i < toggles.length; i++) {\n        const parent        = Dropdown._getParentFromElement(toggles[i])\n        const context       = $(toggles[i]).data(DATA_KEY)\n        const relatedTarget = {\n          relatedTarget : toggles[i]\n        }\n\n        if (!context) {\n          continue\n        }\n\n        const dropdownMenu = context._menu\n        if (!$(parent).hasClass(ClassName.SHOW)) {\n          continue\n        }\n\n        if (event && (event.type === 'click' &&\n            /input|textarea/i.test(event.target.tagName) || event.type === 'keyup' && event.which === TAB_KEYCODE)\n            && $.contains(parent, event.target)) {\n          continue\n        }\n\n        const hideEvent = $.Event(Event.HIDE, relatedTarget)\n        $(parent).trigger(hideEvent)\n        if (hideEvent.isDefaultPrevented()) {\n          continue\n        }\n\n        // if this is a touch-enabled device we remove the extra\n        // empty mouseover listeners we added for iOS support\n        if ('ontouchstart' in document.documentElement) {\n          $('body').children().off('mouseover', null, $.noop)\n        }\n\n        toggles[i].setAttribute('aria-expanded', 'false')\n\n        $(dropdownMenu).removeClass(ClassName.SHOW)\n        $(parent)\n          .removeClass(ClassName.SHOW)\n          .trigger($.Event(Event.HIDDEN, relatedTarget))\n      }\n    }\n\n    static _getParentFromElement(element) {\n      let parent\n      const selector = Util.getSelectorFromElement(element)\n\n      if (selector) {\n        parent = $(selector)[0]\n      }\n\n      return parent || element.parentNode\n    }\n\n    static _dataApiKeydownHandler(event) {\n      if (!REGEXP_KEYDOWN.test(event.which) || /button/i.test(event.target.tagName) && event.which === SPACE_KEYCODE ||\n         /input|textarea/i.test(event.target.tagName)) {\n        return\n      }\n\n      event.preventDefault()\n      event.stopPropagation()\n\n      if (this.disabled || $(this).hasClass(ClassName.DISABLED)) {\n        return\n      }\n\n      const parent   = Dropdown._getParentFromElement(this)\n      const isActive = $(parent).hasClass(ClassName.SHOW)\n\n      if (!isActive && (event.which !== ESCAPE_KEYCODE || event.which !== SPACE_KEYCODE) ||\n           isActive && (event.which === ESCAPE_KEYCODE || event.which === SPACE_KEYCODE)) {\n\n        if (event.which === ESCAPE_KEYCODE) {\n          const toggle = $(parent).find(Selector.DATA_TOGGLE)[0]\n          $(toggle).trigger('focus')\n        }\n\n        $(this).trigger('click')\n        return\n      }\n\n      const items = $(parent).find(Selector.VISIBLE_ITEMS).get()\n\n      if (!items.length) {\n        return\n      }\n\n      let index = items.indexOf(event.target)\n\n      if (event.which === ARROW_UP_KEYCODE && index > 0) { // up\n        index--\n      }\n\n      if (event.which === ARROW_DOWN_KEYCODE && index < items.length - 1) { // down\n        index++\n      }\n\n      if (index < 0) {\n        index = 0\n      }\n\n      items[index].focus()\n    }\n\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(document)\n    .on(Event.KEYDOWN_DATA_API, Selector.DATA_TOGGLE,  Dropdown._dataApiKeydownHandler)\n    .on(Event.KEYDOWN_DATA_API, Selector.MENU, Dropdown._dataApiKeydownHandler)\n    .on(`${Event.CLICK_DATA_API} ${Event.KEYUP_DATA_API}`, Dropdown._clearMenus)\n    .on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE, function (event) {\n      event.preventDefault()\n      event.stopPropagation()\n      Dropdown._jQueryInterface.call($(this), 'toggle')\n    })\n    .on(Event.CLICK_DATA_API, Selector.FORM_CHILD, (e) => {\n      e.stopPropagation()\n    })\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME]             = Dropdown._jQueryInterface\n  $.fn[NAME].Constructor = Dropdown\n  $.fn[NAME].noConflict  = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Dropdown._jQueryInterface\n  }\n\n  return Dropdown\n\n})($, Popper)\n\nexport default Dropdown\n", "import $ from 'jquery'\nimport Util from './util'\n\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.0.0-beta.2): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Modal = (() => {\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                         = 'modal'\n  const VERSION                      = '4.0.0-beta.2'\n  const DATA_KEY                     = 'bs.modal'\n  const EVENT_KEY                    = `.${DATA_KEY}`\n  const DATA_API_KEY                 = '.data-api'\n  const JQUERY_NO_CONFLICT           = $.fn[NAME]\n  const TRANSITION_DURATION          = 300\n  const BACKDROP_TRANSITION_DURATION = 150\n  const ESCAPE_KEYCODE               = 27 // KeyboardEvent.which value for Escape (Esc) key\n\n  const Default = {\n    backdrop : true,\n    keyboard : true,\n    focus    : true,\n    show     : true\n  }\n\n  const DefaultType = {\n    backdrop : '(boolean|string)',\n    keyboard : 'boolean',\n    focus    : 'boolean',\n    show     : 'boolean'\n  }\n\n  const Event = {\n    HIDE              : `hide${EVENT_KEY}`,\n    HIDDEN            : `hidden${EVENT_KEY}`,\n    SHOW              : `show${EVENT_KEY}`,\n    SHOWN             : `shown${EVENT_KEY}`,\n    FOCUSIN           : `focusin${EVENT_KEY}`,\n    RESIZE            : `resize${EVENT_KEY}`,\n    CLICK_DISMISS     : `click.dismiss${EVENT_KEY}`,\n    KEYDOWN_DISMISS   : `keydown.dismiss${EVENT_KEY}`,\n    MOUSEUP_DISMISS   : `mouseup.dismiss${EVENT_KEY}`,\n    MOUSEDOWN_DISMISS : `mousedown.dismiss${EVENT_KEY}`,\n    CLICK_DATA_API    : `click${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  const ClassName = {\n    SCROLLBAR_MEASURER : 'modal-scrollbar-measure',\n    BACKDROP           : 'modal-backdrop',\n    OPEN               : 'modal-open',\n    FADE               : 'fade',\n    SHOW               : 'show'\n  }\n\n  const Selector = {\n    DIALOG             : '.modal-dialog',\n    DATA_TOGGLE        : '[data-toggle=\"modal\"]',\n    DATA_DISMISS       : '[data-dismiss=\"modal\"]',\n    FIXED_CONTENT      : '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top',\n    STICKY_CONTENT     : '.sticky-top',\n    NAVBAR_TOGGLER     : '.navbar-toggler'\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Modal {\n\n    constructor(element, config) {\n      this._config              = this._getConfig(config)\n      this._element             = element\n      this._dialog              = $(element).find(Selector.DIALOG)[0]\n      this._backdrop            = null\n      this._isShown             = false\n      this._isBodyOverflowing   = false\n      this._ignoreBackdropClick = false\n      this._originalBodyPadding = 0\n      this._scrollbarWidth      = 0\n    }\n\n\n    // getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    static get Default() {\n      return Default\n    }\n\n\n    // public\n\n    toggle(relatedTarget) {\n      return this._isShown ? this.hide() : this.show(relatedTarget)\n    }\n\n    show(relatedTarget) {\n      if (this._isTransitioning || this._isShown) {\n        return\n      }\n\n      if (Util.supportsTransitionEnd() && $(this._element).hasClass(ClassName.FADE)) {\n        this._isTransitioning = true\n      }\n\n      const showEvent = $.Event(Event.SHOW, {\n        relatedTarget\n      })\n\n      $(this._element).trigger(showEvent)\n\n      if (this._isShown || showEvent.isDefaultPrevented()) {\n        return\n      }\n\n      this._isShown = true\n\n      this._checkScrollbar()\n      this._setScrollbar()\n\n      this._adjustDialog()\n\n      $(document.body).addClass(ClassName.OPEN)\n\n      this._setEscapeEvent()\n      this._setResizeEvent()\n\n      $(this._element).on(\n        Event.CLICK_DISMISS,\n        Selector.DATA_DISMISS,\n        (event) => this.hide(event)\n      )\n\n      $(this._dialog).on(Event.MOUSEDOWN_DISMISS, () => {\n        $(this._element).one(Event.MOUSEUP_DISMISS, (event) => {\n          if ($(event.target).is(this._element)) {\n            this._ignoreBackdropClick = true\n          }\n        })\n      })\n\n      this._showBackdrop(() => this._showElement(relatedTarget))\n    }\n\n    hide(event) {\n      if (event) {\n        event.preventDefault()\n      }\n\n      if (this._isTransitioning || !this._isShown) {\n        return\n      }\n\n      const hideEvent = $.Event(Event.HIDE)\n\n      $(this._element).trigger(hideEvent)\n\n      if (!this._isShown || hideEvent.isDefaultPrevented()) {\n        return\n      }\n\n      this._isShown = false\n\n      const transition = Util.supportsTransitionEnd() && $(this._element).hasClass(ClassName.FADE)\n\n      if (transition) {\n        this._isTransitioning = true\n      }\n\n      this._setEscapeEvent()\n      this._setResizeEvent()\n\n      $(document).off(Event.FOCUSIN)\n\n      $(this._element).removeClass(ClassName.SHOW)\n\n      $(this._element).off(Event.CLICK_DISMISS)\n      $(this._dialog).off(Event.MOUSEDOWN_DISMISS)\n\n      if (transition) {\n\n        $(this._element)\n          .one(Util.TRANSITION_END, (event) => this._hideModal(event))\n          .emulateTransitionEnd(TRANSITION_DURATION)\n      } else {\n        this._hideModal()\n      }\n    }\n\n    dispose() {\n      $.removeData(this._element, DATA_KEY)\n\n      $(window, document, this._element, this._backdrop).off(EVENT_KEY)\n\n      this._config              = null\n      this._element             = null\n      this._dialog              = null\n      this._backdrop            = null\n      this._isShown             = null\n      this._isBodyOverflowing   = null\n      this._ignoreBackdropClick = null\n      this._scrollbarWidth      = null\n    }\n\n    handleUpdate() {\n      this._adjustDialog()\n    }\n\n    // private\n\n    _getConfig(config) {\n      config = $.extend({}, Default, config)\n      Util.typeCheckConfig(NAME, config, DefaultType)\n      return config\n    }\n\n    _showElement(relatedTarget) {\n      const transition = Util.supportsTransitionEnd() &&\n        $(this._element).hasClass(ClassName.FADE)\n\n      if (!this._element.parentNode ||\n         this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n        // don't move modals dom position\n        document.body.appendChild(this._element)\n      }\n\n      this._element.style.display = 'block'\n      this._element.removeAttribute('aria-hidden')\n      this._element.scrollTop = 0\n\n      if (transition) {\n        Util.reflow(this._element)\n      }\n\n      $(this._element).addClass(ClassName.SHOW)\n\n      if (this._config.focus) {\n        this._enforceFocus()\n      }\n\n      const shownEvent = $.Event(Event.SHOWN, {\n        relatedTarget\n      })\n\n      const transitionComplete = () => {\n        if (this._config.focus) {\n          this._element.focus()\n        }\n        this._isTransitioning = false\n        $(this._element).trigger(shownEvent)\n      }\n\n      if (transition) {\n        $(this._dialog)\n          .one(Util.TRANSITION_END, transitionComplete)\n          .emulateTransitionEnd(TRANSITION_DURATION)\n      } else {\n        transitionComplete()\n      }\n    }\n\n    _enforceFocus() {\n      $(document)\n        .off(Event.FOCUSIN) // guard against infinite focus loop\n        .on(Event.FOCUSIN, (event) => {\n          if (document !== event.target &&\n              this._element !== event.target &&\n              !$(this._element).has(event.target).length) {\n            this._element.focus()\n          }\n        })\n    }\n\n    _setEscapeEvent() {\n      if (this._isShown && this._config.keyboard) {\n        $(this._element).on(Event.KEYDOWN_DISMISS, (event) => {\n          if (event.which === ESCAPE_KEYCODE) {\n            event.preventDefault()\n            this.hide()\n          }\n        })\n\n      } else if (!this._isShown) {\n        $(this._element).off(Event.KEYDOWN_DISMISS)\n      }\n    }\n\n    _setResizeEvent() {\n      if (this._isShown) {\n        $(window).on(Event.RESIZE, (event) => this.handleUpdate(event))\n      } else {\n        $(window).off(Event.RESIZE)\n      }\n    }\n\n    _hideModal() {\n      this._element.style.display = 'none'\n      this._element.setAttribute('aria-hidden', true)\n      this._isTransitioning = false\n      this._showBackdrop(() => {\n        $(document.body).removeClass(ClassName.OPEN)\n        this._resetAdjustments()\n        this._resetScrollbar()\n        $(this._element).trigger(Event.HIDDEN)\n      })\n    }\n\n    _removeBackdrop() {\n      if (this._backdrop) {\n        $(this._backdrop).remove()\n        this._backdrop = null\n      }\n    }\n\n    _showBackdrop(callback) {\n      const animate = $(this._element).hasClass(ClassName.FADE) ?\n        ClassName.FADE : ''\n\n      if (this._isShown && this._config.backdrop) {\n        const doAnimate = Util.supportsTransitionEnd() && animate\n\n        this._backdrop = document.createElement('div')\n        this._backdrop.className = ClassName.BACKDROP\n\n        if (animate) {\n          $(this._backdrop).addClass(animate)\n        }\n\n        $(this._backdrop).appendTo(document.body)\n\n        $(this._element).on(Event.CLICK_DISMISS, (event) => {\n          if (this._ignoreBackdropClick) {\n            this._ignoreBackdropClick = false\n            return\n          }\n          if (event.target !== event.currentTarget) {\n            return\n          }\n          if (this._config.backdrop === 'static') {\n            this._element.focus()\n          } else {\n            this.hide()\n          }\n        })\n\n        if (doAnimate) {\n          Util.reflow(this._backdrop)\n        }\n\n        $(this._backdrop).addClass(ClassName.SHOW)\n\n        if (!callback) {\n          return\n        }\n\n        if (!doAnimate) {\n          callback()\n          return\n        }\n\n        $(this._backdrop)\n          .one(Util.TRANSITION_END, callback)\n          .emulateTransitionEnd(BACKDROP_TRANSITION_DURATION)\n\n      } else if (!this._isShown && this._backdrop) {\n        $(this._backdrop).removeClass(ClassName.SHOW)\n\n        const callbackRemove = () => {\n          this._removeBackdrop()\n          if (callback) {\n            callback()\n          }\n        }\n\n        if (Util.supportsTransitionEnd() &&\n           $(this._element).hasClass(ClassName.FADE)) {\n          $(this._backdrop)\n            .one(Util.TRANSITION_END, callbackRemove)\n            .emulateTransitionEnd(BACKDROP_TRANSITION_DURATION)\n        } else {\n          callbackRemove()\n        }\n\n      } else if (callback) {\n        callback()\n      }\n    }\n\n\n    // ----------------------------------------------------------------------\n    // the following methods are used to handle overflowing modals\n    // todo (fat): these should probably be refactored out of modal.js\n    // ----------------------------------------------------------------------\n\n    _adjustDialog() {\n      const isModalOverflowing =\n        this._element.scrollHeight > document.documentElement.clientHeight\n\n      if (!this._isBodyOverflowing && isModalOverflowing) {\n        this._element.style.paddingLeft = `${this._scrollbarWidth}px`\n      }\n\n      if (this._isBodyOverflowing && !isModalOverflowing) {\n        this._element.style.paddingRight = `${this._scrollbarWidth}px`\n      }\n    }\n\n    _resetAdjustments() {\n      this._element.style.paddingLeft = ''\n      this._element.style.paddingRight = ''\n    }\n\n    _checkScrollbar() {\n      const rect = document.body.getBoundingClientRect()\n      this._isBodyOverflowing = rect.left + rect.right < window.innerWidth\n      this._scrollbarWidth = this._getScrollbarWidth()\n    }\n\n    _setScrollbar() {\n      if (this._isBodyOverflowing) {\n        // Note: DOMNode.style.paddingRight returns the actual value or '' if not set\n        //   while $(DOMNode).css('padding-right') returns the calculated value or 0 if not set\n\n        // Adjust fixed content padding\n        $(Selector.FIXED_CONTENT).each((index, element) => {\n          const actualPadding = $(element)[0].style.paddingRight\n          const calculatedPadding = $(element).css('padding-right')\n          $(element).data('padding-right', actualPadding).css('padding-right', `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`)\n        })\n\n        // Adjust sticky content margin\n        $(Selector.STICKY_CONTENT).each((index, element) => {\n          const actualMargin = $(element)[0].style.marginRight\n          const calculatedMargin = $(element).css('margin-right')\n          $(element).data('margin-right', actualMargin).css('margin-right', `${parseFloat(calculatedMargin) - this._scrollbarWidth}px`)\n        })\n\n        // Adjust navbar-toggler margin\n        $(Selector.NAVBAR_TOGGLER).each((index, element) => {\n          const actualMargin = $(element)[0].style.marginRight\n          const calculatedMargin = $(element).css('margin-right')\n          $(element).data('margin-right', actualMargin).css('margin-right', `${parseFloat(calculatedMargin) + this._scrollbarWidth}px`)\n        })\n\n        // Adjust body padding\n        const actualPadding = document.body.style.paddingRight\n        const calculatedPadding = $('body').css('padding-right')\n        $('body').data('padding-right', actualPadding).css('padding-right', `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`)\n      }\n    }\n\n    _resetScrollbar() {\n      // Restore fixed content padding\n      $(Selector.FIXED_CONTENT).each((index, element) => {\n        const padding = $(element).data('padding-right')\n        if (typeof padding !== 'undefined') {\n          $(element).css('padding-right', padding).removeData('padding-right')\n        }\n      })\n\n      // Restore sticky content and navbar-toggler margin\n      $(`${Selector.STICKY_CONTENT}, ${Selector.NAVBAR_TOGGLER}`).each((index, element) => {\n        const margin = $(element).data('margin-right')\n        if (typeof margin !== 'undefined') {\n          $(element).css('margin-right', margin).removeData('margin-right')\n        }\n      })\n\n      // Restore body padding\n      const padding = $('body').data('padding-right')\n      if (typeof padding !== 'undefined') {\n        $('body').css('padding-right', padding).removeData('padding-right')\n      }\n    }\n\n    _getScrollbarWidth() { // thx d.walsh\n      const scrollDiv = document.createElement('div')\n      scrollDiv.className = ClassName.SCROLLBAR_MEASURER\n      document.body.appendChild(scrollDiv)\n      const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth\n      document.body.removeChild(scrollDiv)\n      return scrollbarWidth\n    }\n\n\n    // static\n\n    static _jQueryInterface(config, relatedTarget) {\n      return this.each(function () {\n        let data      = $(this).data(DATA_KEY)\n        const _config = $.extend(\n          {},\n          Modal.Default,\n          $(this).data(),\n          typeof config === 'object' && config\n        )\n\n        if (!data) {\n          data = new Modal(this, _config)\n          $(this).data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'string') {\n          if (typeof data[config] === 'undefined') {\n            throw new Error(`No method named \"${config}\"`)\n          }\n          data[config](relatedTarget)\n        } else if (_config.show) {\n          data.show(relatedTarget)\n        }\n      })\n    }\n\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(document).on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE, function (event) {\n    let target\n    const selector = Util.getSelectorFromElement(this)\n\n    if (selector) {\n      target = $(selector)[0]\n    }\n\n    const config = $(target).data(DATA_KEY) ?\n      'toggle' : $.extend({}, $(target).data(), $(this).data())\n\n    if (this.tagName === 'A' || this.tagName === 'AREA') {\n      event.preventDefault()\n    }\n\n    const $target = $(target).one(Event.SHOW, (showEvent) => {\n      if (showEvent.isDefaultPrevented()) {\n        // only register focus restorer if modal will actually get shown\n        return\n      }\n\n      $target.one(Event.HIDDEN, () => {\n        if ($(this).is(':visible')) {\n          this.focus()\n        }\n      })\n    })\n\n    Modal._jQueryInterface.call($(target), config, this)\n  })\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME]             = Modal._jQueryInterface\n  $.fn[NAME].Constructor = Modal\n  $.fn[NAME].noConflict  = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Modal._jQueryInterface\n  }\n\n  return Modal\n\n})($)\n\nexport default Modal\n", "import $ from 'jquery'\nimport Popper from 'popper.js'\nimport Util from './util'\n\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON>trap (v4.0.0-beta.2): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Tooltip = (() => {\n\n  /**\n   * Check for Popper dependency\n   * Popper - https://popper.js.org\n   */\n  if (typeof Popper === 'undefined') {\n    throw new Error('Bootstrap tooltips require Popper.js (https://popper.js.org)')\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                = 'tooltip'\n  const VERSION             = '4.0.0-beta.2'\n  const DATA_KEY            = 'bs.tooltip'\n  const EVENT_KEY           = `.${DATA_KEY}`\n  const JQUERY_NO_CONFLICT  = $.fn[NAME]\n  const TRANSITION_DURATION = 150\n  const CLASS_PREFIX        = 'bs-tooltip'\n  const BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\n  const DefaultType = {\n    animation           : 'boolean',\n    template            : 'string',\n    title               : '(string|element|function)',\n    trigger             : 'string',\n    delay               : '(number|object)',\n    html                : 'boolean',\n    selector            : '(string|boolean)',\n    placement           : '(string|function)',\n    offset              : '(number|string)',\n    container           : '(string|element|boolean)',\n    fallbackPlacement   : '(string|array)'\n  }\n\n  const AttachmentMap = {\n    AUTO   : 'auto',\n    TOP    : 'top',\n    RIGHT  : 'right',\n    BOTTOM : 'bottom',\n    LEFT   : 'left'\n  }\n\n  const Default = {\n    animation           : true,\n    template            : '<div class=\"tooltip\" role=\"tooltip\">'\n                        + '<div class=\"arrow\"></div>'\n                        + '<div class=\"tooltip-inner\"></div></div>',\n    trigger             : 'hover focus',\n    title               : '',\n    delay               : 0,\n    html                : false,\n    selector            : false,\n    placement           : 'top',\n    offset              : 0,\n    container           : false,\n    fallbackPlacement   : 'flip'\n  }\n\n  const HoverState = {\n    SHOW : 'show',\n    OUT  : 'out'\n  }\n\n  const Event = {\n    HIDE       : `hide${EVENT_KEY}`,\n    HIDDEN     : `hidden${EVENT_KEY}`,\n    SHOW       : `show${EVENT_KEY}`,\n    SHOWN      : `shown${EVENT_KEY}`,\n    INSERTED   : `inserted${EVENT_KEY}`,\n    CLICK      : `click${EVENT_KEY}`,\n    FOCUSIN    : `focusin${EVENT_KEY}`,\n    FOCUSOUT   : `focusout${EVENT_KEY}`,\n    MOUSEENTER : `mouseenter${EVENT_KEY}`,\n    MOUSELEAVE : `mouseleave${EVENT_KEY}`\n  }\n\n  const ClassName = {\n    FADE : 'fade',\n    SHOW : 'show'\n  }\n\n  const Selector = {\n    TOOLTIP       : '.tooltip',\n    TOOLTIP_INNER : '.tooltip-inner',\n    ARROW         : '.arrow'\n  }\n\n  const Trigger = {\n    HOVER  : 'hover',\n    FOCUS  : 'focus',\n    CLICK  : 'click',\n    MANUAL : 'manual'\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Tooltip {\n\n    constructor(element, config) {\n\n      // private\n      this._isEnabled     = true\n      this._timeout       = 0\n      this._hoverState    = ''\n      this._activeTrigger = {}\n      this._popper        = null\n\n      // protected\n      this.element = element\n      this.config  = this._getConfig(config)\n      this.tip     = null\n\n      this._setListeners()\n\n    }\n\n\n    // getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    static get Default() {\n      return Default\n    }\n\n    static get NAME() {\n      return NAME\n    }\n\n    static get DATA_KEY() {\n      return DATA_KEY\n    }\n\n    static get Event() {\n      return Event\n    }\n\n    static get EVENT_KEY() {\n      return EVENT_KEY\n    }\n\n    static get DefaultType() {\n      return DefaultType\n    }\n\n\n    // public\n\n    enable() {\n      this._isEnabled = true\n    }\n\n    disable() {\n      this._isEnabled = false\n    }\n\n    toggleEnabled() {\n      this._isEnabled = !this._isEnabled\n    }\n\n    toggle(event) {\n      if (!this._isEnabled) {\n        return\n      }\n\n      if (event) {\n        const dataKey = this.constructor.DATA_KEY\n        let context = $(event.currentTarget).data(dataKey)\n\n        if (!context) {\n          context = new this.constructor(\n            event.currentTarget,\n            this._getDelegateConfig()\n          )\n          $(event.currentTarget).data(dataKey, context)\n        }\n\n        context._activeTrigger.click = !context._activeTrigger.click\n\n        if (context._isWithActiveTrigger()) {\n          context._enter(null, context)\n        } else {\n          context._leave(null, context)\n        }\n\n      } else {\n\n        if ($(this.getTipElement()).hasClass(ClassName.SHOW)) {\n          this._leave(null, this)\n          return\n        }\n\n        this._enter(null, this)\n      }\n    }\n\n    dispose() {\n      clearTimeout(this._timeout)\n\n      $.removeData(this.element, this.constructor.DATA_KEY)\n\n      $(this.element).off(this.constructor.EVENT_KEY)\n      $(this.element).closest('.modal').off('hide.bs.modal')\n\n      if (this.tip) {\n        $(this.tip).remove()\n      }\n\n      this._isEnabled     = null\n      this._timeout       = null\n      this._hoverState    = null\n      this._activeTrigger = null\n      if (this._popper !== null) {\n        this._popper.destroy()\n      }\n\n      this._popper = null\n      this.element = null\n      this.config  = null\n      this.tip     = null\n    }\n\n    show() {\n      if ($(this.element).css('display') === 'none') {\n        throw new Error('Please use show on visible elements')\n      }\n\n      const showEvent = $.Event(this.constructor.Event.SHOW)\n      if (this.isWithContent() && this._isEnabled) {\n        $(this.element).trigger(showEvent)\n\n        const isInTheDom = $.contains(\n          this.element.ownerDocument.documentElement,\n          this.element\n        )\n\n        if (showEvent.isDefaultPrevented() || !isInTheDom) {\n          return\n        }\n\n        const tip   = this.getTipElement()\n        const tipId = Util.getUID(this.constructor.NAME)\n\n        tip.setAttribute('id', tipId)\n        this.element.setAttribute('aria-describedby', tipId)\n\n        this.setContent()\n\n        if (this.config.animation) {\n          $(tip).addClass(ClassName.FADE)\n        }\n\n        const placement  = typeof this.config.placement === 'function' ?\n          this.config.placement.call(this, tip, this.element) :\n          this.config.placement\n\n        const attachment = this._getAttachment(placement)\n        this.addAttachmentClass(attachment)\n\n        const container = this.config.container === false ? document.body : $(this.config.container)\n\n        $(tip).data(this.constructor.DATA_KEY, this)\n\n        if (!$.contains(this.element.ownerDocument.documentElement, this.tip)) {\n          $(tip).appendTo(container)\n        }\n\n        $(this.element).trigger(this.constructor.Event.INSERTED)\n\n        this._popper = new Popper(this.element, tip, {\n          placement: attachment,\n          modifiers: {\n            offset: {\n              offset: this.config.offset\n            },\n            flip: {\n              behavior: this.config.fallbackPlacement\n            },\n            arrow: {\n              element: Selector.ARROW\n            }\n          },\n          onCreate: (data) => {\n            if (data.originalPlacement !== data.placement) {\n              this._handlePopperPlacementChange(data)\n            }\n          },\n          onUpdate : (data) => {\n            this._handlePopperPlacementChange(data)\n          }\n        })\n\n        $(tip).addClass(ClassName.SHOW)\n\n        // if this is a touch-enabled device we add extra\n        // empty mouseover listeners to the body's immediate children;\n        // only needed because of broken event delegation on iOS\n        // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n        if ('ontouchstart' in document.documentElement) {\n          $('body').children().on('mouseover', null, $.noop)\n        }\n\n        const complete = () => {\n          if (this.config.animation) {\n            this._fixTransition()\n          }\n          const prevHoverState = this._hoverState\n          this._hoverState     = null\n\n          $(this.element).trigger(this.constructor.Event.SHOWN)\n\n          if (prevHoverState === HoverState.OUT) {\n            this._leave(null, this)\n          }\n        }\n\n        if (Util.supportsTransitionEnd() && $(this.tip).hasClass(ClassName.FADE)) {\n          $(this.tip)\n            .one(Util.TRANSITION_END, complete)\n            .emulateTransitionEnd(Tooltip._TRANSITION_DURATION)\n        } else {\n          complete()\n        }\n      }\n    }\n\n    hide(callback) {\n      const tip       = this.getTipElement()\n      const hideEvent = $.Event(this.constructor.Event.HIDE)\n      const complete  = () => {\n        if (this._hoverState !== HoverState.SHOW && tip.parentNode) {\n          tip.parentNode.removeChild(tip)\n        }\n\n        this._cleanTipClass()\n        this.element.removeAttribute('aria-describedby')\n        $(this.element).trigger(this.constructor.Event.HIDDEN)\n        if (this._popper !== null) {\n          this._popper.destroy()\n        }\n\n        if (callback) {\n          callback()\n        }\n      }\n\n      $(this.element).trigger(hideEvent)\n\n      if (hideEvent.isDefaultPrevented()) {\n        return\n      }\n\n      $(tip).removeClass(ClassName.SHOW)\n\n      // if this is a touch-enabled device we remove the extra\n      // empty mouseover listeners we added for iOS support\n      if ('ontouchstart' in document.documentElement) {\n        $('body').children().off('mouseover', null, $.noop)\n      }\n\n      this._activeTrigger[Trigger.CLICK] = false\n      this._activeTrigger[Trigger.FOCUS] = false\n      this._activeTrigger[Trigger.HOVER] = false\n\n      if (Util.supportsTransitionEnd() &&\n          $(this.tip).hasClass(ClassName.FADE)) {\n\n        $(tip)\n          .one(Util.TRANSITION_END, complete)\n          .emulateTransitionEnd(TRANSITION_DURATION)\n\n      } else {\n        complete()\n      }\n\n      this._hoverState = ''\n\n    }\n\n    update() {\n      if (this._popper !== null) {\n        this._popper.scheduleUpdate()\n      }\n    }\n\n    // protected\n\n    isWithContent() {\n      return Boolean(this.getTitle())\n    }\n\n    addAttachmentClass(attachment) {\n      $(this.getTipElement()).addClass(`${CLASS_PREFIX}-${attachment}`)\n    }\n\n    getTipElement() {\n      this.tip = this.tip || $(this.config.template)[0]\n      return this.tip\n    }\n\n    setContent() {\n      const $tip = $(this.getTipElement())\n      this.setElementContent($tip.find(Selector.TOOLTIP_INNER), this.getTitle())\n      $tip.removeClass(`${ClassName.FADE} ${ClassName.SHOW}`)\n    }\n\n    setElementContent($element, content) {\n      const html = this.config.html\n      if (typeof content === 'object' && (content.nodeType || content.jquery)) {\n        // content is a DOM node or a jQuery\n        if (html) {\n          if (!$(content).parent().is($element)) {\n            $element.empty().append(content)\n          }\n        } else {\n          $element.text($(content).text())\n        }\n      } else {\n        $element[html ? 'html' : 'text'](content)\n      }\n    }\n\n    getTitle() {\n      let title = this.element.getAttribute('data-original-title')\n\n      if (!title) {\n        title = typeof this.config.title === 'function' ?\n          this.config.title.call(this.element) :\n          this.config.title\n      }\n\n      return title\n    }\n\n\n    // private\n\n    _getAttachment(placement) {\n      return AttachmentMap[placement.toUpperCase()]\n    }\n\n    _setListeners() {\n      const triggers = this.config.trigger.split(' ')\n\n      triggers.forEach((trigger) => {\n        if (trigger === 'click') {\n          $(this.element).on(\n            this.constructor.Event.CLICK,\n            this.config.selector,\n            (event) => this.toggle(event)\n          )\n\n        } else if (trigger !== Trigger.MANUAL) {\n          const eventIn  = trigger === Trigger.HOVER ?\n            this.constructor.Event.MOUSEENTER :\n            this.constructor.Event.FOCUSIN\n          const eventOut = trigger === Trigger.HOVER ?\n            this.constructor.Event.MOUSELEAVE :\n            this.constructor.Event.FOCUSOUT\n\n          $(this.element)\n            .on(\n              eventIn,\n              this.config.selector,\n              (event) => this._enter(event)\n            )\n            .on(\n              eventOut,\n              this.config.selector,\n              (event) => this._leave(event)\n            )\n        }\n\n        $(this.element).closest('.modal').on(\n          'hide.bs.modal',\n          () => this.hide()\n        )\n      })\n\n      if (this.config.selector) {\n        this.config = $.extend({}, this.config, {\n          trigger  : 'manual',\n          selector : ''\n        })\n      } else {\n        this._fixTitle()\n      }\n    }\n\n    _fixTitle() {\n      const titleType = typeof this.element.getAttribute('data-original-title')\n      if (this.element.getAttribute('title') ||\n         titleType !== 'string') {\n        this.element.setAttribute(\n          'data-original-title',\n          this.element.getAttribute('title') || ''\n        )\n        this.element.setAttribute('title', '')\n      }\n    }\n\n    _enter(event, context) {\n      const dataKey = this.constructor.DATA_KEY\n\n      context = context || $(event.currentTarget).data(dataKey)\n\n      if (!context) {\n        context = new this.constructor(\n          event.currentTarget,\n          this._getDelegateConfig()\n        )\n        $(event.currentTarget).data(dataKey, context)\n      }\n\n      if (event) {\n        context._activeTrigger[\n          event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER\n        ] = true\n      }\n\n      if ($(context.getTipElement()).hasClass(ClassName.SHOW) ||\n         context._hoverState === HoverState.SHOW) {\n        context._hoverState = HoverState.SHOW\n        return\n      }\n\n      clearTimeout(context._timeout)\n\n      context._hoverState = HoverState.SHOW\n\n      if (!context.config.delay || !context.config.delay.show) {\n        context.show()\n        return\n      }\n\n      context._timeout = setTimeout(() => {\n        if (context._hoverState === HoverState.SHOW) {\n          context.show()\n        }\n      }, context.config.delay.show)\n    }\n\n    _leave(event, context) {\n      const dataKey = this.constructor.DATA_KEY\n\n      context = context || $(event.currentTarget).data(dataKey)\n\n      if (!context) {\n        context = new this.constructor(\n          event.currentTarget,\n          this._getDelegateConfig()\n        )\n        $(event.currentTarget).data(dataKey, context)\n      }\n\n      if (event) {\n        context._activeTrigger[\n          event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER\n        ] = false\n      }\n\n      if (context._isWithActiveTrigger()) {\n        return\n      }\n\n      clearTimeout(context._timeout)\n\n      context._hoverState = HoverState.OUT\n\n      if (!context.config.delay || !context.config.delay.hide) {\n        context.hide()\n        return\n      }\n\n      context._timeout = setTimeout(() => {\n        if (context._hoverState === HoverState.OUT) {\n          context.hide()\n        }\n      }, context.config.delay.hide)\n    }\n\n    _isWithActiveTrigger() {\n      for (const trigger in this._activeTrigger) {\n        if (this._activeTrigger[trigger]) {\n          return true\n        }\n      }\n\n      return false\n    }\n\n    _getConfig(config) {\n      config = $.extend(\n        {},\n        this.constructor.Default,\n        $(this.element).data(),\n        config\n      )\n\n      if (typeof config.delay === 'number') {\n        config.delay = {\n          show : config.delay,\n          hide : config.delay\n        }\n      }\n\n      if (typeof config.title === 'number') {\n        config.title = config.title.toString()\n      }\n\n      if (typeof config.content === 'number') {\n        config.content = config.content.toString()\n      }\n\n      Util.typeCheckConfig(\n        NAME,\n        config,\n        this.constructor.DefaultType\n      )\n\n      return config\n    }\n\n    _getDelegateConfig() {\n      const config = {}\n\n      if (this.config) {\n        for (const key in this.config) {\n          if (this.constructor.Default[key] !== this.config[key]) {\n            config[key] = this.config[key]\n          }\n        }\n      }\n\n      return config\n    }\n\n    _cleanTipClass() {\n      const $tip = $(this.getTipElement())\n      const tabClass = $tip.attr('class').match(BSCLS_PREFIX_REGEX)\n      if (tabClass !== null && tabClass.length > 0) {\n        $tip.removeClass(tabClass.join(''))\n      }\n    }\n\n    _handlePopperPlacementChange(data) {\n      this._cleanTipClass()\n      this.addAttachmentClass(this._getAttachment(data.placement))\n    }\n\n    _fixTransition() {\n      const tip                 = this.getTipElement()\n      const initConfigAnimation = this.config.animation\n      if (tip.getAttribute('x-placement') !== null) {\n        return\n      }\n      $(tip).removeClass(ClassName.FADE)\n      this.config.animation = false\n      this.hide()\n      this.show()\n      this.config.animation = initConfigAnimation\n    }\n\n    // static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        let data      = $(this).data(DATA_KEY)\n        const _config = typeof config === 'object' && config\n\n        if (!data && /dispose|hide/.test(config)) {\n          return\n        }\n\n        if (!data) {\n          data = new Tooltip(this, _config)\n          $(this).data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'string') {\n          if (typeof data[config] === 'undefined') {\n            throw new Error(`No method named \"${config}\"`)\n          }\n          data[config]()\n        }\n      })\n    }\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME]             = Tooltip._jQueryInterface\n  $.fn[NAME].Constructor = Tooltip\n  $.fn[NAME].noConflict  = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Tooltip._jQueryInterface\n  }\n\n  return Tooltip\n\n})($, Popper)\n\nexport default Tooltip\n", "import $ from 'jquery'\nimport Tooltip from './tooltip'\n\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON>trap (v4.0.0-beta.2): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Popover = (() => {\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                = 'popover'\n  const VERSION             = '4.0.0-beta.2'\n  const DATA_KEY            = 'bs.popover'\n  const EVENT_KEY           = `.${DATA_KEY}`\n  const JQUERY_NO_CONFLICT  = $.fn[NAME]\n  const CLASS_PREFIX        = 'bs-popover'\n  const BSCLS_PREFIX_REGEX  = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\n  const Default = $.extend({}, Tooltip.Default, {\n    placement : 'right',\n    trigger   : 'click',\n    content   : '',\n    template  : '<div class=\"popover\" role=\"tooltip\">'\n              + '<div class=\"arrow\"></div>'\n              + '<h3 class=\"popover-header\"></h3>'\n              + '<div class=\"popover-body\"></div></div>'\n  })\n\n  const DefaultType = $.extend({}, Tooltip.DefaultType, {\n    content : '(string|element|function)'\n  })\n\n  const ClassName = {\n    FADE : 'fade',\n    SHOW : 'show'\n  }\n\n  const Selector = {\n    TITLE   : '.popover-header',\n    CONTENT : '.popover-body'\n  }\n\n  const Event = {\n    HIDE       : `hide${EVENT_KEY}`,\n    HIDDEN     : `hidden${EVENT_KEY}`,\n    SHOW       : `show${EVENT_KEY}`,\n    SHOWN      : `shown${EVENT_KEY}`,\n    INSERTED   : `inserted${EVENT_KEY}`,\n    CLICK      : `click${EVENT_KEY}`,\n    FOCUSIN    : `focusin${EVENT_KEY}`,\n    FOCUSOUT   : `focusout${EVENT_KEY}`,\n    MOUSEENTER : `mouseenter${EVENT_KEY}`,\n    MOUSELEAVE : `mouseleave${EVENT_KEY}`\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Popover extends Tooltip {\n\n\n    // getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    static get Default() {\n      return Default\n    }\n\n    static get NAME() {\n      return NAME\n    }\n\n    static get DATA_KEY() {\n      return DATA_KEY\n    }\n\n    static get Event() {\n      return Event\n    }\n\n    static get EVENT_KEY() {\n      return EVENT_KEY\n    }\n\n    static get DefaultType() {\n      return DefaultType\n    }\n\n\n    // overrides\n\n    isWithContent() {\n      return this.getTitle() || this._getContent()\n    }\n\n    addAttachmentClass(attachment) {\n      $(this.getTipElement()).addClass(`${CLASS_PREFIX}-${attachment}`)\n    }\n\n    getTipElement() {\n      this.tip = this.tip || $(this.config.template)[0]\n      return this.tip\n    }\n\n    setContent() {\n      const $tip = $(this.getTipElement())\n\n      // we use append for html objects to maintain js events\n      this.setElementContent($tip.find(Selector.TITLE), this.getTitle())\n      this.setElementContent($tip.find(Selector.CONTENT), this._getContent())\n\n      $tip.removeClass(`${ClassName.FADE} ${ClassName.SHOW}`)\n    }\n\n    // private\n\n    _getContent() {\n      return this.element.getAttribute('data-content')\n        || (typeof this.config.content === 'function' ?\n              this.config.content.call(this.element) :\n              this.config.content)\n    }\n\n    _cleanTipClass() {\n      const $tip = $(this.getTipElement())\n      const tabClass = $tip.attr('class').match(BSCLS_PREFIX_REGEX)\n      if (tabClass !== null && tabClass.length > 0) {\n        $tip.removeClass(tabClass.join(''))\n      }\n    }\n\n\n    // static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        let data      = $(this).data(DATA_KEY)\n        const _config = typeof config === 'object' ? config : null\n\n        if (!data && /destroy|hide/.test(config)) {\n          return\n        }\n\n        if (!data) {\n          data = new Popover(this, _config)\n          $(this).data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'string') {\n          if (typeof data[config] === 'undefined') {\n            throw new Error(`No method named \"${config}\"`)\n          }\n          data[config]()\n        }\n      })\n    }\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME]             = Popover._jQueryInterface\n  $.fn[NAME].Constructor = Popover\n  $.fn[NAME].noConflict  = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Popover._jQueryInterface\n  }\n\n  return Popover\n\n})($)\n\nexport default Popover\n", "import $ from 'jquery'\nimport Util from './util'\n\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON>trap (v4.0.0-beta.2): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst ScrollSpy = (() => {\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME               = 'scrollspy'\n  const VERSION            = '4.0.0-beta.2'\n  const DATA_KEY           = 'bs.scrollspy'\n  const EVENT_KEY          = `.${DATA_KEY}`\n  const DATA_API_KEY       = '.data-api'\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n\n  const Default = {\n    offset : 10,\n    method : 'auto',\n    target : ''\n  }\n\n  const DefaultType = {\n    offset : 'number',\n    method : 'string',\n    target : '(string|element)'\n  }\n\n  const Event = {\n    ACTIVATE      : `activate${EVENT_KEY}`,\n    SCROLL        : `scroll${EVENT_KEY}`,\n    LOAD_DATA_API : `load${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  const ClassName = {\n    DROPDOWN_ITEM : 'dropdown-item',\n    DROPDOWN_MENU : 'dropdown-menu',\n    ACTIVE        : 'active'\n  }\n\n  const Selector = {\n    DATA_SPY        : '[data-spy=\"scroll\"]',\n    ACTIVE          : '.active',\n    NAV_LIST_GROUP  : '.nav, .list-group',\n    NAV_LINKS       : '.nav-link',\n    NAV_ITEMS       : '.nav-item',\n    LIST_ITEMS      : '.list-group-item',\n    DROPDOWN        : '.dropdown',\n    DROPDOWN_ITEMS  : '.dropdown-item',\n    DROPDOWN_TOGGLE : '.dropdown-toggle'\n  }\n\n  const OffsetMethod = {\n    OFFSET   : 'offset',\n    POSITION : 'position'\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class ScrollSpy {\n\n    constructor(element, config) {\n      this._element       = element\n      this._scrollElement = element.tagName === 'BODY' ? window : element\n      this._config        = this._getConfig(config)\n      this._selector      = `${this._config.target} ${Selector.NAV_LINKS},`\n                          + `${this._config.target} ${Selector.LIST_ITEMS},`\n                          + `${this._config.target} ${Selector.DROPDOWN_ITEMS}`\n      this._offsets       = []\n      this._targets       = []\n      this._activeTarget  = null\n      this._scrollHeight  = 0\n\n      $(this._scrollElement).on(Event.SCROLL, (event) => this._process(event))\n\n      this.refresh()\n      this._process()\n    }\n\n\n    // getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    static get Default() {\n      return Default\n    }\n\n\n    // public\n\n    refresh() {\n      const autoMethod = this._scrollElement !== this._scrollElement.window ?\n        OffsetMethod.POSITION : OffsetMethod.OFFSET\n\n      const offsetMethod = this._config.method === 'auto' ?\n        autoMethod : this._config.method\n\n      const offsetBase = offsetMethod === OffsetMethod.POSITION ?\n        this._getScrollTop() : 0\n\n      this._offsets = []\n      this._targets = []\n\n      this._scrollHeight = this._getScrollHeight()\n\n      const targets = $.makeArray($(this._selector))\n\n      targets\n        .map((element) => {\n          let target\n          const targetSelector = Util.getSelectorFromElement(element)\n\n          if (targetSelector) {\n            target = $(targetSelector)[0]\n          }\n\n          if (target) {\n            const targetBCR = target.getBoundingClientRect()\n            if (targetBCR.width || targetBCR.height) {\n              // todo (fat): remove sketch reliance on jQuery position/offset\n              return [\n                $(target)[offsetMethod]().top + offsetBase,\n                targetSelector\n              ]\n            }\n          }\n          return null\n        })\n        .filter((item)  => item)\n        .sort((a, b)    => a[0] - b[0])\n        .forEach((item) => {\n          this._offsets.push(item[0])\n          this._targets.push(item[1])\n        })\n    }\n\n    dispose() {\n      $.removeData(this._element, DATA_KEY)\n      $(this._scrollElement).off(EVENT_KEY)\n\n      this._element       = null\n      this._scrollElement = null\n      this._config        = null\n      this._selector      = null\n      this._offsets       = null\n      this._targets       = null\n      this._activeTarget  = null\n      this._scrollHeight  = null\n    }\n\n\n    // private\n\n    _getConfig(config) {\n      config = $.extend({}, Default, config)\n\n      if (typeof config.target !== 'string') {\n        let id = $(config.target).attr('id')\n        if (!id) {\n          id = Util.getUID(NAME)\n          $(config.target).attr('id', id)\n        }\n        config.target = `#${id}`\n      }\n\n      Util.typeCheckConfig(NAME, config, DefaultType)\n\n      return config\n    }\n\n    _getScrollTop() {\n      return this._scrollElement === window ?\n          this._scrollElement.pageYOffset : this._scrollElement.scrollTop\n    }\n\n    _getScrollHeight() {\n      return this._scrollElement.scrollHeight || Math.max(\n        document.body.scrollHeight,\n        document.documentElement.scrollHeight\n      )\n    }\n\n    _getOffsetHeight() {\n      return this._scrollElement === window ?\n          window.innerHeight : this._scrollElement.getBoundingClientRect().height\n    }\n\n    _process() {\n      const scrollTop    = this._getScrollTop() + this._config.offset\n      const scrollHeight = this._getScrollHeight()\n      const maxScroll    = this._config.offset\n        + scrollHeight\n        - this._getOffsetHeight()\n\n      if (this._scrollHeight !== scrollHeight) {\n        this.refresh()\n      }\n\n      if (scrollTop >= maxScroll) {\n        const target = this._targets[this._targets.length - 1]\n\n        if (this._activeTarget !== target) {\n          this._activate(target)\n        }\n        return\n      }\n\n      if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n        this._activeTarget = null\n        this._clear()\n        return\n      }\n\n      for (let i = this._offsets.length; i--;) {\n        const isActiveTarget = this._activeTarget !== this._targets[i]\n            && scrollTop >= this._offsets[i]\n            && (typeof this._offsets[i + 1] === 'undefined' ||\n                scrollTop < this._offsets[i + 1])\n\n        if (isActiveTarget) {\n          this._activate(this._targets[i])\n        }\n      }\n    }\n\n    _activate(target) {\n      this._activeTarget = target\n\n      this._clear()\n\n      let queries = this._selector.split(',')\n      // eslint-disable-next-line arrow-body-style\n      queries     = queries.map((selector) => {\n        return `${selector}[data-target=\"${target}\"],` +\n               `${selector}[href=\"${target}\"]`\n      })\n\n      const $link = $(queries.join(','))\n\n      if ($link.hasClass(ClassName.DROPDOWN_ITEM)) {\n        $link.closest(Selector.DROPDOWN).find(Selector.DROPDOWN_TOGGLE).addClass(ClassName.ACTIVE)\n        $link.addClass(ClassName.ACTIVE)\n      } else {\n        // Set triggered link as active\n        $link.addClass(ClassName.ACTIVE)\n        // Set triggered links parents as active\n        // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n        $link.parents(Selector.NAV_LIST_GROUP).prev(`${Selector.NAV_LINKS}, ${Selector.LIST_ITEMS}`).addClass(ClassName.ACTIVE)\n        // Handle special case when .nav-link is inside .nav-item\n        $link.parents(Selector.NAV_LIST_GROUP).prev(Selector.NAV_ITEMS).children(Selector.NAV_LINKS).addClass(ClassName.ACTIVE)\n      }\n\n      $(this._scrollElement).trigger(Event.ACTIVATE, {\n        relatedTarget: target\n      })\n    }\n\n    _clear() {\n      $(this._selector).filter(Selector.ACTIVE).removeClass(ClassName.ACTIVE)\n    }\n\n\n    // static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        let data      = $(this).data(DATA_KEY)\n        const _config = typeof config === 'object' && config\n\n        if (!data) {\n          data = new ScrollSpy(this, _config)\n          $(this).data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'string') {\n          if (typeof data[config] === 'undefined') {\n            throw new Error(`No method named \"${config}\"`)\n          }\n          data[config]()\n        }\n      })\n    }\n\n\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(window).on(Event.LOAD_DATA_API, () => {\n    const scrollSpys = $.makeArray($(Selector.DATA_SPY))\n\n    for (let i = scrollSpys.length; i--;) {\n      const $spy = $(scrollSpys[i])\n      ScrollSpy._jQueryInterface.call($spy, $spy.data())\n    }\n  })\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME]             = ScrollSpy._jQueryInterface\n  $.fn[NAME].Constructor = ScrollSpy\n  $.fn[NAME].noConflict  = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return ScrollSpy._jQueryInterface\n  }\n\n  return ScrollSpy\n\n})($)\n\nexport default ScrollSpy\n", "import $ from 'jquery'\nimport Util from './util'\n\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.0.0-beta.2): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Tab = (() => {\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                = 'tab'\n  const VERSION             = '4.0.0-beta.2'\n  const DATA_KEY            = 'bs.tab'\n  const EVENT_KEY           = `.${DATA_KEY}`\n  const DATA_API_KEY        = '.data-api'\n  const JQUERY_NO_CONFLICT  = $.fn[NAME]\n  const TRANSITION_DURATION = 150\n\n  const Event = {\n    HIDE           : `hide${EVENT_KEY}`,\n    HIDDEN         : `hidden${EVENT_KEY}`,\n    SHOW           : `show${EVENT_KEY}`,\n    SHOWN          : `shown${EVENT_KEY}`,\n    CLICK_DATA_API : `click${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  const ClassName = {\n    DROPDOWN_MENU : 'dropdown-menu',\n    ACTIVE        : 'active',\n    DISABLED      : 'disabled',\n    FADE          : 'fade',\n    SHOW          : 'show'\n  }\n\n  const Selector = {\n    DROPDOWN              : '.dropdown',\n    NAV_LIST_GROUP        : '.nav, .list-group',\n    ACTIVE                : '.active',\n    ACTIVE_UL             : '> li > .active',\n    DATA_TOGGLE           : '[data-toggle=\"tab\"], [data-toggle=\"pill\"], [data-toggle=\"list\"]',\n    DROPDOWN_TOGGLE       : '.dropdown-toggle',\n    DROPDOWN_ACTIVE_CHILD : '> .dropdown-menu .active'\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Tab {\n\n    constructor(element) {\n      this._element = element\n    }\n\n\n    // getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n\n    // public\n\n    show() {\n      if (this._element.parentNode &&\n          this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n          $(this._element).hasClass(ClassName.ACTIVE) ||\n          $(this._element).hasClass(ClassName.DISABLED)) {\n        return\n      }\n\n      let target\n      let previous\n      const listElement = $(this._element).closest(Selector.NAV_LIST_GROUP)[0]\n      const selector    = Util.getSelectorFromElement(this._element)\n\n      if (listElement) {\n        const itemSelector = listElement.nodeName === 'UL' ? Selector.ACTIVE_UL : Selector.ACTIVE\n        previous = $.makeArray($(listElement).find(itemSelector))\n        previous = previous[previous.length - 1]\n      }\n\n      const hideEvent = $.Event(Event.HIDE, {\n        relatedTarget: this._element\n      })\n\n      const showEvent = $.Event(Event.SHOW, {\n        relatedTarget: previous\n      })\n\n      if (previous) {\n        $(previous).trigger(hideEvent)\n      }\n\n      $(this._element).trigger(showEvent)\n\n      if (showEvent.isDefaultPrevented() ||\n         hideEvent.isDefaultPrevented()) {\n        return\n      }\n\n      if (selector) {\n        target = $(selector)[0]\n      }\n\n      this._activate(\n        this._element,\n        listElement\n      )\n\n      const complete = () => {\n        const hiddenEvent = $.Event(Event.HIDDEN, {\n          relatedTarget: this._element\n        })\n\n        const shownEvent = $.Event(Event.SHOWN, {\n          relatedTarget: previous\n        })\n\n        $(previous).trigger(hiddenEvent)\n        $(this._element).trigger(shownEvent)\n      }\n\n      if (target) {\n        this._activate(target, target.parentNode, complete)\n      } else {\n        complete()\n      }\n    }\n\n    dispose() {\n      $.removeData(this._element, DATA_KEY)\n      this._element = null\n    }\n\n\n    // private\n\n    _activate(element, container, callback) {\n      let activeElements\n      if (container.nodeName === 'UL') {\n        activeElements = $(container).find(Selector.ACTIVE_UL)\n      } else {\n        activeElements = $(container).children(Selector.ACTIVE)\n      }\n\n      const active          = activeElements[0]\n      const isTransitioning = callback\n        && Util.supportsTransitionEnd()\n        && (active && $(active).hasClass(ClassName.FADE))\n\n      const complete = () => this._transitionComplete(\n        element,\n        active,\n        isTransitioning,\n        callback\n      )\n\n      if (active && isTransitioning) {\n        $(active)\n          .one(Util.TRANSITION_END, complete)\n          .emulateTransitionEnd(TRANSITION_DURATION)\n\n      } else {\n        complete()\n      }\n\n      if (active) {\n        $(active).removeClass(ClassName.SHOW)\n      }\n    }\n\n    _transitionComplete(element, active, isTransitioning, callback) {\n      if (active) {\n        $(active).removeClass(ClassName.ACTIVE)\n\n        const dropdownChild = $(active.parentNode).find(\n          Selector.DROPDOWN_ACTIVE_CHILD\n        )[0]\n\n        if (dropdownChild) {\n          $(dropdownChild).removeClass(ClassName.ACTIVE)\n        }\n\n        if (active.getAttribute('role') === 'tab') {\n          active.setAttribute('aria-selected', false)\n        }\n      }\n\n      $(element).addClass(ClassName.ACTIVE)\n      if (element.getAttribute('role') === 'tab') {\n        element.setAttribute('aria-selected', true)\n      }\n\n      if (isTransitioning) {\n        Util.reflow(element)\n        $(element).addClass(ClassName.SHOW)\n      } else {\n        $(element).removeClass(ClassName.FADE)\n      }\n\n      if (element.parentNode &&\n          $(element.parentNode).hasClass(ClassName.DROPDOWN_MENU)) {\n\n        const dropdownElement = $(element).closest(Selector.DROPDOWN)[0]\n        if (dropdownElement) {\n          $(dropdownElement).find(Selector.DROPDOWN_TOGGLE).addClass(ClassName.ACTIVE)\n        }\n\n        element.setAttribute('aria-expanded', true)\n      }\n\n      if (callback) {\n        callback()\n      }\n    }\n\n\n    // static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        const $this = $(this)\n        let data    = $this.data(DATA_KEY)\n\n        if (!data) {\n          data = new Tab(this)\n          $this.data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'string') {\n          if (typeof data[config] === 'undefined') {\n            throw new Error(`No method named \"${config}\"`)\n          }\n          data[config]()\n        }\n      })\n    }\n\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(document)\n    .on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE, function (event) {\n      event.preventDefault()\n      Tab._jQueryInterface.call($(this), 'show')\n    })\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME]             = Tab._jQueryInterface\n  $.fn[NAME].Constructor = Tab\n  $.fn[NAME].noConflict  = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Tab._jQueryInterface\n  }\n\n  return Tab\n\n})($)\n\nexport default Tab\n", "import $ from 'jquery'\nimport Al<PERSON> from './alert'\nimport <PERSON><PERSON> from './button'\nimport Carousel from './carousel'\nimport Collapse from './collapse'\nimport Dropdown from './dropdown'\nimport Modal from './modal'\nimport Popover from './popover'\nimport Scrollspy from './scrollspy'\nimport Tab from './tab'\nimport Tooltip from './tooltip'\nimport Util from './util'\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.0.0-alpha.6): index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n(() => {\n  if (typeof $ === 'undefined') {\n    throw new Error('Bootstrap\\'s JavaScript requires jQuery. jQuery must be included before Bootstrap\\'s JavaScript.')\n  }\n\n  const version = $.fn.jquery.split(' ')[0].split('.')\n  const minMajor = 1\n  const ltMajor  = 2\n  const minMinor = 9\n  const minPatch = 1\n  const maxMajor = 4\n\n  if (version[0] < ltMajor && version[1] < minMinor || version[0] === minMajor && version[1] === minMinor && version[2] < minPatch || version[0] >= maxMajor) {\n    throw new Error('Bootstrap\\'s JavaScript requires at least jQuery v1.9.1 but less than v4.0.0')\n  }\n})($)\n\nexport {\n  Util,\n  Alert,\n  Button,\n  Carousel,\n  Collapse,\n  Dropdown,\n  Modal,\n  Popover,\n  Scrollspy,\n  Tab,\n  Tooltip\n}\n"]}