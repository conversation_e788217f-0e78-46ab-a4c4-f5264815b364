Vue.component('campaign-edit', {
    props: ['campaign', 'oldInput'],

    data() {
        return {
            timezone: 'US/Eastern',
            startOption: "auto",
            stopOption: "auto",
            startTime: null,
            stopTime: null,
            startTimeTemp: null,
            stopTimeTemp: null,
            isAdvancedOpened: false
        }
    },

    mounted() {
        this.timezone = this.campaign.timezone;
        this.startOption = this.campaign.start_at == null ? 'auto' : 'schedule';
        this.stopOption = this.campaign.stop_at == null ? 'auto' : 'schedule';
        this.startTime = this.startTimeTemp = this.campaign.start_at;
        this.stopTime = this.stopTimeTemp = this.campaign.stop_at;

        if(this.oldInput.archive_autoreplies || this.oldInput.start_at || this.oldInput.stop_colleagues
            || this.oldInput.forward_positive || this.oldInput.archive_bounces || this.oldInput.stop_at
            || this.campaign.archive_autoreplies || this.campaign.start_at || this.campaign.stop_colleagues
            || this.campaign.forward_positive || this.campaign.archive_bounces || this.campaign.stop_at
            || this.oldInput.forward_neutral || this.campaign.forward_neutral  || this.oldInput.webhook_url 
            || this.oldInput.webhook_triggers_replied || this.oldInput.webhook_triggers_positive
            || this.oldInput.webhook_url || this.campaign.webhook_triggers || this.campaign.webhooks
        ) {
            this.toggleAdvanceSettings(true)
        }
    },

    watch: {
        startOption: function (newOption, oldOption) {
            if (newOption === 'auto') {
                this.startTimeTemp = this.startTime;
                this.startTime = null;
            } else {
                this.startTime = this.startTimeTemp;
            }
        },
        stopOption: function (newOption, oldOption) {
            if (newOption === 'auto') {
                this.stopTimeTemp = this.stopTime;
                this.stopTime = null;
            } else {
                this.stopTime = this.stopTimeTemp;
            }
        },
    },

    computed: {

    },

    methods: {
        toggleAdvanceSettings(isOpen) {
            this.isAdvancedOpened = isOpen
            console.log(this.isAdvancedOpened)

            setTimeout(()=>{
                $('[data-toggle="tooltip"]').tooltip();
            }, 500)
        }
    }
});
