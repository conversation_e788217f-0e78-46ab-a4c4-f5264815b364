Vue.component('home', {
    props: ['user', 'new_team_accnt', 'first_promo_wid', 'first_promo_api'],

    mounted() {
    	// if(this.new_team_accnt) {
    	// 	// this.submitLeads();
    	// }

    	/*
        new Chartist.Bar('.dashbCampaignChartx', {
			labels: ['Campaign 1', 'Campaign 2', 'Campaign 3'],
			series: [
				[
					{value:1, className:'bg-red-700'},
					{value:0, className:'bg-red-700'}, 
					{value:5, className:'bg-red-700'}
				],
				[4, 8, 3],
				[
					{value:10, className:'bg-grey-200'},
					{value:15, className:'bg-grey-200'}, 
					{value:23, className:'bg-grey-200'}
				]
			]
		}, {
			seriesBarDistance: 10,
			reverseData: true,
			horizontalBars: true,
			axisY: {
				offset: 70
			}
		});

		new Chartist.Bar('.dashbCampaignChartx', {
			labels: ['Q1', 'Q2', 'Q3', 'Q4'],
			series: [
				[{value:800000, class:'foo'}, 1200000, 1400000, 1300000],
				[200000, 400000, 500000, 300000],
				[100000, 200000, 400000, 600000]
			]
		}, {
			stackBars: true,
			axisY: {
				labelInterpolationFnc: function(value) {
					return (value / 1000) + 'k';
				}
			}
		}).on('draw', function(data) {
			if(data.type === 'bar') {
				data.element.attr({
					style: 'stroke-width: 30px'
				});
			}
		});*/
    },

    methods: {
    	/*
         * Send leads to firstpromoter
         */
    	submitLeads() {
    		// sent the affiliate tracking 
            // let strTID = this.getCookie('_fprom_track');
            // axios.post('https://firstpromoter.com/api/v1/track/signup', {
            //     "wid": this.first_promo_wid,
            //     "email": this.user.email,
            //     // "uid": this.user.stripe_id,
            //     "tid": strTID
            // }, {
            //     headers: {
            //         "x-api-key": this.first_promo_api
            //     }
            // }).then(track_response => {
            //     console.log(track_response)
            // });
    	},

    	/*
         * Utility function to get the cookie value
         */
    	getCookie(name) {
            let cookie = {};
            document.cookie.split(';').forEach(function(el) {
                let [k,v] = el.split('=');
                cookie[k.trim()] = v;
            })
            return cookie[name];
        },
    }

});
