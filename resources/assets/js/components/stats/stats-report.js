Vue.component('stats-report', { 
    props: ['campaigns', 'teams'],

    /**
     * The component's data.
     */
    data() {
        return {
            selectedAllCampaigns: false,
            downloadData: [],
            filters: {
                team_id: 0,
                keyword: ''
            },
            arrCampaigns: [],
            report: { 
                queued: [],
                contacted: [],
                replied: [],
                positive: [],
                neutral: [],
                negative: []
            },
            sorted: { 
                queued: [],
                contacted: [],
                replied: [],
                positive: [],
                neutral: [],
                negative: []
            },
            responses: {
                ampm: [],
                byday: []
            },
            optionTeams: [],

            /* 
             * maxcount is the basis for the length of
             * progressbar which act as a horizontal bar chart
             * in the comparison boxes
             */
            maxCount: {
                queued: 0,
                contacted: 0,
                replied: 0,
                positive: 0,
                neutral: 0,
                negative: 0
            },
 
            amPmPieOptions: { 
                plugins: [
                    Vue.chartist.plugins.tooltip()
                ],
                donut: true,
                donutWidth: 20,
                chartPadding: 10,
                showLabel: false,
            },

            bydayBarOptions: { 
                plugins: [
                    Vue.chartist.plugins.tooltip()
                ],
                axisY: {
                    onlyInteger: true,
                    labelInterpolationFnc: function(value, index) {
                        return index % 2 === 0 ? value : null;
                    }
                },
                chartPadding: {
                    top: 15,
                    right: 5,
                    bottom: -10,
                    left: 0
                }, 
                height:'150px'
            },

            bydayLabels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'], 
        }
    },

    computed: {
        // get total of queued stat
        sumQueued () {
            return this.report.queued.reduce((sum, queued) => {
                return sum + queued.count;
            }, 0)
        },

        // get total of contacted stat
        sumContacted () {
            return this.report.contacted.reduce((sum, contacted) => {
                return sum + contacted.count;
            }, 0)
        },

        // get total of replied stat
        sumReplied () {
            return this.report.replied.reduce((sum, replied) => {
                return sum + replied.count;
            }, 0)
        },

        // get total of positive stat
        sumPositive () {
            return this.report.positive.reduce((sum, positive) => {
                return sum + positive.count;
            }, 0)
        },

        // get total of neutral stat
        sumNeutral () {
            return this.report.neutral.reduce((sum, neutral) => {
                return sum + neutral.count;
            }, 0)
        },

        // get total of negative stat
        sumNegative () {
            return this.report.negative.reduce((sum, negative) => {
                return sum + negative.count;
            }, 0)
        },

        // calculate percentage of positive based on contacted
        percentPositive () { 
            return this.sumContacted && this.sumPositive ? 
                parseFloat(((this.sumPositive/this.sumContacted)*100).toFixed(1)) : 0;
        },

        // calculate percentage of neutral based on contacted
        percentNeutral () { 
            return this.sumContacted && this.sumNeutral ? 
                parseFloat(((this.sumNeutral/this.sumContacted)*100).toFixed(1)) : 0;
        },

        // calculate percentage of negative based on contacted
        percentNegative () { 
            return this.sumContacted && this.sumNegative ? 
                parseFloat(((this.sumNegative/this.sumContacted)*100).toFixed(1)) : 0;
        },

        amPmResponseTotal () {
            return {
                labels: ['AM', 'PM'],
                series: [
                    {
                        meta: 'AM Total Response', 
                        value: this.amResponseTotal
                    },
                    {
                        meta: 'PM Total Response', 
                        value: this.pmResponseTotal
                    }
                ]
            }
        },

        amResponseTotal () {
            return this.responses.ampm.reduce((sum, item) => {
                return sum + item.am;
            }, 0) 
        },

        pmResponseTotal () {
            return this.responses.ampm.reduce((sum, item) => {
                return sum + item.pm;
            }, 0)
        },

        bydayResponseTotal () {
            return {
                labels: this.bydayLabels,
                series: [this.bydayDailyTotal]
            }
        },

        bydayDailyTotal () {
            let totalVals = [];

            this.bydayLabels.forEach((val) => {  
                let dayTotal = this.responses.byday.reduce((sum, day) => {
                    return sum + day[val.toLowerCase()];
                }, 0);

                totalVals.push(dayTotal);
            })

            return totalVals
        }


    },

    mounted() {
        this.bydayDailyTotal;

        // we will be manipulating campaign
        this.arrCampaigns = this.campaigns;

        // create a sorted teams by name
        this.optionTeams = this.teams.sort((a, b) => { 
            return a.name.toLowerCase() > b.name.toLowerCase() ? 1 : -1
        });
    },

    created() {
        
    },

    methods: {
        /*
         * Select all campaigns that are not hidden by the filter
         */
        selectAllCampaigns() {
            this.arrCampaigns.forEach((campaign, index) => {
                campaign.is_selected = false

                // select only campaigns that are not filtered
                if(!campaign.is_hidden) {
                    campaign.is_selected = true;
                    this.addToReport(campaign);
                }
            })

            this.selectedAllCampaigns = true
        },

        /*
         * Deselect and remove campaign from report
         */
        deselectAllCampaigns() {
            this.arrCampaigns.forEach((campaign, index) => {
                campaign.is_selected = false
                this.removeFromReport(campaign);
            })

            this.selectedAllCampaigns = false
        },     

        /*
         * Filter the contacts campaigns by name
         */
        filterCampaignList(shouldReset) {
            if(shouldReset) {
                this.deselectAllCampaigns();
            }

            this.arrCampaigns.forEach((campaign, index) => {
                campaign.is_hidden = campaign.name.toLowerCase().indexOf(this.filters.keyword.toLowerCase()) < 0;

                // override with team filter
                if(parseInt(this.filters.team_id) && this.filters.team_id != campaign.team_id) {
                    campaign.is_hidden = true;
                }

                this.arrCampaigns.splice(index, 1, campaign);
            });
        },

        /*
         * Add or Remove the campaign from the aggreggated status
         */
        toggleCampaign(event, campaign){
            if(event.target.checked) { 
                this.addToReport(campaign);
                campaign.is_selected = true
            } else { 
                this.removeFromReport(campaign);
                campaign.is_selected = false
            } 
        },

        /*
         * compute the width campaign stats
         */
        progressBar(count, max) {
            return 'width:'+Math.ceil((count/max) * 100)+'%';
        }, 

        /*
         * Remove the campaign from the aggregated stats
         */ 
        removeFromReport(campaign) {  
            // loop thru report, find the campaign in each report object and remove
            _.each(this.report, (val, key) => { 
                let campaignIndex = this.report[key].map(item => item.id).indexOf(campaign.id);
                this.report[key].splice(campaignIndex, 1);
            });  

            // remove from ampm response
            let ampmIndex = this.responses.ampm.map(item => item.id).indexOf(campaign.id);
            this.responses.ampm.splice(ampmIndex, 1);

            // remove from byday response
            let bydayIndex = this.responses.byday.map(item => item.id).indexOf(campaign.id);
            this.responses.byday.splice(bydayIndex, 1);

            // remove this campaign from the download report table 
            let downloadIndex = this.downloadData.map(item => item.id).indexOf(campaign.id);
            this.downloadData.splice(downloadIndex, 1);

            // and finally, sort the comparison boxes
            this.sortReport();
        },

        /*
         * fetch the campaign stats and add it to the aggregated stats
         */
        addToReport(campaign) {
            axios.post('/reports/campaign-aggregates/'+campaign.hashid)
                .then(response => {
                    if(response && response.data && response.data.status == 'success'){ 
                        let campaignStats = response.data.report; 
                        let hashInfo = response.data.info;
                        let downloadItem = {
                            id:campaign.id, 
                            name: campaign.name, 
                            team_hashid: hashInfo.team_hashid,
                            campaign_hashid: hashInfo.campaign_hashid
                        };

                        // add reponse to report 
                        _.each(this.report, (val, key) => { 
                            let statCount = campaignStats[key];
                            this.report[key].push({
                                id:campaign.id,
                                name: campaign.name,
                                count: statCount
                            });

                            this.maxCount[key] = statCount > this.maxCount[key] ? statCount : this.maxCount[key]; 
                            downloadItem[key] = statCount;
                        });

                        // add to ampm responses 
                        let campaignAmPm = response.data.responses.ampm; 
                        this.responses.ampm.push({
                            id:campaign.id,
                            name: campaign.name,
                            am: campaignAmPm.am,
                            pm: campaignAmPm.pm,
                            total: campaignAmPm.am + campaignAmPm.pm
                        }); 

                        // add to byday responses 
                        let campaignByday = response.data.responses.byday; 
                        this.responses.byday.push({
                            id:campaign.id,
                            name: campaign.name, 
                            fri: campaignByday.fri, 
                            mon: campaignByday.mon, 
                            sat: campaignByday.sat, 
                            sun: campaignByday.sun, 
                            thu: campaignByday.thu, 
                            tue: campaignByday.tue, 
                            wed: campaignByday.wed 
                        });  

                        // add ampm and byday data to downloadItem
                        downloadItem['am'] = campaignAmPm.am;
                        downloadItem['pm'] = campaignAmPm.pm; 
                        downloadItem['fri'] = campaignByday.fri;
                        downloadItem['mon'] = campaignByday.mon;
                        downloadItem['sat'] = campaignByday.sat;
                        downloadItem['sun'] = campaignByday.sun;
                        downloadItem['thu'] = campaignByday.thu;
                        downloadItem['tue'] = campaignByday.tue;
                        downloadItem['wed'] = campaignByday.wed;  
                        this.downloadData.push(downloadItem); 

                        // sort
                        this.sortReport();
                    }
                })
                .catch(e => {
                    
                });  
        }, 

        sortReport() {
            this.sorted.queued = _.orderBy(this.report.queued, ['count'], ['desc']);  
            this.sorted.contacted = _.orderBy(this.report.contacted, ['count'], ['desc']);  
            this.sorted.replied = _.orderBy(this.report.replied, ['count'], ['desc']);  
            this.sorted.positive = _.orderBy(this.report.positive, ['count'], ['desc']);  
            this.sorted.neutral = _.orderBy(this.report.neutral, ['count'], ['desc']);  
            this.sorted.negative = _.orderBy(this.report.negative, ['count'], ['desc']);  
        },

        /*
         * Export the campaign data into CSV
         */
        downloadReportCSV () { 
            // construct the CSV header
            let csvHeader = ['CAMPAIGN', 'QUEUED', 'CONTACTED', 'REPLIED', 'POSITIVE', 
                'NEUTRAL', 'NEGATIVE', 'AM RESPONSE', 'PM RESPONSE', 'MONDAY', 'TUESDAY', 
                'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY', 'SUNDAY', 'CAMPAIGN ID', 'CLIENT ID'];

            let csvRowData = [];

            // construct the data content
            this.downloadData.forEach((data) => {
                csvRowData.push([
                    data.name, data.queued, data.contacted, data.replied, data.positive, 
                    data.neutral, data.negative, data.am, data.pm, data.mon, data.tue, data.wed, 
                    data.thu, data.fri, data.sat, data.sun, data.campaign_hashid, data.team_hashid
                ]);
            });

            // construct the footer - contains the total of each column
            let csvFooter = [
                'TOTAL', this.sumQueued, this.sumContacted, this.sumReplied, this.sumPositive, 
                this.sumNeutral, this.sumNegative, this.amResponseTotal, this.pmResponseTotal, 
                this.bydayDailyTotal[0], this.bydayDailyTotal[1], this.bydayDailyTotal[2], this.bydayDailyTotal[3], 
                this.bydayDailyTotal[4], this.bydayDailyTotal[5], this.bydayDailyTotal[6], '--', '--'
            ]; 

            $('#reportExportHeader').val(JSON.stringify(csvHeader));
            $('#reportExportData').val(JSON.stringify(csvRowData));
            $('#reportExportFooter').val(JSON.stringify(csvFooter));
            $('#exportReportForm').submit();
 
            // this.convertToCsv('Aggregate Report.csv', csvRowData);
            // this.convertToCsv2('Aggregate Report.csv', csvRowData);
        },
        
        /*
         * Export the campaign data into CSV
         */
        downloadReport () { 
            // construct the CSV header
            let csvRowData = [['CAMPAIGN', 'QUEUED', 'CONTACTED', 'REPLIED', 'POSITIVE',
                'NEUTRAL', 'NEGATIVE', 'AM RESPONSE', 'PM RESPONSE',
                'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY', 'SUNDAY']];

            // construct the data content
            this.downloadData.forEach((data) => {
                csvRowData.push([
                    data.name, data.queued, data.contacted, data.replied, data.positive, 
                    data.neutral, data.negative, data.am, data.pm, 
                    data.mon, data.tue, data.wed, data.thu, data.fri, data.sat, data.sun
                ]);
            });

            // construct the footer - contains the total of each column
            csvRowData.push([
                'TOTAL', this.sumQueued, this.sumContacted, this.sumReplied, this.sumPositive, 
                this.sumNeutral, this.sumNegative, this.amResponseTotal, this.pmResponseTotal, 
                this.bydayDailyTotal[0], this.bydayDailyTotal[1], this.bydayDailyTotal[2], this.bydayDailyTotal[3], 
                this.bydayDailyTotal[4], this.bydayDailyTotal[5], this.bydayDailyTotal[6]
            ]); 
 
            this.convertToCsv('Aggregate Report.csv', csvRowData);
        },

        convertToCsv2(fName, rows) {
            var csv = Papa.unparse(rows, {quotes: true, delimiter:',',quoteChar: '"',escapeChar: '"'});

            var csvData = new Blob(['\ufeff' + csv], {type: 'data:text/csv;charset=utf-8;'});
            var csvURL =  null;
            if (navigator.msSaveBlob)
            {
                csvURL = navigator.msSaveBlob(csvData, 'download.csv');
            }
            else
            {
                csvURL = window.URL.createObjectURL(csvData);
            }

            var tempLink = document.createElement('a');
            tempLink.href = csvURL;
            tempLink.setAttribute('download', 'download.csv');
            tempLink.click();
        },

        /*
         * Utility function to convert an array into CSV
         */
        convertToCsv(fName, rows) {
            // format each rows in array into CSV string
            var csv = '';
            for (var i = 0; i < rows.length; i++) {
                var row = rows[i];
                for (var j = 0; j < row.length; j++) {
                    var val = row[j] === null ? '' : row[j].toString();
                    val = val.replace(',', " ").replace('–', "-");
                    if (j > 0)
                        csv += ',';
                    csv += val;
                }
                csv += '\n';
            }

            // for UTF-16
            var cCode, bArr = [];
            bArr.push(255, 254);
            for (var i = 0; i < csv.length; ++i) {
                cCode = csv.charCodeAt(i);
                bArr.push(cCode & 0xff);
                bArr.push(cCode / 256 >>> 0);
            }

            // HTML5 file API "Blob"
            var blob = new Blob([new Uint8Array(bArr)], { type: 'text/csv;charset=UTF-16LE;' });
            if (navigator.msSaveBlob) {
                navigator.msSaveBlob(blob, fName);
            } else {
                var link = document.createElement("a");
                if (link.download !== undefined) {
                    var url = window.URL.createObjectURL(blob);
                    link.setAttribute("href", url);
                    link.setAttribute("download", fName);
                    link.style.visibility = 'hidden';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    window.URL.revokeObjectURL(url);
                }
            }
        }
    },

});