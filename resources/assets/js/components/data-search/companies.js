import DataItemSlideout from "./DataItemSlideout";
import SaveSearchSlideout from "./SaveSearchSlideout";
import AddToListSlideout from "./lists/AddToListSlideout";
import AddAllToListSlideout from "./lists/AddAllToListSlideout";
import SaveFilterSlideout from "./filters/SaveFilterSlideout";
import FilterListSlideout from "./filters/FilterListSlideout";
import FilterEditSlideout from "./filters/FilterEditSlideout";
import ListAllSlideout from "./lists/ListAllSlideout";
import ListCreateSlideout from "./lists/ListCreateSlideout";
import ListEditSlideout from "./lists/ListEditSlideout";
import ContactImportSlideout from "./ContactImportSlideout";
import FilterMixin from "./filter-mixin";

Vue.component("DataItemSlideout", DataItemSlideout);
Vue.component("SaveSearchSlideout", SaveSearchSlideout);
Vue.component("AddToListSlideout", AddToListSlideout);
Vue.component("AddAllToListSlideout", AddAllToListSlideout);
Vue.component("SaveFilterSlideout", SaveFilterSlideout);
Vue.component("FilterListSlideout", FilterListSlideout);
Vue.component("FilterEditSlideout", FilterEditSlideout);
Vue.component("ListAllSlideout", ListAllSlideout);
Vue.component("ListCreateSlideout", ListCreateSlideout);
Vue.component("ListEditSlideout", ListEditSlideout);
Vue.component("ContactImportSlideout", ContactImportSlideout);

Vue.component("data-companies", {
  mixins: [FilterMixin],
  props: [
    "totalDomains",
    "isSupport",
    "teams",
    "campaigns",
    "displayClientSelect",
  ],
  data() {
    return {
      isLoading: false,
      isFetchingTotals: false,
      hasFilters: false,
      domains: [],
      /*
      cursorDomains: {
        // data: [],
        per_page: 50,
        path: 0,
        next_cursor: null,
        next_page_url: null,
        prev_cursor: null,
        prev_page_url: null,
        total: "0",
        hidden: 0,
      },
      */
      paginatedDomains: {
        // data: [],
        per_page: 50,
        total: 0,
        current_page: 1,
        last_page: 1,
        real_total: 0,
        real_last_page: 1,
      },
      savedDomains: {
        total: 0,
        from: 1,
        current_page: 1,
        last_page: 1,
        per_page: 50,
        to: 50,
      },
      cursorPage: 0,
      maxCursorPage: 100,
      maxCursorData: 2000,
      search_name: "",
      number_of_contacts: 1,
      introJs: null,
      selectedIds: [],
      selectedBtnType: "", // 'access', 'add', 'access_add'
      emailCheckboxAll: false,
      isSelectAll: false,
      selectAllType: "",
      radioSelectNumber: 25,
      showSelectAllPopover: false,
      viewType: "total", // "total", "saved", "new"
      typingTimer: null,
      typingWait: 500,
      keywords: "",
    };
  },

  mounted() {
    this.setCompaniesGridHeight();

    $(window).resize(() => {
      this.setCompaniesGridHeight();
    });

    this.fetchFilters();
    // this.fetchSearches();
    this.fetchAgencyFilters();
    this.fetchAgencyLists();
    this.searchDomains(1);
    this.fetchCreditsAvailable();
    // this.initSearchTour();
  },

  computed: {
    disableFilters() {
      return this.isFetchingFilters || this.isLoading || this.isFetchingTotals;
    },
    estimatedTotal() {
      // return this.cursorDomains.total;
      return this.paginatedDomains.real_total;
    },
    newCount() {
      // return this.cursorDomains.total - this.savedDomains.total;
      const count = this.paginatedDomains.real_total - this.savedDomains.total;
      return count > 0 ? count : 0;
    },
  },

  methods: {
    /*
     * calculate the height of sidebar and company table to fit the viewport
     */
    setCompaniesGridHeight() {
      // logoHeadHeight (60px),
      // navMenuHeight (50px),
      // pageHeaderHeight (72px),
      // content spaces (60)
      // footerHeight (44px),
      let intInboxHt = $(window).height() - 60 - 50 - 72 - 60 - 44;
      // console.log(intInboxHt);

      const pageObject = this.getPaginationData();
      const hasPagination = pageObject.total > pageObject.per_page;

      // pagination (52px)
      if (!hasPagination) {
        intInboxHt = intInboxHt + 52;
      }

      $(".scrollable-container-mailbox").height(intInboxHt);
      $(".columnTreeGrid").height(intInboxHt);

      let sidebarScrollHt = intInboxHt - 97;

      if (!hasPagination) {
        sidebarScrollHt = sidebarScrollHt - 52;
      }

      $(".tabbed-inbox-scrollable-filter").height(sidebarScrollHt);

      $(".columnTreeGrid").animate(
        {
          scrollTop: 0,
        },
        50
      );
    },

    filterByKeywords() {
      clearTimeout(this.typingTimer);

      this.typingTimer = setTimeout(() => {
        this.keywords = $.trim(this.keywords);

        // this.searchTotals();
        this.searchDomains(1);
      }, this.typingWait);
    },

    // initiate fetching of domains
    // based on viewType (or the 3 tab button "Total, New, Saved" filter)
    searchDomains(currentPage, fetchTotals = true) {
      if (this.isLoading) {
        return;
      }

      // skip fetching totals on pagination change
      if (fetchTotals) {
        this.fetchSavedTotal();
        this.searchTotals(null);
      }

      this.uncheckAll();

      // max page is 100
      if (currentPage <= 0 || currentPage > 100) {
        return;
      }

      if (["total", "new"]?.includes(this.viewType)) {
        if (currentPage > this.paginatedDomains.last_page) {
          return;
        }

        this.paginatedDomains.current_page = currentPage;
        this.isLoading = true;

        /*
        this.searchCursorDomains(cursor)
          .then(() => {
            // Code here will run after searchCursorDomains completes
            this.isLoading = false;
            this.setCompaniesGridHeight();
          })
          .catch((error) => {
            // console.error('Search failed:', error);
            this.isLoading = false;
          });
        */

        this.searchPaginatedDomains()
          .then(() => {
            // Code here will run after searchCursorDomains completes
            // this.isLoading = false;
            this.setCompaniesGridHeight();
          })
          .catch((error) => {
            // console.error('Search failed:', error);
            // this.isLoading = false;
          });
      } else {
        if (currentPage > this.savedDomains.last_page) {
          return;
        }

        this.savedDomains.current_page = currentPage;
        this.isLoading = true;

        this.fetchSavedDomains()
          .then(() => {
            // Code here will run after searchCursorDomains completes
            // this.isLoading = false;
            this.setCompaniesGridHeight();
          })
          .catch((error) => {
            // console.error('Search failed:', error);
            // this.isLoading = false;
          });
      }
    },

    // fetch domains that are already saved by the agency
    async fetchSavedDomains() {
      try {
        // Prepare params
        let filterParams = this.getParamFilterIds();
        let params = {
          page: this.savedDomains.current_page,
          list: this.agencyListId,
          keywords: this.keywords,
        };

        params = { ...params, ...filterParams };

        // Set filter flag
        if (Object.keys(params).length > 1) {
          this.hasFilters = true;
          params["filtered"] = 1;
        } else {
          this.hasFilters = false;
          params["filtered"] = 0;
        }

        this.isLoading = true;

        const response = await axios.get("/search/contacts/saved", { params });

        if (response?.data?.status === "success") {
          const newDomains = response?.data?.domains?.data || [];
          this.domains = newDomains;

          // if (this.savedDomains.current_page == 1) {
          //   this.domains = newDomains;
          // } else {
          //   this.domains = this.domains.concat(newDomains);
          // }

          this.savedDomains.last_page = response?.data?.domains?.last_page;
          this.savedDomains.from = response?.data?.domains?.from;
          this.savedDomains.to = response?.data?.domains?.to;

          this.savedDomains.first_page_url =
            response?.data?.domains?.first_page_url;
          this.savedDomains.last_page_url =
            response?.data?.domains?.last_page_url;
          this.savedDomains.next_page_url =
            response?.data?.domains?.next_page_url;
          this.savedDomains.prev_page_url =
            response?.data?.domains?.prev_page_url;
          // savedDomains.total is set by "fetchSavedTotal()"
        }

        this.isLoading = false;
      } catch (error) {
        console.log(error);
        this.isLoading = false;
      }
    },

    // fetch domains that are already saved by the agency
    async searchPaginatedDomains() {
      try {
        this.selectedIds = [];
        this.selectedBtnType = "";

        // Prepare params
        let filterParams = this.getParamFilterIds();
        let params = { ...filterParams };

        if (this.keywords?.length) {
          params["keywords"] = this.keywords;
        }

        // Set filter flag
        if (Object.keys(params).length > 1) {
          this.hasFilters = true;
          params["filtered"] = 1;
        } else {
          this.hasFilters = false;
          params["filtered"] = 0;
        }

        params["page"] = this.paginatedDomains.current_page;
        params["view"] = this.viewType;

        axios
          .get("/search/search-paginated", { params })
          .then((response) => {
            if (
              response &&
              response.data &&
              response.data.status == "success"
            ) {
              this.domains = response.data?.domains;
              this.buildPagination();
            }

            this.isLoading = false;

            setTimeout(() => {
              this.setCompaniesGridHeight();
            }, 200);
          })
          .catch((error) => {
            console.log(error);
            this.isLoading = false;
            this.setCompaniesGridHeight();
          });
      } catch (error) {
        console.log(error);
      }
    },

    // called when pagination item is clicked
    onPageChange(currentPage) {
      this.searchDomains(currentPage, false);
    },

    getPaginationData() {
      if (this.viewType == "saved") {
        return this.savedDomains;
      }

      return this.paginatedDomains;
    },

    buildPagination() {
      if (!this.paginatedDomains.total) {
        this.paginatedDomains.total = this.paginatedDomains.real_total;
      }

      // fix for new tab pagination
      if(this.viewType == "new") {
        this.paginatedDomains.total = this.newCount;
      }

      const lastPage = Math.ceil(
        this.paginatedDomains.total / this.paginatedDomains.per_page
      );

      this.paginatedDomains.last_page = lastPage || 1;

      // reset pagination to max result to 2000
      if (this.paginatedDomains.total > 2000) {
        this.paginatedDomains.last_page = 40;
        this.paginatedDomains.total = 2000;
      }

      // prev page
      if (this.paginatedDomains.current_page > 1) {
        const prevPageNum = this.paginatedDomains.current_page - 1;
        this.paginatedDomains.prev_page_url = "/?page=" + prevPageNum;
      } else {
        this.paginatedDomains.prev_page_url = null;
      }

      // next page
      if (
        this.paginatedDomains.current_page < this.paginatedDomains.last_page
      ) {
        const prevPageNum = this.paginatedDomains.current_page + 1;
        this.paginatedDomains.next_page_url = "/?page=" + prevPageNum;
      } else {
        this.paginatedDomains.next_page_url = null;
      }
    },

    // fetch all domains (cursor style)
    /*
    async searchCursorDomains(cursor, total = 0) {
      // Reset data if this is a new search (no cursor)
      if (!cursor) {
        this.cursorPage = 0;
        this.cursorDomains.hidden = 0;
        this.domains = [];
      } else {
        // Don't proceed if we're already loading or hit the max page limit
        if (
          this.cursorPage > this.maxCursorPage ||
          this.domains.length > this.maxCursorData
        ) {
          return;
        }
      }

      // Prepare params
      let params = { cursor, view: this.viewType };
      let filterParams = this.getParamFilterIds();
      params = { ...params, ...filterParams };

      // Set filter flag
      if (Object.keys(params).length > 1) {
        this.hasFilters = true;
        params["filtered"] = 1;
      } else {
        this.hasFilters = false;
        params["filtered"] = 0;
      }

      try {
        const response = await axios.get("/search/search-cursor", { params });

        if (response?.data?.status === "success") {
          const newDomains = response.data?.domains?.data || [];

          // Append new domains to existing data
          if (this.cursorPage === 0) {
            this.domains = newDomains;
          } else {
            this.domains = this.domains.concat(newDomains);
          }

          // Update pagination data
          this.cursorDomains.next_cursor = response.data?.domains?.next_cursor;
          this.cursorDomains.next_page_url =
            response.data?.domains?.next_page_url;
          this.cursorDomains.path = response.data?.domains?.path;
          this.cursorDomains.per_page = response.data?.domains?.per_page;
          this.cursorDomains.prev_cursor = response.data?.domains?.prev_cursor;
          this.cursorDomains.prev_page_url =
            response.data?.domains?.prev_page_url;
          this.cursorDomains.hidden += response.data?.hidden;

          // Update page counter
          this.cursorPage++;

          // Check if we need to fetch more domains based on just this batch
          const newTotal = total + newDomains.length;
          const needMoreDomains =
            newTotal < 50 &&
            this.cursorDomains.next_cursor &&
            this.cursorPage < this.maxCursorPage &&
            this.domains.length < this.maxCursorData;

          if (needMoreDomains) {
            await new Promise((resolve) => setTimeout(resolve, 100));
            return this.searchCursorDomains(
              this.cursorDomains.next_cursor,
              newTotal
            );
          }

          setTimeout(() => {
            this.setCompaniesGridHeight();
          }, 300);
        }
      } catch (error) {
        // console.error('Error fetching domains:', error);
      }
    },
    */

    // fetch the domain count of "Total" filter
    searchTotals(cursor) {
      // Prepare params
      let params = { cursor };
      let filterParams = this.getParamFilterIds();
      params = { ...params, ...filterParams };

      if (this.keywords) {
        params["keywords"] = this.keywords;
      }

      // Set filter flag
      if (Object.keys(params).length > 1) {
        this.hasFilters = true;
        params["filtered"] = 1;
      } else {
        this.hasFilters = false;
        params["filtered"] = 0;
      }

      params["view"] = this.viewType;

      this.isFetchingTotals = true;
      axios.get("/search/search-total", { params }).then((response) => {
        if (response && response.data && response.data.status == "success") {
          // this.cursorDomains.total = response.data?.total;
          this.paginatedDomains.total = response.data?.total;
          this.paginatedDomains.real_total = response.data?.total || 0;
          this.isFetchingTotals = false;
          this.buildPagination();
        }
      });
    },

    // fetch the domain count of "Saved" filter
    fetchSavedTotal() {
      try {
        // Prepare params
        let filterParams = this.getParamFilterIds();
        let params = {
          list: this.agencyListId,
          keywords: this.keywords,
        };
        params = { ...params, ...filterParams };

        // Set filter flag
        if (Object.keys(params).length > 1) {
          this.hasFilters = true;
          params["filtered"] = 1;
        } else {
          this.hasFilters = false;
          params["filtered"] = 0;
        }

        axios
          .get("/search/contacts/saved-total", { params })
          .then((response) => {
            if (response?.data?.status === "success") {
              this.savedDomains.total = response?.data?.total || 0;

              // rebuild pagination of new tab since "newCount" was updated
              if(this.viewType == "new") {
                this.buildPagination();
              }
            }

            setTimeout(() => {
              this.setCompaniesGridHeight();
            }, 300);
          });
      } catch (error) {
        console.log(error);
      }
    },

    // when clicked the "Saved" button in sidebar
    displaySavedDomains() {
      if (this.viewType == "saved") {
        return;
      }

      this.viewType = "saved";
      this.domains = [];
      this.searchDomains(1);
    },

    // when clicked the "Total" button in sidebar
    displayTotalDomains() {
      if (this.viewType == "total") {
        return;
      }

      this.viewType = "total";
      this.domains = [];

      this.searchDomains(1);
    },

    // when clicked the "New" button in sidebar
    // this will display the difference of total and saved domains
    displayNewDomains() {
      if (this.viewType == "new") {
        return;
      }

      this.viewType = "new";
      this.domains = [];

      this.searchDomains(1);
    },

    // initiate fetching of more domains based on viewType
    /*
    showMoreDomains(cursor) {
      this.isLoading = true;

      if (["total", "new"].includes(this.viewType)) {
        this.searchCursorDomains(cursor)
          .then(() => {
            // Code here will run after searchCursorDomains completes
            // console.log('Search completed!');
            this.isLoading = false;
            this.setCompaniesGridHeight();
          })
          .catch((error) => {
            // console.error('Search failed:', error);
            this.isLoading = false;
          });
      } else {
        this.savedDomains.current_page++;

        this.fetchSavedDomains()
          .then(() => {
            // Code here will run after searchCursorDomains completes
            this.isLoading = false;
            this.setCompaniesGridHeight();
          })
          .catch((error) => {
            // console.error('Search failed:', error);
            this.isLoading = false;
          });
      }
    },
    */

    // trigger search everytime user check other filter checkbox
    filterToggle() {
      this.selectedIds = [];
      this.selectedBtnType = "";
      this.emailCheckboxAll = false;
      this.isSelectAll = false;

      this.updateNumSelected();
      this.searchDomains(1);
    },

    // trigger search everytime user check platform filter checkbox
    platformToggle(ev, platform) {
      this.filterPlatform(ev, platform);
      this.updateNumSelected();
      this.searchDomains(1);
    },

    // trigger search everytime user check plan filter checkbox
    // plan is nested inside of platform filter
    planToggle(ev, platform) {
      this.filterPlan(ev, platform);
      this.updateNumSelected();
      this.searchDomains(1);
    },

    // helper function to determine if display "shopify plus" on platform name
    getDomainPlatformName(domain) {
      if (domain?.plan?.name?.toLowerCase() == "shopify plus") {
        return "Shopify Plus";
      }
      return domain.platform?.nice_name || "Unknown";
    },

    // helper function to display category in the list
    getDomainCategory(domain) {
      if (!domain.categories || domain.categories.length == 0) {
        return "Unknown";
      }

      let category = domain.categories[0].name;
      if (category.toLowerCase() === "none") {
        return "Unknown";
      }

      return category;
    },

    // helper function to get display location of domain
    getDomainLocation(domain) {
      let locationName = domain.country?.code || "unknown";
      if (locationName?.toLowerCase() === "unknown") {
        locationName = "Unknown";
      }

      if (locationName != "unknown" && domain.region) {
        locationName = locationName + ", " + domain.region;
      }

      return locationName;
    },

    // helper function to get display sales of domain
    getDomainSale(domain) {
      if (
        !domain?.estimated_sales?.name ||
        domain?.estimated_sales?.name?.toLowerCase() == "unknown"
      ) {
        return "Unknown";
      }

      return domain?.estimated_sales?.name;
    },

    // helper function to get display employee count of domain
    getDomainEmployeeCount(domain) {
      if (
        !domain?.employee_count?.name ||
        domain?.employee_count?.name?.toLowerCase() == "unknown"
      ) {
        return "Unknown";
      }

      return domain?.employee_count?.name;
    },

    // uncheck all filter checkbox when clicking the "reset filter" button in the sidebar
    resetFilters() {
      this.keywords = "";
      this.clearFilters();
      this.searchDomains(1);
    },

    // reapply filters of selected saved filter
    previousSearchSelected() {
      let prevSearch = this.agency_filters.find(
        (s) => s.id == this.previousSearchId
      );

      if (!prevSearch) {
        return;
      }

      // this.search_name = prevSearch["name"] + " -- copy";
      // this.number_of_contacts = prevSearch["total_contacts"];

      if (prevSearch["filters"]) {
        // First, deselect all filters
        this.clearFilters();

        this.filterTypes.forEach((f) => {
          let ids = prevSearch["filters"][f];

          if (ids?.length) {
            if (f != "domain_languages" && f != "shipping_countries") {
              let numSelected = 0;

              this.filters[f]["options"].forEach((o) => {
                if (ids.includes(o.id)) {
                  o.checked = true;
                  numSelected++;
                }
              });

              this.filters[f]["numSelected"] = numSelected;
            }
          }
        });

        let platformNumSelected = 0;
        let catNumSelected = 0;

        // setup for keywords
        if (prevSearch["filters"]["keywords"]) {
          this.keywords = prevSearch["filters"]["keywords"];
        }

        // setup for category filters
        if (prevSearch["filters"]["categories"]) {
          let catdIds = prevSearch["filters"]["categories"];

          this.filters["categories"]["options"].forEach((c) => {
            let isLvl2Checked = false;
            let isLvl3Checked = false;

            // traverse level2
            if (c.children?.length) {
              c.children.forEach((lvl2) => {
                if (catdIds.includes(lvl2.id)) {
                  isLvl2Checked = true;
                  lvl2.checked = true;
                  catNumSelected++;
                }

                // traverse level3
                if (lvl2.children?.length) {
                  lvl2.children.forEach((lvl3) => {
                    if (catdIds.includes(lvl3.id)) {
                      isLvl3Checked = true;
                      lvl3.checked = true;
                      lvl2.checked = true;
                      catNumSelected++;
                    }
                  });
                }
              });
            }

            if (catdIds.includes(c.id) || isLvl2Checked || isLvl3Checked) {
              c.checked = true;
              catNumSelected++;
            }
          });
        }

        // setup for platform filters
        if (prevSearch["filters"]["platforms"]) {
          let platformIds = prevSearch["filters"]["platforms"];

          this.filters["platforms"]["options"].forEach((p) => {
            if (platformIds.includes(p.id)) {
              p.checked = true;
              platformNumSelected++;
            }
          });
        }

        // setup for plan filters
        if (prevSearch["filters"]["plans"]) {
          let planIds = prevSearch["filters"]["plans"];

          this.filters["platforms"]["options"].forEach((platform) => {
            if (!platform?.plans || !platform?.plans?.length) {
              return;
            }

            let isPlatformChecked = false;

            platform?.plans?.forEach((plan) => {
              if (planIds.includes(plan.id)) {
                isPlatformChecked = true;
                platform.checked = true;
                plan.checked = true;
                platformNumSelected++;
              }
            });

            if (isPlatformChecked) {
              platformNumSelected++;
            }
          });
        }

        this.filters["categories"]["numSelected"] = catNumSelected;
        this.filters["platforms"]["numSelected"] = platformNumSelected;

        this.updateNumSelected();
      }

      this.searchDomains(1);
    },

    // when clicking company name in the table, open slideout to display company data
    // include the contacts if domain is already saved by the agency
    showDomain(domain) {
      axios.get("/search/domain-data/" + domain.id).then((response) => {
        domain["data"] = response.data["domain-data"];
        let merchantName = response.data["merchant-name"] || "";

        const panelHandle = this.$showPanel({
          component: "DataItemSlideout",
          openOn: "right",
          width: 1010,
          hideBg: true,
          disableBgClick: true,
          disableEscClick: true,
          props: {
            domain,
            agencyDomain: response.data["agency-domain"],
            domainCategories: response.data["categories"],
            domainCountry: response.data["country"],
            merchantName: merchantName,
          },
        });

        // Promise returned when panel is closed.
        panelHandle.promise.then((data) => {
          if (data?.contact_email) {
            domain.contact_email = data.contact_email;
            domain.contact_name = data.contact_name;
            domain.contact_position = data.contact_position;
          }
        });
      });
    },

    // display slideout to create new search based on
    // selected domainIds or filters
    showSaveSearch() {
      let filterIds = this.getParamFilterIds();
      let numberOfContacts = this.number_of_contacts;
      let selectedIds = this.selectedIds;

      if (!this.selectAllType && this.selectedIds?.length) {
        numberOfContacts = this.selectedIds.length;
      } else if (this.selectAllType == "all") {
        selectedIds = [];
      }

      const searchPanelHandle = this.$showPanel({
        component: "SaveSearchSlideout",
        openOn: "right",
        width: 700,
        hideBg: true,
        disableBgClick: true,
        disableEscClick: true,
        props: {
          searchName: this.search_name,
          numberOfContacts: numberOfContacts,
          filters: this.filters,
          filterTypes: this.filterTypes,
          filterIds: filterIds,
          keywords: this.keywords,
          teams: this.teams,
          selectedIds: selectedIds,
          selectAllType: this.selectAllType,
          selectedBtnType: this.selectedBtnType,
        },
      });

      // Promise returned when panel is closed.
      searchPanelHandle.promise.then((data) => {
        // this.fetchSearches();
        this.fetchAgencyFilters();
        this.fetchCreditsAvailable();

        if (data?.status == "success") {
          this.uncheckAll();
        }
      });
    },

    initSearchTour() {
      let video =
        '<div class="tooltip-yt-embeded-video"><iframe src="https://www.youtube-nocookie.com/embed/pUIEZVzBvP8?si=tyqbITlRniV5H9R6&amp;controls=0" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe></div>';
      const imgChatgptSort =
        "<div class='tooltip-img-box'><img src='/img/tour/search-create.png'></div>";

      this.introJs = introJs().setOptions({
        steps: [
          {
            intro: "<h3>How to Search for Contacts in Wavo</h3><br/>" + video,
          },
          {
            element: document.querySelector(".tour-filter-box"),
            intro:
              "<h3>Search Filters</h3><p>Select your filters according to the types of merchants you want to target.</p>",
            position: "bottom",
          },
          // {
          //   element: document.querySelector(".tour-morefilter-box"),
          //   intro:
          //     "<h3>More Filters</h3><p>Click here to reveal 12 more filters for detailed targeting.</p>",
          //   position: "bottom",
          // },
          {
            element: document.querySelector(".tour-searchcreate-box"),
            intro:
              "<h3>Start Search</h3><p>Open search form to run a search. Give your search a name, then choose how many leads to find that match your search filters.</p><br/>" +
              imgChatgptSort,
            position: "bottom",
          },
          {
            element: document.querySelector(".tour-preview-box"),
            intro:
              "<h3>Search Preview</h3><p>Preview of the companies you'll find leads at, based on the search filters you selected.</p>",
            position: "top",
          },
          {
            element: document.querySelector(".tour-searchresult-box"),
            intro:
              '<h3>Search Results</h3><p>When your search is done, you can access your contacts on the "Results" tab.</p>',
            position: "bottom",
          },
        ],
        skipLabel: "<i class='wb-close'></i> Close",
        doneLabel: "<i class='fa-check'></i> Done",
        prevLabel: "← Previous",
        hidePrev: true,
        hideNext: true,
        overlayOpacity: 0.5,
        tooltipPosition: "bottom",
      });

      // display the tour if user hasn't viewed it
      let searchTourDone = localStorage.getItem("search_tour_done");
      if (!searchTourDone) {
        this.introJs.start();
      }

      // mark tour as viewed by user
      this.introJs.onexit(function () {
        localStorage.setItem("search_tour_done", true);
      });

      this.introJs.onafterchange(function (targetElement) {});
    },

    displaySearchTour() {
      this.introJs.start();
    },

    // this is the toggle checkbox in the table
    toggleEmailCheckboxAll() {
      if (this.emailCheckboxAll) {
        this.showSelectAllPopover = true;
        this.selectAllType = "number";
        this.radioSelectNumber = 25;
      } else {
        this.uncheckAll();
      }

      // this.setCompaniesGridHeight();
    },

    getSelectAllCount() {
      let allCount = 1000;

      if (
        ["total", "new"].includes(this.viewType) &&
        this.paginatedDomains.total < allCount
      ) {
        allCount = this.paginatedDomains.total;
      } else if (
        this.viewType == "saved" &&
        this.savedDomains.total < allCount
      ) {
        allCount = this.savedDomains.total;
      }

      return allCount;
    },

    // loop on companies and check/select them based on
    // number of contacts to be selected
    checkAll() {
      this.selectedIds = [];
      this.selectedBtnType = "";
      let btnTypes = new Set();

      this.domains?.forEach((domain) => {
        if (domain.agency_domain && domain.agency_domain?.status != "success") {
          domain.isChecked = false;
        } else if (this.selectedIds?.length >= this.number_of_contacts) {
          domain.isChecked = false;
        } else {
          domain.isChecked = true;
          this.selectedIds.push(domain.id);

          const btnType = domain.agency_domain ? "add" : "access";
          btnTypes.add(btnType);
        }
      });

      // convert btnTypes to array
      btnTypes = [...btnTypes];

      if (btnTypes.length == 2) {
        this.selectedBtnType = "access_add";
      } else {
        this.selectedBtnType = btnTypes[0];
      }
    },

    // loop through the companies and uncheck them
    uncheckAll() {
      this.selectedIds = [];
      this.selectedBtnType = "";

      this.domains?.forEach((domain) => {
        domain.isChecked = false;
      });

      this.emailCheckboxAll = false;
      this.isSelectAll = false;
      this.selectAllType = "";
      this.number_of_contacts = 1;
    },

    // this is the toggle checkbox of individual domain
    toggleEmailCheckbox(domain) {
      if (!domain.isChecked) {
        this.emailCheckboxAll = false;
        this.isSelectAll = false;
      }

      this.selectedIds = [];
      this.selectedBtnType = "";
      let btnTypes = new Set();

      this.domains?.forEach((domain) => {
        if (domain.isChecked) {
          this.selectedIds.push(domain.id);

          const btnType = domain.agency_domain ? "add" : "access";
          btnTypes.add(btnType);
        }
      });

      // convert btnTypes to array
      btnTypes = [...btnTypes];

      if (btnTypes.length == 2) {
        this.selectedBtnType = "access_add";
      } else {
        this.selectedBtnType = btnTypes[0];
      }
    },

    // cancel the select all popup
    cancelSelectAll() {
      this.uncheckAll();
      this.showSelectAllPopover = false;
    },

    // apple the select all popup
    applySelectAll() {
      this.showSelectAllPopover = false;

      if (!this.radioSelectNumber) {
        return;
      }

      switch (this.selectAllType) {
        case "number":
          this.number_of_contacts = this.radioSelectNumber;
          break;
        case "page":
          this.number_of_contacts = this.domains?.length;
          break;
        case "all":
          this.number_of_contacts = this.getSelectAllCount();
          break;

        default:
          break;
      }

      this.checkAll();
    },

    /**
     * process On-Demand search
     */
    accessEmail(domain) {
      if (domain.is_fetching) {
        return;
      }

      domain.is_fetching = true;

      axios
        .post("/search/" + domain.id + "/access-email")
        .then((response) => {
          if (response && response.data && response.data.status == "success") {
            domain.contact_email = response.data?.contact?.email;

            domain.agency_domain = {
              id: response.data?.agency_domain?.id || null,
              status: "success",
              list_ids: [],
              contacts_count: response.data?.agency_domain?.contacts_count,
            };

            this.fetchCreditsAvailable();
          } else {
            domain.agency_domain = {
              id: null,
              status: "failed",
              list_ids: [],
            };
          }

          domain.has_agency_domain = true;
          domain.is_fetching = false;
        })
        .catch((error) => {
          domain.is_fetching = false;
          console.log(error);

          if (error.response?.data?.errors) {
            this.errors = error.response.data.errors;

            swal({
              title: "Failed saving contact",
              text: error.response.data?.message,
              icon: "error",
            });
          }
        });
    },

    // trigger when list is selected from the sidebar
    // almost same as displaySavedDomain, except it need to switch
    // to saved view first because only saved domains are listed
    agencyListSelected() {
      this.viewType = "saved";
      this.savedDomains.current_page = 1;
      this.isLoading = true;

      this.fetchSavedTotal();
      this.uncheckAll();
      this.updateNumSelected();

      this.fetchSavedDomains()
        .then(() => {
          // Code here will run after searchCursorDomains completes
          this.isLoading = false;
          this.setCompaniesGridHeight();
        })
        .catch((error) => {
          // console.error('Search failed:', error);
          this.isLoading = false;
        });
    },

    // check if show more is disabled
    /*
    disabledShowMore() {
      if (this.viewType == "saved") {
        return this.savedDomains.current_page >= this.savedDomains.last_page;
      }

      if (["total", "new"].includes(this.viewType)) {
        return (
          !this.cursorDomains.next_cursor ||
          this.domains.length >= this.maxCursorData
        );
      }

      return false;
    },
    */

    // display create new agency filter slideout
    showAddFilterForm() {
      let filterIds = this.getParamFilterIds();

      const searchPanelHandle = this.$showPanel({
        component: "SaveFilterSlideout",
        openOn: "right",
        width: 700,
        hideBg: true,
        disableBgClick: true,
        disableEscClick: true,
        props: {
          filters: this.filters,
          filterTypes: this.filterTypes,
          filterIds: filterIds,
          keywords: this.keywords
        },
      });

      // Promise returned when panel is closed.
      searchPanelHandle.promise.then((data) => {
        // this.fetchSearches();
        this.fetchAgencyFilters();
        this.fetchCreditsAvailable();

        if (data?.status == "success") {
          this.uncheckAll();
        }
      });
    },

    // display all of agency filters slideout
    showAllFilters() {
      const searchPanelHandle = this.$showPanel({
        component: "FilterListSlideout",
        openOn: "right",
        width: 700,
        hideBg: true,
        disableBgClick: true,
        disableEscClick: true,
        props: {
          filters: this.filters,
          filterTypes: this.filterTypes,
        },
      });

      // Promise returned when panel is closed.
      searchPanelHandle.promise.then((data) => {
        this.fetchAgencyFilters();
      });
    },

    // display slideout to import selected
    // companies (saved) to campaigns
    showImportToCampaign() {
      let filterIds = this.getParamFilterIds();
      let selectedIds = [];

      if (this.selectAllType !== "all") {
        this.domains?.forEach((domain) => {
          if (domain.isChecked && domain.agency_domain.current_email_id) {
            selectedIds?.push(domain.agency_domain.current_email_id);
          }
        });
      }

      const searchPanelHandle = this.$showPanel({
        component: "ContactImportSlideout",
        openOn: "right",
        width: 700,
        hideBg: true,
        disableBgClick: true,
        disableEscClick: true,
        props: {
          teams: this.teams,
          campaigns: this.campaigns,
          filterIds: { ...filterIds },
          selectedIds: selectedIds,
          isSelectAll: this.selectAllType === "all",
          displayClientSelect: this.displayClientSelect,
        },
      });

      // Promise returned when panel is closed.
      searchPanelHandle.promise.then((data) => {
        this.fetchAgencyLists();
      });
    },

    // display all of agency lists slideout
    showAllAgencyLists() {
      const searchPanelHandle = this.$showPanel({
        component: "ListAllSlideout",
        openOn: "right",
        width: 700,
        hideBg: true,
        disableBgClick: true,
        disableEscClick: true,
        props: {},
      });

      // Promise returned when panel is closed.
      searchPanelHandle.promise.then((data) => {
        this.fetchAgencyLists();
      });
    },

    // display slideout to create new agency list
    showAddList() {
      const listAddPanelHandle = this.$showPanel({
        component: "ListCreateSlideout",
        openOn: "right",
        width: 700,
        hideBg: true,
        disableBgClick: true,
        disableEscClick: true,
        props: {},
      });

      // Promise returned when panel is closed.
      listAddPanelHandle.promise.then((data) => {
        this.fetchAgencyLists();
      });
    },

    // display slideout to add single domain to multiple lists
    showAddToList(domain) {
      const listPanelHandle = this.$showPanel({
        component: "AddToListSlideout",
        openOn: "right",
        width: 700,
        hideBg: true,
        disableBgClick: true,
        disableEscClick: true,
        props: {
          domain,
        },
      });

      // Promise returned when panel is closed.
      listPanelHandle.promise.then((data) => {
        domain.agency_domain.list_ids = data.list_ids;
      });
    },

    // display slideout to add all selected domains (from saved tab) to multiple lists
    showAddAllToList() {
      let selectedDomainIds = [];

      if (this.selectAllType !== "all") {
        this.domains?.forEach((domain) => {
          if (domain.isChecked && domain.agency_domain) {
            selectedDomainIds?.push(domain.agency_domain.id);
          }
        });
      }

      const searchPanelHandle = this.$showPanel({
        component: "AddAllToListSlideout",
        openOn: "right",
        width: 700,
        hideBg: true,
        disableBgClick: true,
        disableEscClick: true,
        props: {
          agency_domain_ids: selectedDomainIds,
        },
      });

      // Promise returned when panel is closed.
      searchPanelHandle.promise.then((data) => {
        this.searchDomains(1);
      });
    },
  },
});
