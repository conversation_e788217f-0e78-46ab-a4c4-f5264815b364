version: '3'
services:
  app:
    image: wavo-app:v3-dev-local
    build:
      context: ./
      dockerfile: ./docker/dev/Dockerfile
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    ports:
      - '8080:8080'
    environment:
      START_HORIZON: 'false'
      START_FRONTEND: 'true'
    volumes:
      - '.:/var/www/html'
    networks:
      - wavo-dev
    depends_on:
      - redis
      - elasticsearch

  go-verifier:
    image: wavo-go-email-verifier:v1-dev-local
    ports:
      - '8081:8081'
    networks:
      - wavo-dev
    depends_on:
      - app

  emailengine:
    image: andris9/emailengine:v2.32.1
    ports:
      - '3000:3000'
    environment:
      EENGINE_WORKERS: '4'
      EENGINE_REDIS: 'redis://redis:6379/8'
      EENGINE_WORKERS_WEBHOOKS: '1'
      EENGINE_SECRET: 'test12345678'
      EENGINE_HOST: '0.0.0.0'
      EENGINE_LOG_LEVEL: 'warn'
      EENGINE_AUTH: 'staging:test12345678'
    #      - 'EENGINE_PREPARED_TOKEN:"hqJpZNlAYzBhYzNmYjY0MzFhMDg1YmViZDE4OTk3YTQ3ZDRkODllNjAyMTk2MWY2MDNjYWZkZTEzODMwNTNmMDU4MzVlOadjcmVhdGVk1_9p25wAYa9l_6JpcKoxMC4yMC4xMC44rXJlbW90ZUFkZHJlc3OqMTAuMjAuMTAuOKZzY29wZXORoSqrZGVzY3JpcHRpb26tZWRfdGVzdF90b2tlbg"'
    networks:
      - wavo-dev
    depends_on:
      - redis
      - app

  mysql:
    image: 'mysql/mysql-server:8.0'
    ports:
      - '3306:3306'
    environment:
      MYSQL_ROOT_PASSWORD: '${DB_PASSWORD}'
      MYSQL_ROOT_HOST: "%"
      MYSQL_DATABASE: '${DB_DATABASE}'
      MYSQL_USER: '${DB_USERNAME}'
      MYSQL_PASSWORD: '${DB_PASSWORD}'
      MYSQL_ALLOW_EMPTY_PASSWORD: 1
    volumes:
      - 'mysql-data:/var/lib/mysql'
    networks:
      - wavo-dev
    healthcheck:
      test: [ "CMD", "mysqladmin", "ping", "-p${DB_PASSWORD}" ]
      retries: 3
      timeout: 5s

  redis:
    image: 'redis'
    #    ports:
    #      - '6379:6379' enable to access from host
    volumes:
      - 'redis-data:/data'
    networks:
      - wavo-dev
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      retries: 3
      timeout: 5s

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.4.0
    container_name: elasticsearch
    environment:
      - xpack.security.enabled=false
      - discovery.type=single-node
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536
    cap_add:
      - IPC_LOCK
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    networks:
      - wavo-dev
#    ports:
#      - 9200:9200
#      - 9300:9300

networks:
  wavo-dev:
    driver: bridge

volumes:
  mysql-data:
    driver: local
  redis-data:
    driver: local
  elasticsearch-data:
    driver: local
  test-data-1:
    driver: local
