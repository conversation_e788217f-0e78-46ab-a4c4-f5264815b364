{"private": true, "scripts": {"dev": "npm run development", "development": "mix", "watch": "mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "mix watch --hot", "prod": "npm run production", "production": "mix --production"}, "dependencies": {"@popperjs/core": "^2.9.2", "@tinymce/tinymce-vue": "^3.2.2", "@zoon/puphpeteer": "github:zoonru/puphpeteer", "axios": "^1.6.2", "bootstrap": "^3.4.1", "card": "^2.4.0", "chartist-plugin-pointlabels": "^0.0.6", "chartist-plugin-tooltips-updated": "^0.1.3", "font-awesome": "^4.7.0", "jquery": "^3.5.1", "js-cookie": "^2.1.0", "moment": "^2.24.0", "promise": "^7.1.1", "puppeteer": "^20", "sprintf-js": "^1.1.2", "sweetalert": "^2.1.2", "tinymce": "^5.7.1", "underscore": "^1.13.1", "urijs": "^1.19.7", "v-calendar": "2.3.1", "v-tooltip": "^2.0.3", "vue": "^2.6.12", "vue-chartist": "^2.3.1", "vue-form-wizard": "^0.8.2", "vue-tel-input": "^5.15.0", "vue2-dropzone": "^3.5.9"}, "devDependencies": {"cross-env": "^7.0.2", "css-loader": "^3.6.0", "file-loader": "^6.2.0", "laravel-mix": "^6.0.6", "less": "^3.12.2", "less-loader": "^6.2.0", "lodash": "^4.17.19", "postcss": "^8.1.14", "sass": "^1.29.0", "sass-loader": "^11.1.1", "vue-loader": "^15.9.6", "vue-template-compiler": "^2.6.11", "vue2-slideout-panel": "^2.12.0"}}