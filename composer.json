{"name": "laravel/laravel", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "type": "project", "require": {"php": "^8.1", "ext-json": "*", "ext-mbstring": "*", "achillesp/laravel-crud-forms": "~6.0", "babenkoivan/scout-elasticsearch-driver": "dev-master", "doctrine/dbal": "^3.1.4", "dompdf/dompdf": "^2.0", "elasticsearch/elasticsearch": "^7.3", "fakerphp/faker": "^1.21", "google/apiclient": "^2.0", "google/cloud-logging": "^1.18", "guzzlehttp/guzzle": "^7.2", "intervention/image": "^2.5", "jeremykendall/php-domain-parser": "^6.3", "laravel/cashier": "~v14.0", "laravel/framework": "^10.0", "laravel/horizon": "^5.0", "laravel/passport": "^12.0", "laravel/scout": "^9.0", "laravel/socialite": "^5.2", "laravel/spark": "*@dev", "laravel/tinker": "^2.8", "laravel/ui": "^4.0", "league/csv": "^9.15", "maatwebsite/excel": "^3.1", "mews/purifier": "~3.0", "monolog/monolog": "^3.0", "predis/predis": "^2.0.0", "rennokki/laravel-eloquent-query-cache": "^3.4", "socialiteproviders/microsoft-azure": "^4.2", "spatie/laravel-google-cloud-storage": "^2.1.0", "spatie/laravel-schedule-monitor": "^3.4", "symfony/http-client": "^6.2", "symfony/mailgun-mailer": "^6.2", "symfony/postmark-mailer": "^6.2", "twilio/sdk": "^8.3", "vinkla/hashids": "^11.0", "zbateson/mail-mime-parser": "^2.0.0", "zoon/puphpeteer": "~2.1.1"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.0", "barryvdh/laravel-ide-helper": "^2.6", "knuckleswtf/scribe": "^4.0", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^9.0", "spatie/laravel-ignition": "^2.0"}, "autoload": {"files": ["app/helpers.php"], "psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}, "files": ["tests/utilities/functions.php"]}, "extra": {"laravel": {"dont-discover": []}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "repositories": [{"type": "path", "url": "./spark", "symlink": true}, {"type": "git", "url": "https://github.com/CasperLaiTW/scout-elasticsearch-driver"}], "minimum-stability": "stable", "prefer-stable": true}