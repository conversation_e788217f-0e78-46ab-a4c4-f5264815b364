<?php

namespace App;

use App\Traits\Hashidable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use <PERSON><PERSON>\Spark\Billable;
use Illuminate\Support\Arr;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\SoftDeletes;

class Agency extends Model
{
    use Billable, Notifiable, SoftDeletes, Hashidable, HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'domain',
        'email',
        'is_billed',
        'creates_campaigns',
        'bills_customers',
        'logo_url',
        'color',
        'is_linkedin_enabled',
        'is_leadsoft_enabled',
        'has_linkedin_outreach',
        'has_linkedin_recruiter',
        'has_warmup',
        'has_chat_gpt',
        'ai_fields',
        'chat_gpt_key',
        'chat_gpt_model',
        'es_contacts_limit',
        'uses_fresh_es_data',
        'wavo_version',
        'hide_billing',
        'has_ecommerce',
        'billing_interval',
        'billing_next_date',
        'email_account_limit',
        'team_member_limit',
        'credits',
        'locked_credits',
    ];

    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
    protected $hidden = [
        'pm_type',
        'pm_last_four',
        'pm_country',
        'billing_address',
        'billing_address_line_2',
        'billing_city',
        'billing_state',
        'billing_zip',
        'billing_country',
        'extra_billing_information',
        'chat_app_id',
        'chat_gpt_key',
        'chat_gpt_model',
    ];

    protected $casts = [
        'trial_ends_at' => 'datetime',
        'plf_started_at' => 'datetime',
        'billing_next_date' => 'datetime',
    ];

    protected $appends = [
        'hashid'
    ];

    /**
     * Make the agency visible for the current user.
     *
     * @return $this
     */
    public function shouldHaveSelfVisibility()
    {
        return $this->makeVisible([
            'pm_type',
            'pm_last_four',
            'pm_country',
            'billing_address',
            'billing_address_line_2',
            'billing_city',
            'billing_state',
            'billing_zip',
            'billing_country',
            'extra_billing_information',
            'es_contacts_limit',
            'uses_fresh_es_data',
            'wavo_version',
            'has_ecommerce',
            'billing_interval',
            'billing_next_date',
            'email_account_limit',
            'team_member_limit',
            'credits',
            'locked_credits',
            'ai_fields',
        ]);
    }

    /**
     * An agency has many users.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function users()
    {
        return $this->hasMany(User::class);
    }

    /**
     * An agency can have many invitations.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function invitations()
    {
        return $this->hasMany(AgencyInvitation::class);
    }

    /**
     * An agency can have many teams.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function teams()
    {
        return $this->hasMany(Team::class);
    }

    /**
     * Get the owner of the agency.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function owner()
    {
        return $this->belongsTo(Spark::userModel(), 'owner_id');
    }

    /**
     * Get the email accounts of the agency.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function emailAccounts()
    {
        return $this->hasMany(EmailAccount::class);
    }

    /**
     * Get the prospects of the agency.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function prospects()
    {
        return $this->hasMany(Prospect::class);
    }

    /**
     * Get the campaigns of the agency.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function campaigns()
    {
        return $this->hasMany(Campaign::class);
    }

    /**
     * Get the email messages of the agency.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function emailMessages()
    {
        return $this->hasMany(EmailMessage::class);
    }

    public function dailyStats()
    {
        return $this->hasMany(DailyStats::class);
    }

    public function researchProjects()
    {
        return $this->hasMany(ResearchProject::class);
    }

    public function researchWeeks()
    {
        return $this->hasMany(ResearchWeek::class);
    }

    public function invitationTemplate()
    {
        return $this->hasOne(InvitationTemplate::class);
    }

    public function agencyTerms()
    {
        return $this->hasOne(AgencyTerms::class);
    }

    public function linkedinAccounts()
    {
        return $this->hasMany(LinkedinAccount::class);
    }

    public function linkedinSearches()
    {
        return $this->hasMany(LinkedinSearch::class);
    }

    public function creditUsages()
    {
        return $this->hasMany(CreditUsage::class);
    }

    public function stripeUsageRecords()
    {
        return $this->hasMany(StripeUsageRecord::class);
    }

    public function linkedinSearchMonthlyUsages()
    {
        return $this->hasMany(LinkedinSearchMonthlyUsage::class);
    }

    public function linkedinSearchCreditUsages()
    {
        return $this->hasMany(LinkedinSearchCreditUsage::class);
    }

    public function linkedinAccessRequest()
    {
        return $this->hasOne(LinkedinAccessRequest::class);
    }

    public function linkedinThreads()
    {
        return $this->hasMany(LinkedinThread::class);
    }

    public function emailSearchIntegrations()
    {
        return $this->hasMany(EmailSearchIntegration::class);
    }

    public function creditLogs()
    {
        return $this->hasMany(CreditLog::class);
    }

    public function chatGptPromptTemplates()
    {
        return $this->hasMany(ChatgptPromptTemplate::class);
    }

    /**
     * Assign the user that is the owner of the agency.
     *
     * @param $userId
     * @return Agency
     */
    public function assignOwner($userId)
    {
        return $this->forceFill(['owner_id' => $userId])->save();
    }

    /**
     * Get all of the subscriptions for the agency.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function subscriptions()
    {
        return $this->hasMany(AgencySubscription::class, 'agency_id')
            ->orderBy('created_at', 'desc');
    }

    /**
     * Get the available billing plans for the given entity.
     *
     * @return \Illuminate\Support\Collection
     */
    public function availablePlans()
    {
        return Spark::agencyPlans();
    }

    /**
     * Get the current week of this agency's research projects.
     * @return mixed
     */
    public function currentResearchWeek()
    {
        return $this->researchWeeks()->latest()->first();
    }

    /**
     * Update the agency owner's billing address.
     *
     * @param $user
     * @param array $data
     */
    public function updateBillingAddress($user, array $data)
    {
        $user->forceFill([
            'pm_country' => Arr::get($data, 'pm_country'),
            'billing_address' => Arr::get($data, 'address'),
            'billing_address_line_2' => Arr::get($data, 'address_line_2'),
            'billing_city' => Arr::get($data, 'city'),
            'billing_state' => Arr::get($data, 'state'),
            'billing_zip' => Arr::get($data, 'zip'),
            'billing_country' => Arr::get($data, 'country'),
        ])->save();

//        event(new BillingAddressUpdated($user));
    }

    /**
     * Update the agency owner's vat id.
     *
     * @param $user
     * @param $vatId
     */
    public function updateVatId($user, $vatId)
    {
        $user->forceFill(['vat_id' => $vatId])->save();

//        event(new VatIdUpdated($user));
    }

    /**
     * Convert the model instance to an array.
     *
     * @return array
     */
    public function toArray()
    {
        $array = parent::toArray();

        if (! in_array('tax_rate', $this->hidden)) {
            $array['tax_rate'] = $this->taxPercentage();
        }

        return $array;
    }

    public function getDefaultInvitationTemplate()
    {
        $invitationTemplate = $this->invitationTemplate()->first();

        if ($invitationTemplate) {

            return $invitationTemplate;
        }

        return new InvitationTemplate([
            'agency_id' => $this->id,
            'subject' => 'New Invitation from '.$this->name.'!',
            'msg' => '<p>Hi There!</p><p>You have been invited to create a dashboard account! Please click the link below to get started:</p><p><a href="{{INVITE_LINK}}">{{INVITE_LINK}}</a></p><p>See you soon!</p>'
        ]);
    }

    public function isUserDashboard()
    {
        return $this->current_billing_plan === 'user-dashboard' || ($this->is_billed && !empty($this->current_billing_plan) && $this->current_billing_plan !== 'agency-dashboard' && $this->current_billing_plan !== 'agency-dashboard-wavo3');
    }

    public function isAgencyDashboard()
    {
        return $this->current_billing_plan === 'agency-dashboard' || $this->current_billing_plan === 'agency-dashboard-wavo3';
    }

    public function isWavoAgency()
    {
        return $this->domain === config('app.platformUrl') && !$this->current_billing_plan;
    }

    public function onFreePlan()
    {
        return $this->current_billing_plan == 'free' || $this->current_billing_plan == 'free-ec' || $this->current_billing_plan == 'free-ec2';
    }

    public function onPromoPlan()
    {
        return $this->current_billing_plan == config('app.billing.tsunamiECFreeAgencyPriceId');
    }


    /**
     * Helper function to create prompt templates for this agency
     *
     * @param $agency
     *
     */
    public function addGlobalPrompts()
    {
        $globalPrompts = getGlobalPromptTemplate();

        foreach ($globalPrompts as $prompt) {
            $template = ChatgptPromptTemplate::firstOrCreate([
                    'name' => $prompt['name'],
                    'agency_id' => $this->id
                ], [
                    'prompt_name' => $prompt['name'],
                    'system_prompt' => $prompt['system'],
                    'user_prompt' => $prompt['user'],
                ]);
        }
    }
}
