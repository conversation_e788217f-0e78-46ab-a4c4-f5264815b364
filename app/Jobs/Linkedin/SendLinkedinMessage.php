<?php

namespace App\Jobs\Linkedin;

use App\Campaign;
use App\CampaignStage;
use App\Exceptions\LinkedinPuppeteerBrowser\ElementNotFoundException;
use App\Exceptions\LinkedinPuppeteerBrowser\LinkedinAccountException;
use App\Exceptions\LinkedinPuppeteerBrowser\ProxyException;
use App\LinkedinMessage;
use App\LinkedinMessageTemplate;
use App\LinkedinThread;
use App\Prospect;
use App\ProspectActivity;
use App\Schedule;
use App\Services\CampaignSchedulerService;
use App\Services\LinkedinPuppeteerBrowserService;
use App\Services\LinkedinThreadService;
use App\Traits\LogsMessages;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;

/**
 * Send a LinkedIn message to a prospect based on a schedule
 */
class SendLinkedinMessage implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, LogsMessages;

    public $timeout = 0;
    public $tries = 3;

    private $prospect;
    private $schedule;
    private $linkedinAccount;
    private $linkedinMessageTemplate;
    private $messageText;
    private $campaign;
    private $campaignStage;
    private $page;
    private $browserService;
    private $proxy = 'datacenter';
    private $testMode = false;
    private $linkedinThreadService;

    protected const DB_READ_CONNECTION = 'mysql::read';

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Prospect $prospect, Schedule $schedule)
    {
        $this->prospect = $prospect;
        $this->schedule = $schedule;
        $this->logChannel = 'linkedin';
        $this->logPrefix = "SendLinkedinMessage LinkedinProfile of Prospect-{$this->prospect->id}. ";
    }


    public function tags()
    {
        return [
            'linkedin',
            'linkedin-send-message',
            'linkedin-send-message-'.$this->schedule->linkedin_message_template_id,
            'linkedin-account-'.$this->schedule->linkedin_account_id
        ];
    }

    public function middleware()
    {
        return [
            (new WithoutOverlapping("linkedin-account-id:{$this->schedule->linkedin_account_id}"))
                ->shared()
                ->expireAfter(180)
                ->releaseAfter(185),
        ];
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws \Throwable
     */
    public function handle()
    {
        $this->linkedinAccount = $this->schedule->linkedinAccount;

        if (! $this->validatePrerequisites()) {

            return;
        }

        $this->linkedinMessageTemplate = LinkedinMessageTemplate::on(static::DB_READ_CONNECTION)
            ->find($this->schedule->linkedin_message_template_id);
        $this->campaignStage = CampaignStage::on(static::DB_READ_CONNECTION)->find($this->schedule->campaign_stage_id);
        $this->campaign = Campaign::on(static::DB_READ_CONNECTION)->find($this->schedule->campaign_id);
        $this->messageText = $this->linkedinMessageTemplate->parseMessageContent($this->prospect, $this->linkedinAccount);
        $this->linkedinThreadService = new LinkedinThreadService($this->linkedinAccount);

        $this->logInfo('Starting Linkedin Message send');
        $messageSent = $this->sendLinkedinMessage();

        if ($messageSent) {
            $this->logInfo('Message sent. Save to db and update prospect');

            $this->sendingCompleted($this->saveLinkedinMessage()); // This also releases locks.

        } else {
            // No message sent. Release locks and exit.
            $this->releaseLocks();
        }

        if (config('app.linkedin.send')) {
            $this->browserService->closeBrowser();
            sleep(2);
        }
    }

    public function failed(\Throwable $exception)
    {
        $this->logError('Message send failed. '.$exception->getMessage());
        $this->releaseLocks();
    }

    protected function validatePrerequisites()
    {
        if (is_null($this->prospect->linkedin_slug)) {
            $this->logInfo('No valid linkedin slug for this prospect.');
            $this->releaseLocks();

            return false;
        }

        if (! $this->linkedinAccount || $this->linkedinAccount->status != 'ACTIVE') {
            $this->logInfo('No active linkedin account. Stopping message send.');
            $this->releaseLocks();

            return false;
        }

        // Check if the contact is connected // TODO: Do we really need this?
        if (! $this->prospect->linkedin_connect_status == 'ACCEPTED' || ! $this->prospect->status == 'OK') {
            $this->logInfo('Prospect is not ready to receive any linkedin message.');
            $this->releaseLocks();

            return false;
        }

        return true;
    }

    protected function sendLinkedinMessage()
    {
        if (! config('app.linkedin.send')) {
            return true;
        }

        $messageSent = false;

        try {
            $this->browserService = new LinkedinPuppeteerBrowserService($this->linkedinAccount, $this->proxy, $this->testMode);
            $this->page = $this->browserService->launchBrowser();
            // First visit profile to get public url if not done already
            if (is_null($this->prospect->linkedin_public_url) || is_null($this->prospect->linkedin_hash_miniprofile)) {
                $this->page = $this->browserService->visitProfile($this->prospect);
                $this->prospect->refresh();
            }

            // Get this prospect's thread to make sure we have no new messages
            /*
            $threads = $this->browserService->getProspectThreads($this->prospect);
            foreach ($threads as $thread) {
                $this->logInfo('Parse thread: '.$thread['id']);
                $this->linkedinThreadService->forProspect($this->prospect)->parseRawThread($thread);
            }
            */

            /*
            // doesnt work as of 02/20/2024 since the message button doesnt have link to threads page
            $thread = $this->browserService->getProspectThread($this->prospect);
            if ($thread) {
                $this->logInfo('Parse thread: '.$thread['id']);
                $this->linkedinThreadService->forProspect($this->prospect)->parseRawThread($thread);
            }
            */

            $messages = $this->browserService->getProspectMessages($this->prospect);
            if (!empty($messages)) {
                $this->logInfo('sendLinkedinMessage Parse messages: ');
                // $this->linkedinThreadService->forProspect($this->prospect)->parseRawThread($thread);

                // if last message is a reply, then stop. let the GetLinkedinMessages sync/create the thread properly
                $lastMsg = collect($messages)->last();

                if($lastMsg['origin'] == 'prospect') {
                    $this->logInfo('sendLinkedinMessage last message is prospect: '.$lastMsg['body']);
                    return false;
                }
            }

            // If prospect has not already replied, then send message
            $this->prospect->refresh();
            if ($this->prospect->status == 'OK') {
                // temp disable for testing saving message
                $this->page = $this->browserService->sendMessage($this->prospect, $this->messageText);
                $messageSent = true;
            }

        } catch (ProxyException | ElementNotFoundException $e) {
            $this->logError($e->getMessage());

        } catch (LinkedinAccountException $e) {
            $this->logError("Message send stopped. ".$e->getMessage());

        } catch (\Throwable $e) {
            // Propagate unhandled error to retry job.
            $this->logError('Unhandled exception. '.$e->getMessage());
            $this->browserService->closeBrowser();
            sleep(2);

            throw($e);
        }

        return $messageSent;
    }

    protected function saveLinkedinMessage()
    {
        if (! config('app.linkedin.send') || $this->testMode) {
            return $this->saveDemoMessage();
        }

        try {
            $this->prospect->refresh();

            // if message was sent, just get it from the threads page, it should be among the top threads
            $threads = $this->browserService->getProspectThreads($this->prospect);
        } catch (ProxyException | ElementNotFoundException $e) {
            $this->logError($e->getMessage());

        } catch (LinkedinAccountException $e) {
            $this->logError("Error fetching prospect thread. ".$e->getMessage());

        } catch (\Throwable $e) {
            // Propagate unhandled error to retry job.
            $this->logError('Unhandled exception. '.$e->getMessage());

        }

        $oldSentMessageCount = $this->prospect->linkedinMessages()->where('origin', 'self')->count();

        if (! empty($threads)) {
            $thread = $threads[0];
            $this->linkedinThreadService->forProspect($this->prospect)->parseRawThread($thread);
        }

        $newSentMessageCount = $this->prospect->linkedinMessages()->where('origin', 'self')->count();

        if ($newSentMessageCount > $oldSentMessageCount) {
            $linkedinMessage = $this->prospect->linkedinMessages()
                ->where('origin', 'self')
                ->whereNull('linkedin_message_template_id')
                ->orderByDesc('id')
                ->first();
            if ($linkedinMessage) {
                $linkedinMessage->update([
                    'linkedin_message_template_id' => $this->linkedinMessageTemplate->id
                ]);
            } else {
                $this->logError('New message is not yet available.');
                //TODO: Create placeholder messages or dispatch job GetLinkedinMessages with delay?
            }
        }

        return $linkedinMessage ?? null;
    }

    protected function saveDemoMessage()
    {
        $linkedinThread = $this->prospect->linkedinThreads()->latest()->first();

        if (empty($linkedinThread)) {
            $linkedinThread = LinkedinThread::create([
                'linkedin_thread_hash' => 'demo-'.$this->prospect->id.'-'.rand(0,10000),
                'agency_id' => $this->linkedinAccount->agency_id,
                'linkedin_account_id' => $this->linkedinAccount->id,
                'campaign_id' => $this->campaignStage->campaign_id,
                'prospect_id' => $this->prospect->id,
                'thread_date' => now(),
            ]);
        }

        $linkedinMessage = LinkedinMessage::create([
            'agency_id' => $this->linkedinAccount->agency_id,
            'linkedin_account_id' => $this->linkedinAccount->id,
            'campaign_id' => $this->prospect->campaign_id,
            'prospect_id' => $this->prospect->id,
            'linkedin_thread_id' => $linkedinThread->id,
            'linkedin_message_template_id' => $this->linkedinMessageTemplate->id,
            'is_connect_request' => false,
            'linkedin_message_hash' => 'demo-'.$this->prospect->id.'-'.rand(0,10000),
            'from_name' => $this->linkedinAccount->name,
            'to_name' => $this->prospect->first_name . ' ' . $this->prospect->last_name,
            'origin' => 'self',
            'status' => 'OK',
            'message' => $this->messageText,
            'submitted_at' => now(),
        ]);

        return $linkedinMessage;
    }

    protected function sendingCompleted($linkedinMessage)
    {
        $this->linkedinAccount->incrementMessagesSent();

        app('App\Services\CampaignStatsManager')
            ->handleCompletedOutreach($this->linkedinMessageTemplate->fresh(), $this->prospect);

        // Release locks while updating schedule and prospect
        $this->schedule->last_sent_at = now();
        $this->schedule->amount_sent++;
        $this->schedule->queued = false;
        $this->schedule->save();

        $this->prospect->update([
            'scheduled_linkedin_message_template_id' => null,
            'schedule_id' => null,
            'last_contacted' => $linkedinMessage ? $linkedinMessage->submitted_at : now(),
            'timezone' => $this->campaign->timezone,
            'linkedin_messages_sent' => $this->prospect->linkedin_messages_sent + 1,
            'completed_steps' => $this->campaignStage->number,
            'next_step' => $this->campaignStage->number + 1,
            'last_linkedin_step' => $this->campaignStage->number,
        ]);

        // Log Prospect activity
        ProspectActivity::create([
            'prospect_id' => $this->prospect->id,
            'linkedin_message_id' => $linkedinMessage ? $linkedinMessage->id : null,
            'activity' => 'LinkedinMessageSent',
        ]);

        // Wake up next step schedule if available
        $campaignScheduler = new CampaignSchedulerService($this->campaign);
        $campaignScheduler->wakeNextSchedules($this->schedule);
    }

    protected function releaseLocks()
    {
        if (! is_null($this->schedule)) {
            $this->schedule->update(['queued' => false]);
        }

        if (! is_null($this->prospect)) {
            if ($this->prospect->linkedinMessages()->count() == 0) {
                $this->prospect->update([
                    'linkedin_account_id' => null,
                    'scheduled_linkedin_message_template_id' => null,
                    'schedule_id' => null,
                ]);
            } else {
                $this->prospect->update([
                    'scheduled_linkedin_message_template_id' => null,
                    'schedule_id' => null,
                ]);
            }
        }
    }
}
