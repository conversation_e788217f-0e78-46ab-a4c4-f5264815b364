<?php

namespace App\Jobs;

use App\EmailNotification;
use App\LinkedinSearchCreditUsage;
use App\LinkedinSearchMonthlyUsage;
use App\Mail\ErrorNotification;
use App\Role;
use App\Spark;
use App\StripeUsageRecord;
use App\User;
use Dompdf\Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class ReportCreditUsage implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 3;

    public $timeout = 300;
    public $backoff = 30;

    /**
     * @var \App\User
     */
    protected $user;

    /**
     * @var \Laravel\Cashier\Subscription|null
     */
    protected $subscription;

    /**
     * This type of subscription.
     *
     * @var string
     */
    protected static $subscriptionType = 'linkedin-data';

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(User $user)
    {
        $this->user = $user;
        $this->subscription = $this->user->subscription(static::$subscriptionType);
    }

    /**
     * Get the tags that should be assigned to the job.
     *
     * @return array
     */
    public function tags()
    {
        return ['stripe-api', 'db', 'credit-usage', 'credit-usage-'.$this->user->id, 'user-'.$this->user->id];
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $pendingCreditUsage = $this->user->creditUsages()->whereNotNull('update_id')->first();
        if (! $pendingCreditUsage) {
            // No pending update, start job from the beginning.
            if (! $this->setPendingUsages()) {

                return;
            }

            $this->updateBilledUsages($this->reportUsage());

        } else {
            // We have pending usages, so a previous job must have failed midway. Continue from where it stopped.
            $stripeUsageRecord = StripeUsageRecord::where('update_id', $pendingCreditUsage->update_id);
            if ($stripeUsageRecord) {
                // Stripe already updated. Clean up pending usages and update billed usages.
                $this->updateBilledUsages($pendingCreditUsage->update_id);
            } else {
                // No Stripe usage record found. Update Stripe and then clean up.
                $this->updateBilledUsages($this->reportUsage());
            }
        }
    }

    public function failed()
    {

    }

    /**
     * Prepare credits table with pending usages.
     *
     * @return bool
     * @throws \Throwable
     */
    protected function setPendingUsages()
    {
        // Get last row of CreditUsage table
        $creditUsage = $this->user->creditUsages()->where('subscription_id', $this->subscription->id)->latest()->first();

        // Check if the period is still the same or we need a new one
        $stripeSubscription = $this->subscription->asStripeSubscription();
        if ($creditUsage->period_end->timestamp != $stripeSubscription->current_period_end) {
            $creditUsage = $this->user->creditUsages()->create([
                'agency_id' => $this->user->agency_id,
                'subscription_id' => $this->subscription->id,
                'period_start' => $stripeSubscription->current_period_start,
                'period_end' => $stripeSubscription->current_period_end
            ]);
        }

        // Get all LinkedinSearchCreditUsage of user's agency, where credit_usage != billed_usage
        $pendingSearchCreditUsages = LinkedinSearchCreditUsage::where('agency_id', $this->user->agency_id)
            ->where('credit_usage', '!=', 'billed_usage')
            ->get();

        if (! $pendingSearchCreditUsages) {

            return false;
        }

        // There are pending credits to report. Proceed to save the pending amount to local tables.
        // Create a unique update id to link all pending usages with stripe usage record.
        $updateId = Str::uuid();

        $newUsageQuantitySum = $pendingSearchCreditUsages->sum(function ($usage) {
            return $usage->credit_usage - $usage->billed_usage;
        });
        $newCachedUsageQuantitySum = $pendingSearchCreditUsages->sum(function ($usage) {
            return $usage->cached_credit_usage - $usage->cached_billed_usage;
        });


        DB::beginTransaction();

        try {
            // Subscription monthly usage
            $creditUsage->update([
                'pending_usage' => $newUsageQuantitySum,
                'cached_pending_usage' => $newCachedUsageQuantitySum,
                'update_id' => $updateId
            ]);

            $pendingSearchCreditUsages->each(function ($usage) use ($creditUsage, $updateId) {
                $newUsageQuantity = $usage->credit_usage - $usage->billed_usage;
                $newCachedUsageQuantity = $usage->cached_credit_usage - $usage->cached_billed_usage;

                // Li search running usage
                $usage->update([
                    'pending_usage' => $newUsageQuantity,
                    'cached_pending_usage' => $newCachedUsageQuantity,
                    'update_id' => $updateId,
                ]);

                // Li search monthly usage
                $monthlyUsage = $usage->linkedinSearchMonthlyUsages()->where('credit_usage_id', $creditUsage->id)->first();
                if ($monthlyUsage) {
                    $monthlyUsage->update([
                        'pending_usage' => $newUsageQuantity,
                        'cached_pending_usage' => $newCachedUsageQuantity,
                        'update_id' => $updateId,
                    ]);
                } else {
                    $monthlyUsage = $usage->linkedinSearchMonthlyUsages()->create([
                        'user_id' => $this->user->id,
                        'agency_id' => $this->user->agency_id,
                        'linkedin_search_id' => $usage->linkedin_search_id,
                        'credit_usage_id' => $creditUsage->id,
                        'subscription_id' => $this->subscription->id,
                        'pending_usage' => $newUsageQuantity,
                        'cached_pending_usage' => $newCachedUsageQuantity,
                        'update_id' => $updateId
                    ]);
                }
            });

            DB::commit();
        } catch (\Throwable $e) {
            DB::rollback();

            // Throw an exception to re-try job.
            throw($e);
        }

        return true;
    }

    /**
     * Report pending usages to stripe and return the update_id.
     *
     * @return string|null
     * @throws \Exception
     */
    protected function reportUsage()
    {
        // Get pending usage
        $creditUsage = $this->user->creditUsages()
            ->where('subscription_id', $this->subscription->id)
            ->whereNotNull('update_id')
            ->first();

        $meteredPlan = $this->getMeteredPlan();

        if (! $meteredPlan) {
            throw(new \Exception('No metered plan found when trying to report subscription usage'));
        }

        if ($creditUsage && ! StripeUsageRecord::where('update_id', $creditUsage->update_id)->exists()) {
            // This will return a usageRecord or throw an exception (causing job retry from where it stopped).
            $usageRecord = $this->subscription->reportUsageFor($meteredPlan->id, $creditUsage->pending_usage);
            info('Got response from stripe with id:'.$usageRecord->id);
            /*
             * usageRecord:
             * @property string $id Unique identifier for the object.
             * @property string $object String representing the object's type. Objects of the same type share the same value.
             * @property bool $livemode Has the value <code>true</code> if the object exists in live mode or the value <code>false</code> if the object exists in test mode.
             * @property int $quantity The usage quantity for the specified date.
             * @property string $subscription_item The ID of the subscription item this usage record contains data for.
             * @property int $timestamp The timestamp when this usage occurred.
            */
            try {
                $this->user->stripeUsageRecords()->create([
                    'agency_id' => $this->user->agency_id,
                    'stripe_id' => $usageRecord->id,
                    'object' => $usageRecord->object,
                    'quantity' => $usageRecord->quantity,
                    'subscription_id' => $this->subscription->id,
                    'subscription_stripe_id' => $this->subscription->stripe_id,
                    'subscription_item_stripe_id' => $usageRecord->subscription_item,
                    'timestamp' => $usageRecord->timestamp,
                    'update_id' => $creditUsage->update_id,
                ]);
            } catch (\Throwable $e) {
                // Single point of failure (if db write fails after updating usage on Stripe).
                // Notify admin with stripe's usage_record_id and update_id and finish job (don't retry).
                // Admin can manually set reported usage to 0 and let the job re-run.
                Log::error($e->getMessage());
                $message = $e->getMessage() . '<br>' . 'Failed to update db with stripe usage record. Update stripe usage to 0 and re-run job.<br>' . "stripe_usage_record_id: {$usageRecord->id}, update_id: {$creditUsage->update_id}";
                Mail::to(Role::where('name', 'admin')->first()->users)
                    ->send(new ErrorNotification($message, 'Error Reporting Credit Usage to Stripe'));
                EmailNotification::create([
                    'origin' => 'ReportCreditUsage ErrorNotification',
                    'subject' => "Stripe API error",
                    'recipients' => 'admins',
                ]);
            }

            return $creditUsage->update_id;
        }

        return null;
    }

    /**
     * Update local billed usage based on values of pending usage.
     *
     * @throws \Throwable
     */
    protected function updateBilledUsages($updateId)
    {
        if (! $updateId) {

            return;
        }

        $creditUsage = $this->user->creditUsages()->where('update_id', $updateId)->first();
        if (! $creditUsage) {
            throw(new Exception("Unable to find credit usage with update_id $updateId for setting billed_usage."));
        }

        DB::beginTransaction();

        try {
            $creditUsage->update([
                'billed_usage' => $creditUsage->billed_usage + $creditUsage->pending_usage,
                'cached_billed_usage' => $creditUsage->cached_billed_usage + $creditUsage->cached_pending_usage,
                'pending_usage' => 0,
                'cached_pending_usage' => 0,
                'update_id' => null,
            ]);

            LinkedinSearchMonthlyUsage::where('update_id', $updateId)->where('agency_id', $this->user->agency_id)
                ->each(function($usage) {
                    $usage->update([
                        'billed_usage' => $usage->billed_usage + $usage->pending_usage,
                        'cached_billed_usage' => $usage->cached_billed_usage + $usage->cached_pending_usage,
                        'pending_usage' => 0,
                        'cached_pending_usage' => 0,
                        'update_id' => null,
                    ]);
                });

            LinkedinSearchCreditUsage::where('update_id', $updateId)->where('agency_id', $this->user->agency_id)
                ->each(function($usage) {
                    $usage->update([
                        'billed_usage' => $usage->billed_usage + $usage->pending_usage,
                        'cached_billed_usage' => $usage->cached_billed_usage + $usage->cached_pending_usage,
                        'pending_usage' => 0,
                        'cached_pending_usage' => 0,
                        'update_id' => null,
                    ]);
                });

            DB::commit();

        } catch (\Throwable $e) {
            DB::rollBack();

            throw($e);
        }

    }

    /**
     * @return \Laravel\Spark\Plan|null
     */
    protected function getMeteredPlan()
    {
        if ($this->subscription && $this->subscription->valid()) {
            $subscriptionItem = $this->subscription->items->filter(function ($subscriptionItem) {
                $plan = Spark::plans()->where('id', $subscriptionItem->stripe_price)->first();
                return $plan->metered;
            })->first();

            return Spark::plans()->where('id', $subscriptionItem->stripe_price)->first();
        }
    }
}
