<?php

namespace App\Jobs\Email;

use App\Campaign;
use App\EmailAccount;
use App\EmailEngineAccount;
use App\EmailMessage;
use App\EmailMessageTest;
use App\EmailThread;
use App\Events\EmailAccount\SyncError;
use App\IncomingEmailMessage;
use App\Jobs\StopColleagues;
use App\Jobs\SendWebhook;
use App\Jobs\UpdateCampaignStats;
use App\PostponedEEWebhook;
use App\Prospect;
use App\ProspectActivity;
use App\ScheduledProspect;
use App\Services\EmailIntegrationServices\EmailEngineApiService;
use App\Traits\LogsMessages;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
//use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Str;
use ZBateson\MailMimeParser\Message;

class EmailEngineGetMessage implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, LogsMessages;

    public $tries = 12;

    public $timeout = 300;

    public $backoff = 30;

    /**
     * The message id that we get from the Nylas webhook.
     *
     * @var string
     */
    protected $messageId;

    /**
     * This EmailEngine id of the message.
     *
     * @var string
     */
    protected $eeId;

    /**
     * The Nylas Account Id where the message was created.
     *
     * @var string
     */
    protected $emailEngineAccountId;

    /**
     * The Nylas API Service.
     *
     * @var EmailEngineApiService
     */
    protected $emailApi;

    /**
     * The Nylas Account.
     *
     * @var EmailEngineAccount
     */
    protected $emailEngineAccount;

    /**
     * The Email Account.
     *
     * @var EmailAccount
     */
    protected $emailAccount;

    /**
     * The message data array that we get from EmailEngine.
     *
     * @var array
     */
    protected $messageData;

    protected $messageHeaders;

    /**
     * The raw message that we get from Nylas.
     *
     * @var Message
     */
    protected $rawMessage;

    /**
     * The name of the sender.
     *
     * @var string
     */
    protected $sender;

    /**
     * The sender email address of the message.
     *
     * @var string
     */
    protected $senderEmail;

    /**
     * The name of the recipient.
     *
     * @var string
     */
    protected $recipient;

    /**
     * The recipient email address of the message.
     *
     * @var string
     */
    protected $recipientEmail;

    /**
     * The prospect linked to the message.
     *
     * @var Prospect
     */
    protected $prospect;

    /**
     * The saved email message.
     *
     * @var EmailMessage
     */
    protected $emailMessage;

    /**
     * The thread of the message.
     *
     * @var EmailThread
     */
    protected $emailThread;

    protected $webhookData;

    /**
     * The pattern we use for searching with regex for an email address in a string.
     *
     * @var string
     */
    protected const EMAIL_REGEX_PATTERN = '/[a-z0-9_\-\+\.]+@[a-z0-9\-]+\.([a-z0-9\-]+)(?:\.[a-z]+)?/i';

    protected const DB_READ_CONNECTION = 'mysql::read';

    /**
     * Create a new job instance.
     *
     * @param $messageId
     * @param $nylasAccountId
     */
    public function __construct($webhookData)
    {
        $messageId = str_replace(['<', '>'], '', $webhookData['data']['messageId']);

        $this->webhookData = $webhookData;
        $this->webhookData['data']['messageId'] = $messageId;
        $this->messageId = $messageId;
        $this->emailEngineAccountId = $webhookData['account'];
        $this->eeId = $webhookData['data']['id'];

        $this->messageHeaders = isset($webhookData['data']['headers']) ? $webhookData['data']['headers'] : null;

        $this->logChannel = 'emailengine';
        $this->logPrefix = "EmailEngineGetMessage-$this->eeId";
    }

    /**
     * Get the tags that should be assigned to the job.
     *
     * @return array
     */
    public function tags()
    {
        return ['email-api', 'fetch-message', 'ee-id-'.$this->eeId, 'message-'.$this->messageId];
    }


//    public function middleware()
//    {
//        return [
//            (new WithoutOverlapping("emailengine-account-id:{$this->emailEngineAccountId}"))
//                ->expireAfter(300)
//                ->releaseAfter(305),
//        ];
//    }

    /**
     * Catch the "reply" object from deltas and save the message to database.
     *
     * @return void
     */
    public function handle()
    {
        $this->logInfo('Process message created. EmailEngine Account id: '.$this->emailEngineAccountId.'. Message id: '.$this->messageId);

        $this->fixOutlookImapMsg();

        // check if "messageNew" should be parsed
        if (!$this->checkMessageNew()) {

            return;
        }

        if ($this->checkSavedOutlookMsg()) {
            $this->logInfo('EmailEngine already saved Outlook message '.$this->messageId);

            return;
        }

        $existingMessage = EmailMessage::where('message_id', $this->messageId)
            ->whereNotNull('ee_id')->first();

        if ($existingMessage) {
            $this->logInfo('Done! Found existing message '.$this->messageId);

            return;

        } elseif (IncomingEmailMessage::where('message_id', $this->messageId)->exists()) {
            $this->logInfo('Done! Found existing IncomingEmailMessage '.$this->messageId);

            return;
        }

        $this->emailAccount = $this->emailEngineAccount->emailAccount;

        if (!$this->emailAccount->isRunning()) {
            // Save this webhook data to DB and retry job if/when account re-connects.
            PostponedEEWebhook::create([
                'email_account_id' => $this->emailAccount->id,
                'webhook_data' => json_encode($this->webhookData),
            ]);

            return;
        }

        if (!is_null($this->emailAccount->sync_paused_until)) {
            if ($this->emailAccount->sync_paused_until <= now()) {
                $this->emailAccount->sync_paused_until = null;
                $this->emailAccount->save();
            } else {
                $this->logInfo('Account sync paused. Retry later.');
                $diff = $this->emailAccount->sync_paused_until->diffInSeconds(now()) + rand(120, 300); //avoid all jobs running together
                self::dispatch($this->webhookData)
                    ->onQueue('api')
                    ->delay($diff);

                return;
            }
        }

        $this->emailApi = new EmailEngineApiService($this->emailAccount);

        // check if message was sent from wavo and just update the emailMassage
        $selfEmailMsg = EmailMessage::where('message_id', $this->messageId)
            ->where('origin', 'self')
            ->with(['prospect'])
            ->first();
        if (!empty($selfEmailMsg)) {
            $this->logInfo('Done! EmailEngine already saved message sent by Wavo '.$this->messageId);
            $this->webhookData['data']['text'] = array(
                'plain' => $selfEmailMsg->snippet,
                'html' => $selfEmailMsg->message
            );
            $this->messageData = $this->parseMessageData();
            $this->emailApi->saveEmailMessage($this->messageData, $this->messageHeaders, $selfEmailMsg->prospect, 'self', null, null);

            return;
        }

        // fetch message content
        $rawResponse = $this->emailApi->getEmailMessage($this->webhookData['data']['id'], 'EmailEngineGetMessage');

        if ($rawResponse === false) {
            $this->logInfo('Error when fetching message');
            $this->release(600 * $this->job->attempts());

            return;

        } elseif (!is_null($rawResponse) && $rawResponse->getStatusCode() == 200) {
            resetRedisCounter('sync_errors', $this->emailAccount);
            // just replace the empty webhook['data']['text'] with real message from API
            $rawMessage = collect(json_decode($rawResponse->getBody()->getContents(), true));
            if (!isset($rawMessage['text'])) {
                $this->logError('Account id: '.$this->emailEngineAccountId.'. Error Fetching Raw Message. No text');

                return;
            }

            $this->webhookData['data']['text'] = $rawMessage['text'];
            $this->logInfo('Message exists! webhook.data.text Updated.');
        } else {
            $reason = $rawResponse->getReasonPhrase();
            $errorBody = collect(json_decode($rawResponse->getBody()->getContents(), true));

            // if fetching message failed during "syncing" state of emailAccount, retry again later.
            if ($reason == 'Service Unavailable' && !empty($errorBody['state']) && $errorBody['state'] == 'syncing') {
                $this->logInfo('Account syncing. Pause fetching messages');
                $this->emailAccount->sync_paused_until = now()->addMinutes(20);
                $this->emailAccount->save();
                self::dispatch($this->webhookData)
                    ->onQueue('api')
                    ->delay(now()->addSeconds(1300));
                $this->logError('Account id: '.$this->emailEngineAccountId.'. Syncing: '.$this->emailAccount->email_address.', Retry fetching raw message later.');

                return;
            }

            // If other error, release the job (to retry in 5') till last retry and then stop.
            $this->logError('Account id: '.$this->emailEngineAccountId.'. Error Fetching Raw Message : '.$rawResponse->getStatusCode().' '.$reason. ' Retry later.');

            if ($rawResponse->getStatusCode() == 504 || $rawResponse->getStatusCode() == 503) {
                $this->logError('Timeout error. Pause or stop account');
                $this->emailAccount->sync_paused_until = now()->addMinutes(20);
                $this->emailAccount->save();

                incrementRedisCounter('sync_errors', $this->emailAccount);

                // Dispatch event to potentially notify users and stop account with `sync.timeout` error.
                event(new SyncError($this->emailAccount, 'sync.timeout'));

                self::dispatch($this->webhookData)
                    ->onQueue('api')
                    ->delay(1300);

            } elseif ($rawResponse->getStatusCode() == 404) {
                // If we get 404 errors on all job tries, then just exit without failing
                // (if we kept retrying over the tries limit then the job will fail with a generic message "has been attempted too many times or run too long.").
                // We could implicitly fail the job with a custom message, but it's better to avoid filling the failed jobs on such cases after they've failed for 10*8=80 minutes.
                if ($this->job->attempts() == $this->tries) {
                    $this->logError('Failed fetching message (404)');
                } else {
                    $this->release(600 * $this->job->attempts());
                }
            } else {
                $this->release(600);
            }

            return;
        }

        $this->messageData = $this->parseMessageData();

        // If this was a test, do nothing. Already done this in checkMessageNew method
//        $emailMessageTest = EmailMessageTest::where('ee_message_id', $this->messageId)->first();
//
//        if ($emailMessageTest) {
//            $this->checkForConnectionMessage($emailMessageTest);
//            $this->logInfo('Done! Testing message... abort parse.');
//
//            return;
//        }

        if ($this->checkAutoResponse()) {
            $this->logInfo('Done! Auto response processed.');

            return;
        }

        if (!$this->getFromToEmails()) {

            return;
        }

        $this->handleMessage($this->getOrigin());
    }

    /**
     * The job failed to process.
     *
     * @param  Exception  $exception
     */
    public function failed(Exception $exception)
    {
        $this->logError('Failed parsing message.created from EmailEngine: '.$exception->getMessage());
    }

    /**
     * Handle the message based on its origin (self or prospect).
     *
     * @param $origin
     */
    protected function handleMessage($origin)
    {
        /*
        Should we handle only messages with origin='prospect'? As it is now we can also save messages
        that were sent to the prospect outside of the app. But there are cases (possible Nylas bug), where
        we save duplicate messages with different nylasMessageId (once when sending and once from here).
        We can avoid these messages if we only handle here messages with origin != self, but we will lose
        messages created outside of the app if example some client replies through the webmail or outlook.
        */

        // First we try to attach on thread based on EE thread id (this is for gmail provider).
        // If not available, we try to use the prospect we might have found.
        if (!empty($this->messageData['threadId'])) {
            // check for autoforward (positive / neutral) messages because they have
            // same gmail threadID as the original message that was forwarded
            $forwardedStart = '<br><br><div class="quote"><div dir="ltr">---------- Forwarded message ---------<br>';


            if ($origin === 'self' && Str::startsWith($this->messageData['text']['html'], $forwardedStart)) {
                $this->logInfo('Auto Forwarded message: '.$this->messageId);
            } else {
                $this->emailThread = EmailThread::where('ee_thread_id', $this->messageData['threadId'])
                    ->where('email_account_id', $this->emailAccount->id)
                    ->first();
            }
        }

        // if no thread, check "in-reply-to" header if exists in email_messages.message_id
        if (!$this->emailThread) {
            $inReplyTo = $this->getHeaderValue('in-reply-to');
            $inReplyTo = !empty($inReplyTo) ? $inReplyTo[0] : '';

            if ($inReplyTo) {
                $inReplyTo = str_replace(['<', '>'], '', $inReplyTo);
                $inReplyMessage = EmailMessage::where('message_id', $inReplyTo)->with('emailThread')->first();
                if ($inReplyMessage && $inReplyMessage->emailThread) {
                    $this->emailThread = $inReplyMessage->emailThread;
                }
            }
        }

        // if no prospect but has emailThread and origin is self
        // get the prospect from emailThread as fallback
        if (empty($this->prospect) && $this->emailThread && $origin == 'self') {
            $this->prospect = $this->emailThread->prospect;
        }

        if ($origin == 'prospect' && $this->emailThread) {
            $this->prospect = Prospect::on(self::DB_READ_CONNECTION)->with('campaign')->find($this->emailThread->prospect_id);

            // Save message to db.
            $this->emailMessage = $this->emailApi->saveEmailMessage($this->messageData, $this->messageHeaders, $this->prospect, $origin, null, 'REPLIED');
            $this->emailThread->update(['is_read' => false]);
            $this->updateProspectStatus('REPLIED');
            $this->stopColleagues();
            $this->logInfo('Done! Processed reply from prospect: '.$this->prospect->email);

        } elseif ($this->prospect) {
            $this->logInfo('Found prospect id: '.$this->prospect->id);
            // Save message to db.
            $this->emailMessage = $this->emailApi->saveEmailMessage($this->messageData, $this->messageHeaders, $this->prospect, $origin, null, 'REPLIED');
            if ($origin == 'prospect') {
                // This is a reply from a prospect.
                $this->emailThread = $this->emailMessage->emailThread;
                $this->emailThread->update(['is_read' => false]);
                $this->updateProspectStatus('REPLIED');
                $this->stopColleagues();
                $this->logInfo('Done! Processed reply from prospect: '.$this->prospect->email);
            } else {
                $this->logInfo('Done! Processed message related to prospect: '.$this->prospect->email);
            }

        } elseif ($origin == 'prospect') {
            // We have an incoming message, but couldn't find a prospect.
            // Check the message id that might be in the raw headers `references` and `in-reply-to`.
            if ($this->findProspectFromRawHeaders()) {
                $this->emailMessage = $this->emailApi->saveEmailMessage($this->messageData, $this->messageHeaders, $this->prospect, $origin, null, 'REPLIED');
                $this->emailThread = $this->emailMessage->emailThread;
                $this->emailThread->update(['is_read' => false]);
                $this->updateProspectStatus('REPLIED');
                $this->stopColleagues();
                $this->logInfo('Done! Processed reply from prospect: '.$this->prospect->email);
            } else {
                // Not able to match with previous message. Save as incoming message to be manually reviewed.
                $incomingEmailMessage = $this->emailApi->saveIncomingEmailMessage($this->messageData, $this->messageHeaders);
                $this->logInfo('Done! Processed Incoming Email Message');
                ParseIncomingEmailMessage::dispatch($incomingEmailMessage->id, $incomingEmailMessage->ee_id)
                    ->onQueue('default')
                    ->delay(now()->addSeconds(30));
            }
        }
    }

    /**
     * check if microsoft used their own messageId and updated the original messageId we got from EE
     *
     */
    protected function fixOutlookImapMsg()
    {
        $msOrigMsgId = $this->getHeaderValue('x-microsoft-original-message-id');

        if (!empty($msOrigMsgId[0])) {
            $xmsOrigMsgId = str_replace(['<', '>'], '', $msOrigMsgId[0]);

            // check if x-microsoft-original-message-id is in "EmailMessage"
            $xmsMessage = EmailMessage::where('message_id', $xmsOrigMsgId)->first();

            if ($xmsMessage) {
                $xmsMessage->update([
                    'message_id' => $this->messageId,
                    'ee_email_id' => $xmsOrigMsgId
                ]);
                return true;
            }

            // check if x-microsoft-original-message-id is in "EmailMessageTest"
            $xmsMessageTest = EmailMessageTest::where('ee_message_id', $xmsOrigMsgId)->first();

            if ($xmsMessageTest) {
                $xmsMessageTest->update(['ee_message_id' => $this->messageId]);
            }
        }
    }

    /**
     * check if this outlook imap message is already saved
     * under it's new Outlook message ID
     *
     */
    protected function checkSavedOutlookMsg()
    {
        $xmsMessage = EmailMessage::where('ee_email_id', $this->messageId)->first();

        if (!empty($xmsMessage)) {
            return true;
        }

        return false;
    }

    /**
     * Find if the message origin is self or prospect.
     *
     * Logic Matrix:
     * FROM         TO          CC          ORIGIN
     * ---------------------------------------------
     * account                              self
     * prospect     account                 prospect
     * prospect     unknown     account     prospect
     * unknown      prospect    account     self
     * unknown      account     prospect    prospect
     * unknown      account                 prospect
     * unknown      unknown     account     prospect
     *
     * *Unique case if the account is not a from/to/cc (mailbox sync issue)
     *
     * @return string
     */
    protected function getOrigin()
    {
        $accountEmails = [strtolower($this->emailAccount->email_address)];
        if ($this->emailAccount->alias_enabled) {
            $accountEmails[] = strtolower($this->emailAccount->alias_email_address);
        }

        $recipients = array_merge($this->messageData['to'], $this->messageData['cc']);

        // If the account is not a sender or recipient, don't search for a prospect.
        // This needs to be checked just in case we have old messages from a previous account using this mailbox.
        if (!in_array(strtolower($this->senderEmail), $accountEmails)) {
            $matches = collect($recipients)->filter(function ($recipient) use ($accountEmails) {
                return in_array(strtolower($recipient['address']), $accountEmails);
            })->count();

            if ($matches == 0) {
                $origin = 'self';

                return $origin;
            }
        }

        // If the sender is the account, then origin is self. Find prospect in recipients.
        if (in_array(strtolower($this->senderEmail), $accountEmails)) {
            $origin = 'self';

            // Find prospect in recipients
            foreach ($recipients as $recipient) {
                $this->prospect = $this->getProspectByEmail($recipient['address']);
                if (!is_null($this->prospect)) {
                    break;
                }
            }

            return $origin; //self
        }

        // If the sender is a prospect, then origin is prospect
        $this->prospect = $this->getProspectByEmail($this->senderEmail);

        if (!is_null($this->prospect)) {
            $origin = 'prospect';

            return $origin;
        }

        // Sender was neither the account or a prospect.
        // Search all recipient emails for account email and/or prospect.
        $prospectRecipient = false;
        $accountRecipient = false;
        foreach ($recipients as $recipient) {
            if (in_array(strtolower($recipient['address']), $accountEmails)) {
                $accountRecipient = true;
            } elseif (!$prospectRecipient) { // use elseif to avoid re-doing the query if we've already found the prospect.
                $this->prospect = $this->getProspectByEmail($recipient['address']);
                if (!is_null($this->prospect)) {
                    $prospectRecipient = true;
                }
            }
        }

        if ($prospectRecipient && $accountRecipient) {
            // Both account and prospect found among the recipients. Check domain of sender to choose origin.
            if (Str::after(strtolower($this->senderEmail), '@') == Str::after(strtolower($this->prospect->email), '@')) {
                $origin = 'prospect';

                return $origin;
            } else {
                foreach ($accountEmails as $email) {
                    if (Str::after(strtolower($this->senderEmail), '@') == Str::after(strtolower($email), '@')) {
                        $origin = 'self';

                        return $origin;
                    }
                }
            }
        } elseif ($prospectRecipient) {
            // If the recipient is a prospect, even if the sender was not the account, set origin as self.
            $origin = 'self';

            return $origin;
        } elseif ($accountRecipient) {
            // If the recipient is the account, but the sender was not a prospect (would catch before),
            // set origin as prospect, regardless of if we have found a prospect email.
            $origin = 'prospect';

            return $origin;
        }

        // If we have not found any of the above but we still received this email (so account was either TO or CC),
        // then set origin as prospect;
        $origin = 'prospect';

        return $origin;
    }

    /**
     * Find a prospect based on an email address.
     * Choose the prospect that's the latest created one having email messages,
     * among the campaigns of the email account that received the message we're parsing.
     * @param $email
     * @return mixed
     */
    protected function getProspectByEmail($email)
    {
        $campaignIds = $this->emailAccount->setConnection(self::DB_READ_CONNECTION)->allCampaigns()->pluck('id');

        $prospects = Prospect::on(self::DB_READ_CONNECTION)
            ->whereIn('campaign_id', $campaignIds)
            ->withCount('emailMessages')
            ->where('email', $email)
            ->orderBy('id', 'desc')
            ->get();

        // TODO: We are sorting prospects by their id. The correct process would be to sort them by latest message.
        // TODO: Fix this after upgrading to Laravel 8 that supports withMax('emailMessages','submitted_at').

        return $prospects->where('email_messages_count', '>', 0)->first();
    }

    /**
     * Parse new message data from EmailEngine webhook data.
     * No need to query EE as message data is already in the webhookData
     * and message text is already fetched with rawMessage and merged in webhookData
     *
     */
    protected function parseMessageData()
    {
        $defaultAttributes = [
            'date' => Carbon::now(),
            'from' => [
                'name' => '',
                'address' => '',
            ],
            'to' => [],
            'cc' => [],
            'subject' => '',
            'text' => [
                'plain' => '',
                'html' => '',
            ],
        ];
        $messageData = array_merge($defaultAttributes, $this->webhookData['data']);

        // We need to re-merge nested arrays, because array_merge is not deep.
        $messageData['from'] = array_merge($defaultAttributes['from'], $messageData['from']);
        $messageData['text'] = array_merge($defaultAttributes['text'], $messageData['text']);

        // If we have no recipient as 'to', but we have recipients as 'cc', fill the 'to' field
        if (empty($messageData['to']) && !empty($messageData['cc'])) {
            // If we have cc but not to
            $messageData['to'] = [
                [
                    'name' => $messageData['cc'][0]['name'],
                    'address' => $messageData['cc'][0]['address'],
                ]
            ];
        }

        return $messageData;
    }

    /**
     * Check if a message is an auto-response and handle accordingly.
     *
     * @return bool
     */
    protected function checkAutoResponse()
    {
        // TODO EE: yahoo outlook bounce detection
        if (!empty($this->messageData['from']['address'])) {
            // Bounced message handling for outlook.
            if (in_array($this->emailAccount->email_server_type, ['eas', 'ews', 'office365']) && $this->detectBounceFromOutlook()) {
                return true;
            }
        }

        // Parse raw response in case we have an auto reply.
        // Header fields that are used in auto replies.
        $autoSubmitted = $this->getHeaderValue('auto-submitted');
        $failedEmail = $this->getHeaderValue('x-failed-recipients');
        $precedence = $this->getHeaderValue('x-precedence');

        if ($failedEmail && $autoSubmitted && in_array('auto-replied', $autoSubmitted) &&
            $this->processBounced($failedEmail[0])
        ) {
            return true;

        } elseif ($autoSubmitted && (in_array('auto-replied', $autoSubmitted)) &&
            Str::contains(strtolower($this->messageData['from']['address']), ['postmaster', 'mailer-daemon']) &&
            $this->processBouncedFromText()
        ) {

            return true;

        } elseif (
            (
                ($autoSubmitted && (in_array('auto-replied', $autoSubmitted) || in_array('auto-generated', $autoSubmitted))) ||
                ($precedence && $precedence == 'auto_reply') ||
                (Str::contains(strtolower($this->messageData['subject']), ['automatic reply', 'autoresponse']))
            ) && $this->processAutoResponse()
        ) {

            return true;

        } elseif ($this->processBouncedFromText()) {

            return true;
        } else {

            return $this->processRawBounced();
        }
    }

    public function getHeaderValue($headerKey)
    {
        if (!$this->messageHeaders) {
            return null;
        }

        if ($this->messageHeaders &&
            isset($this->messageHeaders[$headerKey]) &&
            @count($this->messageHeaders[$headerKey])
        ) {
            return $this->messageHeaders[$headerKey];
        }

        return null;
    }

    /**
     * Handle a bounced message reply.
     *
     * @param  $message
     * @param  null  $email
     * @return bool
     */
    protected function processBounced($email = null)
    {
        if (null === $email) {
            $email = $this->getHeaderValue('from');
        }

        $this->prospect = $this->getProspectByEmail($email);

        if ($this->prospect) {
            $this->emailMessage = $this->saveBouncedMessage();

            if ($this->emailMessage) {
                $this->emailThread = $this->emailMessage->emailthread;
                $this->emailThread->update(['is_read' => false]);
                $this->updateProspectStatus('BOUNCED');
                $this->archiveMessage('BOUNCED');

                return true;
            }
        }

        return false;
    }

    /**
     * Handle an auto response message reply.
     *
     * @return bool
     */
    protected function processAutoResponse()
    {
        if (empty($this->messageData['from']['address']) || $this->messageData['from']['address'] == '<EMAIL>') {
            // We need to find data from raw message
            return $this->detectIncompleteDeliveryFromGmail();
        } else {
            // First we try to attach on thread based on nylas thread id.
            // If not available, we try to find the prospect.
            if (!empty($this->messageData['threadId'])) {
                $this->emailThread = EmailThread::where('ee_thread_id', $this->messageData['threadId'])
                    ->where('email_account_id', $this->emailAccount->id)
                    ->first();
            }

            // if no thread, check "in-reply-to" header if exists in email_messages.message_id
            if (!$this->emailThread) {
                $inReplyTo = $this->getHeaderValue('in-reply-to');
                $inReplyTo = !empty($inReplyTo) ? $inReplyTo[0] : '';

                if ($inReplyTo) {
                    $inReplyTo = str_replace(['<', '>'], '', $inReplyTo);
                    $inReplyMessage = EmailMessage::where('message_id', $inReplyTo)->with('emailThread')->first();
                    if ($inReplyMessage && $inReplyMessage->emailThread) {
                        $this->emailThread = $inReplyMessage->emailThread;
                    }
                }
            }

            if ($this->emailThread) {
                $this->prospect = Prospect::on(self::DB_READ_CONNECTION)->find($this->emailThread->prospect_id);
            } else {
                $this->prospect = $this->getProspectByEmail($this->messageData['from']['address']);
            }

            if ($this->prospect) {
                if (empty($this->messageData['to'])) {
                    $this->messageData['to'][0] = [
                        'name' => $this->emailEngineAccount->name,
                        'address' => $this->emailEngineAccount->email_address
                    ];
                }
                $this->emailMessage = $this->emailApi->saveEmailMessage($this->messageData, $this->messageHeaders, $this->prospect, 'prospect', null, 'AUTOREPLIED');
                $this->emailThread = $this->emailMessage->emailThread;
                $this->emailThread->update(['is_read' => false]);
                $this->updateProspectStatus('AUTOREPLIED');
                $this->archiveMessage('AUTOREPLIED');

                return true;
            }
        }

        return false;
    }

    /**
     * Detect if a message was bounced when using Yahoo as provider and update the prospect and thread accordingly.
     *
     * @return bool
     */
    protected function detectBounceFromOutlook()
    {
        $originators = [
            '<EMAIL>',
            'mailer-daemon',
            'onmicrosoft.com'
        ];
        $subjectWords = [
            'undeliverable',
            'undelivered',
            'mail delivery failed'
        ];

        if (Str::contains(strtolower($this->messageData['from']['address']), $originators) &&
            Str::contains(strtolower($this->messageData['subject']), $subjectWords)
        ) {
            $inReplyTo = $this->getHeaderValue('in-reply-to');
            $inReplyTo = !empty($inReplyTo) ? $inReplyTo[0] : '';

            if ($inReplyTo) {
                $inReplyTo = str_replace(['<', '>'], '', $inReplyTo);
                $inReplyMessage = EmailMessage::where('message_id', $inReplyTo)->with('emailThread')->first();
                if ($inReplyMessage && $inReplyMessage->emailThread) {
                    $this->emailThread = $inReplyMessage->emailThread;
                }
            }

            if ($this->emailThread) {
                $this->processBouncedThread();

                return true;
            }

            if ($this->scanTextForEmailAndProcessBounced()) {

                return true;
            }
        }

        return false;
    }

    /**
     * Detect if a message was bounced when using Outlook as provider and update the prospect and thread accordingly.
     *
     * @return bool
     */
    protected function detectIncompleteDeliveryFromGmail()
    {
        // TODO EE
    }

    /**
     * Handle a bounced message reply using the raw message headers.
     *
     * @return bool Whether this was a bounced message or not.
     */
    protected function processRawBounced()
    {
        $sender = $this->getHeaderValue('from');
        $sender = $sender ? strtolower($sender[0]) : '';

        $subject = $this->getHeaderValue('subject');
        $subject = $subject ? strtolower($subject[0]) : '';

        if (
            (Str::contains($sender, 'postmaster') && Str::contains($subject, 'undeliverable')) ||
            (Str::contains($sender, 'mailer-daemon') && Str::contains($subject, 'delivery status notification'))
        ) {
            // Try to find referenced email
            $references = $this->getHeaderValue('references');
            $references = !empty($references) ? $references[0] : '';

            if (!empty($references) && preg_match_all(self::EMAIL_REGEX_PATTERN, $references, $matches) > 0) {
                // Referenced email example: <EMAIL>
                // Example message id: e6oyt80af8gyuoih637slsdfz
                $referencedEmail = $matches[0][0];
                $referencedEmailId = preg_replace('/-0@[a-z0-9\-]+\.([a-z]+)(?:\.[a-z]+)?/i', '', $referencedEmail);
                $lastEmailMessage = EmailMessage::where('message_id', $referencedEmailId)->first();

                if ($lastEmailMessage) {
                    $this->processBounced($lastEmailMessage->to);

                    return true;
                }
            }

            // Try to find if there is an email address in the body and use this also.
            if ($this->scanTextForEmailAndProcessBounced()) {

                return true;
            }

            return true;
        }

        return false; // Not 'undeliverable' in subject and 'postmaster' in from.
    }


    /**
     * Test an incoming message for being bounced, based on specific phrases in the subject or snippet.
     *
     * @return bool
     */
    protected function processBouncedFromText()
    {
        // Search for 'delivery failed', 'undeliverable' etc;
        $phrases = [
            'undeliverable',
            'undelivered',
            'delivery failed',
            'delivery has failed',
            'message delivery failure',
            'permanent fatal errors',
            'permanent error',
            'unknown user',
        ];

        if (
            Str::contains(strtolower($this->messageData['subject']), $phrases) ||
            Str::contains(strtolower($this->messageData['text']['plain']), $phrases) ||
            Str::contains(strtolower($this->messageData['text']['html']), $phrases)
        ) {
            if ($this->scanTextForEmailAndProcessBounced()) {

                return true;
            }

            if (!empty($this->messageData['threadId'])) {
                $this->emailThread = EmailThread::where('ee_thread_id', $this->messageData['threadId'])
                    ->where('email_account_id', $this->emailAccount->id)
                    ->first();

                if ($this->emailThread) {
                    $this->processBouncedThread();

                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Check that we have send and receive email address.
     *
     * @return bool
     */
    protected function getFromToEmails()
    {
        // Check that we have send and receive email address.
        if (empty($this->messageData['from']['address'])) {
            // TODO: extract email "codie paja <<EMAIL>>"
            $headerFrom = $this->getHeaderValue('from');
            $headerFrom = $headerFrom ? $headerFrom[0] : null;

            $countFrom = preg_match_all(self::EMAIL_REGEX_PATTERN, $headerFrom, $matchesFrom);
            if ($countFrom > 0 && filter_var($matchesFrom[0][0], FILTER_VALIDATE_EMAIL)) {
                $headerFrom = $matchesFrom[0][0];
            } else {
                $headerFrom = '';
            }

            $this->senderEmail = $headerFrom;
            $this->sender = '';

            $this->messageData['from'] = [
                'name' => $this->sender,
                'address' => $this->senderEmail
            ];
        } else {
            $this->senderEmail = $this->messageData['from']['address'];
            $this->sender = $this->messageData['from']['name'];
        }

        if (empty($this->messageData['to'])) {
            $headerTo = $this->getHeaderValue('to');
            $headerTo = $headerTo ? $headerTo[0] : null;

            $countTo = preg_match_all(self::EMAIL_REGEX_PATTERN, $headerTo, $matchesTo);
            if ($countTo > 0 && filter_var($matchesTo[0][0], FILTER_VALIDATE_EMAIL)) {
                $headerTo = $matchesTo[0][0];
            } else {
                $headerTo = '';
            }

            $this->recipientEmail = $headerTo;
            $this->recipient = '';

            $this->messageData['to'][0] = [
                'name' => $this->recipient,
                'address' => $this->recipientEmail
            ];
        } else {
            $this->recipientEmail = $this->messageData['to'][0]['address'];
            $this->recipient = $this->messageData['to'][0]['name'];
        }

        if (empty($this->senderEmail) || empty($this->recipientEmail)) {
            $this->logError('Message '.$this->messageId.' missing sender or recipient');

            return false;
        }

        return true;
    }

    /**
     * Handle a bounced reply to a known thread.
     *
     * @param  array  $message
     */
    protected function processBouncedThread()
    {
        $this->prospect = $this->emailThread->prospect;

        if ($this->prospect) {
            $this->emailMessage = $this->saveBouncedMessage($this->emailThread);
            $this->emailThread->update(['is_read' => false]);

            $this->updateProspectStatus('BOUNCED');
            $this->archiveMessage('BOUNCED');
        }
    }

    /**
     * Save a bounced message reply.
     *
     * @param  array  $message
     * @param  bool  $thread
     * @return mixed
     */
    protected function saveBouncedMessage($thread = false)
    {
        if (!$thread) {
            // If no thread provided, get the prospect's last thread.
            $thread = $this->prospect->emailThreads()
                ->where('email_account_id', $this->emailAccount->id)
                ->orderBy('created_at', 'desc')
                ->first();
        }

        if (!$thread) {
            // If no thread found from the email account, find it from prospect's last message.
            try {
                $thread = $this->prospect->emailMessages()
                    ->whereIn('email_account_id', $this->emailAccount->team->emailAccounts()->pluck('id'))
                    ->orderBy('created_at', 'desc')
                    ->first()
                    ->emailThread;
            } catch (Exception $e) {
                $this->logWarning("Failed saving bounced message. No existing thread for prospect: {$this->prospect->id}. MessageId: {$this->messageId}");

                return null;
            }
        }

        // Fix the recipient address if using an alias
        if ($this->emailAccount->alias_enabled && $this->messageData['to'][0]['address'] == $this->emailAccount->alias_email_address) {
            $recipientEmail = $this->emailAccount->alias_email_address;
        } else {
            $recipientEmail = $this->emailAccount->email_address;
        }

        if (!empty($this->messageData['text']['plain'])) {
            $snippet = $this->messageData['text']['plain'];
        } elseif (!empty($this->messageData['text']['html'])) {
            $htmlBody = mb_strcut(purifyAndClearMessageBody($this->messageData['text']['html']), 0, 64000);
            $snippet = strip_tags(preg_replace('/\s+/', ' ', $htmlBody));
        } else {
            $snippet = $this->messageData['subject'];
        }

        if (!empty($this->messageData['text']['html'])) {
            $messageBody = $this->messageData['text']['html'];
        } elseif (!empty($this->messageData['text']['plain'])) {
            $messageBody = $this->messageData['text']['plain'];
        } else {
            $messageBody = '';
        }

        if (strlen($this->messageData['subject']) > 255) {
            $subject = mb_strcut($this->messageData['subject'], 0, 251) . '...';
        } else {
            $subject = $this->messageData['subject'];
        }

        // If we have managed to find a thread, save the bounce to it.
        if ($thread) {
            $eeEmailId = !empty($this->messageData['emailId']) ? $this->messageData['emailId'] : null;

            try {
                $emailMessage = EmailMessage::create([
                    'message_id' => $this->messageId,
                    'ee_thread_id' => $thread->ee_thread_id,
                    'email_account_id' => $this->emailAccount->id,
                    'agency_id' => $this->prospect->agency_id,
                    'campaign_id' => $this->prospect->campaign_id,
                    'prospect_id' => $this->prospect->id,
                    'email_thread_id' => $thread->id,
                    'from' => $this->prospect->email,
                    'to' => $recipientEmail,
                    'from_name' => $this->prospect->fullname,
                    'to_name' => $this->emailEngineAccount->name,
                    'subject' => $subject,
                    'message' => mb_strcut(purifyMessageBody($messageBody), 0, 64000),
                    'snippet' => mb_substr($snippet, 0, 180),
                    'submitted_at' => $this->messageData['date'],
                    'origin' => 'prospect',
                    'status' => 'BOUNCED',
                    'ee_email_id' => $eeEmailId,
                    'ee_id' => $this->messageData['id']
                ]);
            } catch (Exception $e) {
                // When using utf8_decode throws an exception, try to replace ...
                $emailMessage = EmailMessage::create([
                    'message_id' => $this->messageId,
                    'ee_thread_id' => $thread->ee_thread_id,
                    'email_account_id' => $this->emailEngineAccount->email_account_id,
                    'agency_id' => $this->prospect->agency_id,
                    'campaign_id' => $this->prospect->campaign_id,
                    'prospect_id' => $this->prospect->id,
                    'email_thread_id' => $thread->id,
                    'from' => $this->prospect->email,
                    'to' => $recipientEmail,
                    'from_name' => $this->prospect->fullname,
                    'to_name' => $this->emailEngineAccount->name,
                    'subject' => $subject,
                    'message' => mb_strcut(purifyAndClearMessageBody($messageBody), 0, 64000),
                    'snippet' => mb_substr($snippet, 0, 180),
                    'submitted_at' => $this->messageData['date'],
                    'origin' => 'prospect',
                    'status' => 'BOUNCED',
                    'ee_email_id' => $eeEmailId,
                    'ee_id' => $this->messageData['id']
                ]);
            }

            return $emailMessage;
        }
    }

    /**
     * Update the prospect status based on reply type.
     * Stop prospect's colleagues if 'REPLIED' and campaign's 'stop_colleagues' is ON
     *
     * @param  string  $replyType
     */
    protected function updateProspectStatus($replyType = 'REPLIED')
    {
        $activities = [
            'REPLIED' => 'ProspectReplied',
            'AUTOREPLIED' => 'ProspectAutoReplied',
            'BOUNCED' => 'EmailBounced'
        ];
        $activity = $activities[$replyType];

        // Special cases where we need to set a different prospect status than the reply type
        if ($replyType == 'REPLIED' && Str::contains(strtolower($this->emailMessage->subject), 'unsubscribe')) {
            $replyType = 'UNSUBSCRIBED';
        } elseif ($replyType == 'AUTOREPLIED' && $this->prospect->status == 'REPLIED') {
            $replyType = 'REPLIED';
        }

        if (!$this->emailThread || is_null($this->emailThread)) {
            $this->emailThread = $this->emailMessage->emailThread;
        }

        $this->prospect->last_replied = $this->emailMessage->submitted_at;
        $this->prospect->status = $replyType;
        $this->prospect->schedule_id = null;
        $this->prospect->save();

        $this->emailThread->status = $replyType;
        $this->emailThread->save();
        $this->logInfo('### Saving activity: '.$activity);

        ProspectActivity::create([
            'prospect_id' => $this->prospect->id,
            'email_message_id' => $this->emailMessage->id,
            'activity' => $activity,
        ]);

        ScheduledProspect::where('prospect_id', $this->prospect->id)->delete();
    }

    /**
     * Set message to be archived on the mailbox.
     *
     * @param  string  $replyType
     */
    protected function archiveMessage($replyType = 'BOUNCED')
    {
        $campaign = Campaign::on(self::DB_READ_CONNECTION)->find($this->emailMessage->campaign_id);

        // TODO EE: ArchiveMessage for EmailEngine
        if ($replyType == 'BOUNCED' && $campaign->archive_bounces) {
            ArchiveMessage::dispatch($this->messageId)->delay(5);
        } elseif ($replyType == 'AUTOREPLIED' && $campaign->archive_autoreplies) {
            ArchiveMessage::dispatch($this->messageId)->delay(5);
        }
    }

    protected function stopColleagues()
    {
        $this->prospect->load(['campaign']);

        if ($this->prospect->campaign->stop_colleagues) {
            $this->logInfo('Stopping colleagues of prospect_id '.$this->prospect->id);

            Bus::chain([
                new StopColleagues($this->prospect),
                new UpdateCampaignStats($this->prospect->campaign),
            ])->onQueue('default')->dispatch();
        }
    }

    /**
     * Try to find the prospect based on raw header data, by finding the related messages.
     * Detecting message id in string like "<<EMAIL>>,<<EMAIL>>"
     *
     * @return bool
     */
    protected function findProspectFromRawHeaders()
    {
        $headerMsgIds = [];
        $inReplyTo = $this->getHeaderValue('in-reply-to');
        $references = $this->getHeaderValue('references');

        if (!empty($inReplyTo)) {
            $inReplyTo = str_replace(['<', '>'], '', $inReplyTo[0]);
            array_push($headerMsgIds, $inReplyTo);
        }

        if (!empty($references)) {
            $references = explode(' ', $references[0]);

            foreach ($references as $reference) {
                $reference = str_replace(['<', '>'], '', $reference);

                if (!in_array($reference, $headerMsgIds)) {
                    array_push($headerMsgIds, $reference);
                }
            }
        }

        foreach ($headerMsgIds as $headerMsgId) {
            if ($this->emailAccount->alias_enabled) {
                $originatingMessage = EmailMessage::where('message_id', $headerMsgId)
                    ->whereIn('from', [$this->emailAccount->email_address, $this->emailAccount->alias_email_address])
                    ->first();
            } else {
                $originatingMessage = EmailMessage::where('message_id', $headerMsgId)
                    ->where('from', $this->emailAccount->email_address)
                    ->first();
            }
            if ($originatingMessage) {
                $this->prospect = Prospect::on(self::DB_READ_CONNECTION)->find($originatingMessage->prospect_id);

                return true;
            }
        }

        return false;
    }


    /**
     * Merge "EmailEngineMessageNew" job here
     * This checks if the webhook data should be parsed
     *
     * @param $response
     */
    protected function checkMessageNew()
    {
        // check if Calendar message
        $path = !empty($this->webhookData['data']['path']) ? $this->webhookData['data']['path'] : '';
        if(Str::startsWith('calendar', strtolower($path))) {
            $this->logInfo('Calendar... abort emailengine parse. '.$this->messageId);
            return false;
        }

        // check if draft
        if (!empty($this->webhookData['data']['draft']) && $this->webhookData['data']['draft']) {
            $this->logInfo('Draft message... abort emailengine parse. '.$this->messageId);
            return false;
        }

        // check if ID is in emailtest
        $emailMessageTest = EmailMessageTest::where('ee_message_id', $this->messageId)->first();

        if ($emailMessageTest) {
            $emailMessageTest->update(['status' => 'complete']);
            $this->checkForConnectionMessage($emailMessageTest);
            $this->logInfo('Testing message... abort emailengine parse. '.$this->messageId);

            return false;
        }

        $this->emailEngineAccount = EmailEngineAccount::on(self::DB_READ_CONNECTION)
            ->where('account_id', $this->emailEngineAccountId)
            ->with('emailAccount.campaigns')
            ->first();

        if ($this->emailEngineAccount != null && isset($this->webhookData['data']['date'])) {
            $receivedDate = Carbon::parse($this->webhookData['data']['date']);

            if ($this->emailEngineAccount->created_at->subHours(5)->timestamp <= $receivedDate->timestamp) {
                return true;
            }
        }

        $this->logInfo('Old message... abort emailengine parse. '.$this->messageId);
        return false;
    }

    protected function scanTextForEmailAndProcessBounced()
    {
        $textBlocks = [
            'subject' => $this->messageData['subject'],
            'plain' => strip_tags(preg_replace('/\s+/', ' ', purifyAndClearMessageBody($this->messageData['text']['plain']))),
            'html' => strip_tags(preg_replace('/\s+/', ' ', purifyAndClearMessageBody($this->messageData['text']['html']))),
        ];

        foreach ($textBlocks as $textBlock) {
            if (preg_match_all(self::EMAIL_REGEX_PATTERN, $textBlock, $matches) > 0) {
                foreach ($matches[0] as $match) {
                    if ($this->processBounced($match)) {

                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * Check if the test message was sent to check the account's SMTP status
     * if the message was sent then the newly connected account is working
     *
     */
    protected function checkForConnectionMessage($emailMessageTest) {
        $emailMessageTest->load(['emailAccount']);

        if ($emailMessageTest->is_connect_msg && $emailMessageTest->emailAccount->error == 'account.connecting') {
            $emailMessageTest->emailAccount->update(['error' => null]);
            RescheduleEmailAccount::dispatch($emailMessageTest->emailAccount);

            $this->logInfo('Connection success: account-'.$emailMessageTest->emailAccount->hashid);
        }
    }
}
