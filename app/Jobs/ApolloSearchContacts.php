<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Traits\LogsMessages;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Arr;

use GuzzleHttp\Client as HttpClient;
use GuzzleHttp\Exception\RequestException;

use App\Agency;
use App\Contact;
use App\Models\StoreLeads\Domain;
use App\Models\StoreLeads\Search;
use App\Services\SeniorityRankingService;

class ApolloSearchContacts implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, LogsMessages;

    public $tries = 1;
    public $timeout = 600;
    protected const DOMAINS_PER_JOB = 5;
    protected const SEARCHED_MONTHS_AGO = 4;
    protected const VERIFIED_MONTHS_AGO = 6;
    protected const APOLLO_PER_PAGE = 100;

    private $search;
    private $filter;
    private $value;

    private $domainUrls = [];

    /**
     * Create a new job instance.
     */
    public function __construct($filter, $value)
    {
        $this->filter = $filter;
        $this->value = intval($value);
        $this->logPrefix = "ApolloSearchContacts : ";
    }

    public function tags()
    {
        return ['apollo-search-contacts'];
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $this->logInfo("Start - filter: {$this->filter}, value: {$this->value}");

        if(empty($this->filter) || (empty($this->value) && $this->value !== 0)) {
            $this->logInfo("missing filter or value");
            return;
        }

        // create default agency search
        $wavoAgency = Agency::where('domain', config('app.platformUrl'))
//            ->whereNull('current_billing_plan')
            ->first();

        $this->search = Search::firstOrCreate([
            'agency_id' => $wavoAgency->id,
            'name' => 'AA contacts search',
        ], [
            'status' => 'DONE',
            'search_filters' => []
        ]);

        // search domains
        $domains = $this->fetchDomains();

        if (!$domains->count()) {
            $this->logInfo("Domains not found. Search for {$this->filter}={$this->value} completed");
            return;
        }

        $this->logInfo("domains found:". count($domains));

        $domainIds = [];
        $domainContacts = [];
        $contactsData = [];
        $mainDomains = [];

        foreach ($domains as $domain) {
            $this->logInfo("{$domain->id}: {$domain->name}");
            $mainDomain = getMainDomain(urlTrim($domain->name));

            $this->domainUrls[] = $mainDomain;
            $domainContacts[] = [
                'id' => $domain->id,
                'name' => $mainDomain
            ];
            $domainIds[] = $domain->id;
        }

        // submit to apollo
        $apolloContacts = $this->searchApollo();
        $this->logInfo("apollo contacts found: ".count($apolloContacts['people']));

        // check for result's status
        if ($apolloContacts['status'] == 429) {
            $this->logInfo("apollo search status 429, retrying after 30mins");

            self::dispatch($this->filter, $this->value)
                ->onQueue('emailfinder')
                ->delay(now()->addMinutes(30));

            return;
        } elseif ($apolloContacts['status'] != 200) {
            $this->logInfo("stopped apollo search status {$apolloContacts['status']}");
            return;
        }

        // check for ratelimit issue and redispatch job after required time
        if (empty($apolloContacts['rateLimitsLeft']['day'])) {
            self::dispatch($this->filter, $this->value)
                ->onQueue('emailfinder')
                ->delay(now()->addDay());
        } elseif (empty($apolloContacts['rateLimitsLeft']['hour'])) {
            self::dispatch($this->filter, $this->value)
                ->onQueue('emailfinder')
                ->delay(now()->addMinutes(70));
        } elseif (empty($apolloContacts['rateLimitsLeft']['minute'])) {
            self::dispatch($this->filter, $this->value)
                ->onQueue('emailfinder')
                ->delay(now()->addMinutes(2));
        }

        $seniorityService = app(SeniorityRankingService::class);

        // pluck $apolloContacts ids and check if saved in contacts already
        $apolloIds = Arr::pluck($apolloContacts['people'], 'id');
        $apolloEmailIds = Arr::map($apolloIds, function (string $value, string $key) {
            return 'email_placeholder_'.$value;
        });

        $existingContacts = Contact::select('email', 'domain_id')
            ->whereIn('email', $apolloEmailIds)
            ->get();

        $this->logInfo("existing apolloEmailIds: ".count($existingContacts));

        // find email of each apolloContacts and add it to correct domain
        foreach ($apolloContacts['people'] as $contact) {
            $orgId = $contact['organization_id'];
            $org = $contact['organization'];
            $orgUrl = getMainDomain(urlTrim($org['website_url']));
            $emailPlaceHolder = 'email_placeholder_'.$contact['id'];

            $domainContact = Arr::first($domainContacts, function($domain, $key) use($orgUrl) {
                return $domain['name'] == $orgUrl;
            });

            if (empty($domainContact)) {
                $this->logInfo("no domainContacts index found: ".$orgUrl);
                continue;
            }

            // stop if ID is already saved in contacts table
            $existingContact = $existingContacts->where('email', $emailPlaceHolder)->first();
            if(!empty($existingContact)) {
                $this->logInfo("existing contact: $emailPlaceHolder");
                continue;
            }

            // construct contact object and push to $contactsData array
            $domainId = $domainContact['id'];
            $seniorityId = $seniorityService->determineSeniority($contact['seniority']);
            $contactData = [
                'email' => $emailPlaceHolder,
                'domain' => $orgUrl,
                'domain_id' => $domainId,
                'first_name' => $contact['first_name'] ?? null,
                'last_name' => $contact['last_name'] ?? null,
                'position' => $contact['title'] ?? null,
                'linkedin' => $contact['linkedin_url'] ?? null,
                'linkedin_avatar_url' => $contact['photo_url'] ?? null,
                'enrichment_source' => 'apollo',
                'website' => $org['website_url'] ?? null,
                'phone' => $org['phone'] ?? null,
                'country' => $contact['country'] ?? null,
                'state' => $contact['state'] ?? null,
                'city' => $contact['city'] ?? null,
                'seniority_id' => $seniorityId,
                // 'company_id' => null, // todo: save company or just use domain?
            ];
            $contactsData[] = $contactData;
        }

        // bulk insert in contacts table
        if (count($contactsData)) {
            Contact::insert($contactsData);
            $this->logInfo("inserted new contacts: ".count($contactsData));
        } else {
            $this->logInfo("no inserted contacts");
        }

        // mark domains that it's been searched in apollo
        Domain::whereIn('id', $domainIds)->update([
            'apollo_searched_at' => now()
        ]);
        $this->logInfo("domains marked as apollo_searched: ".count($domainIds));

        // dispatch this job again
        self::dispatch($this->filter, $this->value)->onQueue('emailfinder');
    }

    protected function fetchDomains($id = 0)
    {
        $this->logInfo("Fetching domains with ID > {$id}");

        // Prepare filters
        $keywords = $this->filter == 'keywords' ? [$this->value] : null;
        $categoryIds = $this->filter == 'categories' ? [$this->value] : [];
        $countryIds = $this->filter == 'countries' ? [$this->value] : [];
        $saleIds = $this->filter == 'estimated_sales' ? [$this->value] : [];
        $productCountIds = $this->filter == 'product_counts' ? [$this->value] : [];
        $platformIds = $this->filter == 'platforms' ? [$this->value] : [];
        $planIds = $this->filter == 'plans' ? [$this->value] : [];

        // exclude platforms if plans is available because plan is kindof sub-platform filter
        if(!empty($planIds)) {
            $platformIds = [];
        }

        $appIds = $this->filter == 'apps' ? [$this->value] : [];
        $tagIds = $this->filter == 'tags' ? [$this->value] : [];
        $monthIds = $this->filter == 'months' ? [$this->value] : [];
        $themeIds = $this->filter == 'themes' ? [$this->value] : [];
        $featureIds = $this->filter == 'features' ? [$this->value] : [];
        $languageIds = $this->filter == 'languages' ? [$this->value] : [];
        if (empty($languageIds)) {
            $languageIds = $this->filter == 'domain_languages' ? [$this->value] : [];
        }
        $currencyIds = $this->filter == 'currencies' ? [$this->value] : [];
        $technologyIds = $this->filter == 'technologies' ? [$this->value] : [];
        $contactTypeIds = $this->filter == 'contact_types' ? [$this->value] : [];
        $themeVendorIds = $this->filter == 'theme_vendors' ? [$this->value] : [];
        $salesChannelIds = $this->filter == 'sales_channels' ? [$this->value] : [];
        $shippingCarrierIds = $this->filter == 'shipping_carriers' ? [$this->value] : [];
        $shippingCountryIds = $this->filter == 'shipping_countries' ? [$this->value] : [];
        $agencyId = $this->search->agency_id;

        $domains = Domain::select(
                'sl_domains.*',
            )
            ->ofKeywords($keywords)
            ->ofCategories($categoryIds)
            ->ofCountries($countryIds)
            ->ofSales($saleIds)
            ->ofProductCounts($productCountIds)
            ->ofPlatforms($platformIds)
            ->ofPlans($planIds)
            ->ofApps($appIds)
            ->ofContactTypes($contactTypeIds)
            ->ofCurrencies($currencyIds)
            ->ofFeatures($featureIds)
            ->ofLanguages($languageIds)
            ->ofMonths($monthIds)
            ->ofSalesChannels($salesChannelIds)
            ->ofShippingCarriers($shippingCarrierIds)
            ->ofShippingCountries($shippingCountryIds)
            ->ofTags($tagIds)
            ->ofTechnologies($technologyIds)
            ->ofThemes($themeIds)
            ->ofThemeVendors($themeVendorIds)
            ->whereNull('last_searched_at')
            ->whereNull('apollo_searched_at')
            ->take(self::DOMAINS_PER_JOB)
            ->get();

        return $domains;
    }

    /**
     * https://docs.apollo.io/reference/people-search
     * Search contacts in apollo by passing domains
     */
    protected function searchApollo()
    {
        $people = [];
        $headers = [];
        $rateLimitsLeft = [
            'day' => 0,
            'hour' => 0,
            'minute' => 0
        ];

        $notificationMessage = 'API Key Error. Please validate API Keys.';
        $apiKey = config('app.emailFinders.apollo.apiKey');
        $http = new HttpClient(['base_uri' => 'https://api.apollo.io/api/v1/']);
        $status = null;
        $response = null;
        $titles = [
            "owner",
            "founder",
            "c_suite",
            "partner",
            "vp",
            "director"
        ];

        // apollo doesnt want indexed array param from http_build_query and guzzle
        $qSeniorities = '';
        $qOrgDomainList = '';

        foreach ($titles as $title) {
            $qSeniorities = $qSeniorities.'&person_seniorities[]='.$title;
        }
        foreach ($this->domainUrls as $url) {
            $qOrgDomainList = $qOrgDomainList.'&q_organization_domains_list[]='.$url;
        }

        try {
            $response = $http->get('mixed_people/search?per_page='.self::APOLLO_PER_PAGE.$qSeniorities.$qOrgDomainList, [
                'headers' => [
                    'X-API-KEY' => $apiKey,
                    'Content-Type' => 'application/json'
                ]
            ]);

            $status = $response->getStatusCode();

        } catch (RequestException $e) {
            $response = null;
            $status = $e->getResponse()->getStatusCode();
            $errMsg = $e->getMessage();
            $errResponse = $e->getResponse() ? json_decode($e->getResponse()->getBody()->getContents(), true) : null;

            $this->logError("searchGoogle failed with status {$status}");
            $this->logError(json_encode($errMsg, JSON_INVALID_UTF8_IGNORE));

            if (!empty($errResponse['message'])) {
                $this->logError("apollo contact search failed with error: {$errResponse['message']}");
            }
        }

        if ($status == 200 && $response) {
            $response->getBody()->rewind();
            $responseData = collect(json_decode($response->getBody()->getContents(), true));

            if(count($responseData["people"])) {
                $people = $responseData["people"];
            }

            $headers = $response->getHeaders();
            $rateLimitsLeft['minute'] = intval($headers['x-minute-requests-left'][0] ?? 0);
            $rateLimitsLeft['hour'] = intval($headers['x-hourly-requests-left'][0] ?? 0);
            $rateLimitsLeft['day'] = intval($headers['x-24-hour-requests-left'][0] ?? 0);

        } else {
            if ($status == 400 || $status == 401) {

            } else {

            }
        }

        return [
            'people' => $people,
            'rateLimitsLeft' => $rateLimitsLeft,
            'status' => $status
        ];
    }
}
