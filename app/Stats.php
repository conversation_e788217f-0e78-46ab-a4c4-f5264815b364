<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Stats extends Model
{
    protected $guarded = ['id'];

    protected $appends = ['success_rate'];

    /**
     * Stats of a campaign.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function campaign()
    {
        return $this->belongsTo(Campaign::class);
    }

    public function campaignStages()
    {
        return $this->hasMany(CampaignStage::class, 'campaign_id', 'campaign_id');
    }

    public function scopeOfCampaign($query, $campaignId)
    {
        return $query->where('campaign_id', $campaignId);
    }

    public function getSuccessRateAttribute()
    {
        if ($this->contacted > 0) {
            // we have a LinkedIn & email campaign
            return round($this->interested / $this->contacted * 100, 1);
        }

        return 0;
    }

// TODO: Check updated code for changes needed to port
//    /**
//     * Update the total amount of prospects.
//     * Used on campaign init or resume.
//     *
//     * @param $amount
//     * @return bool
//     */
//    public function updateProspects($amount)
//    {
//        $this->prospects = $amount;
//        $unAvailable = $this->campaign->prospects()
//            ->ofEmailsSent(0)
//            ->where(function ($query) {
//                $query->whereIn('status', ['UNSUBSCRIBED', 'STOPPED'])
//                    ->orWhere('is_suppressed', true);
//            })->count();
//
//        $queued = $amount - $this->sent - $unAvailable;
//        $this->queue = $queued < 0 ? 0 : $queued;
//
//        return $this->save();
//    }
//
//    /**
//     * Calculate stats based on campaign prospects.
//     */
//    public function calculate()
//    {
//        $statusAggregates = $this->campaign->prospects()->select('status', DB::raw('count(*) AS prospects'))
//            ->groupBy('status')
//            ->whereIn('status', ['BOUNCED','AUTOREPLIED'])
//            ->get()
//            ->mapWithKeys(function($item) {
//                return [$item['status'] => $item['prospects']];
//            })->all();
//        $bounced = Arr::has($statusAggregates, 'BOUNCED') ? $statusAggregates['BOUNCED'] : 0;
//        $autoreplied = Arr::has($statusAggregates, 'AUTOREPLIED') ? $statusAggregates['AUTOREPLIED'] : 0;
//
//        $interestedAggregates = $this->campaign->prospects()->select('interested', DB::raw('count(*) AS prospects'))
//            ->groupBy('interested')
//            ->whereIn('interested', ['POSITIVE','NEGATIVE','NEUTRAL'])
//            ->get()
//            ->mapWithKeys(function($item) {
//                return [$item['interested'] => $item['prospects']];
//            })->all();
//        $positive = Arr::has($interestedAggregates, 'POSITIVE') ? $interestedAggregates['POSITIVE'] : 0;
//        $negative = Arr::has($interestedAggregates, 'NEGATIVE') ? $interestedAggregates['NEGATIVE'] : 0;
//        $neutral = Arr::has($interestedAggregates, 'NEUTRAL') ? $interestedAggregates['NEUTRAL'] : 0;
//
//        $replied = $this->campaign->prospects()->whereHas('emailMessages', function ($query) {
//            $query->where('origin', 'prospect')
//                ->whereNotNull('nylas_message_id')
//                ->orWhereNotNull('ee_id')
//                ->where('status', 'REPLIED');
//        })->count();
//
//        $sent = $this->campaign->prospects()->where('emails_sent', '!=', 0)->count();
//
//        //$unsubscribedBeforeSending = $this->campaign->prospects()->ofStatus('UNSUBSCRIBED')->ofEmailsSent(0)->count();
//        //$stoppedBeforeSending = $this->campaign->prospects()->ofStatus('STOPPED')->ofEmailsSent(0)->count();
//        //$queued = $this->campaign->prospects()->count() - $sent - $unsubscribedBeforeSending - $stoppedBeforeSending;
//
//        $queued = $this->campaign->prospects()->where('emails_sent', 0)
//            ->where('status', 'OK')
//            ->where('is_suppressed', false)
//            ->count();
//
//        $delivered = $sent - $bounced;
//
//        $this->sent = $sent;
//        $this->queue = $queued < 0 ? 0 : $queued;
//        $this->positive = $positive;
//        $this->negative = $negative;
//        $this->neutral = $neutral;
//        $this->replied = $replied;
//        $this->autoreplied = $autoreplied;
//        $this->bounced = $bounced;
//        $this->delivery = $delivered < 0 ? 0 : $delivered;
//        $this->clicked = $this->campaign->emailMessages()->clicked()->count();
//        $this->opened = $this->campaign->emailMessages()->opened()->count();
//
//        $this->save();
//    }
}
