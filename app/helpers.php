<?php

use Illuminate\Support\Str;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

/**
 * Format a number value with K or M for thousands or millions.
 *
 * @param int $value
 * @return string
 */
function number_ks($value = 0)
{
    if ($value > 999 && $value <= 999999) {
        return floor($value / 1000) . 'K';
    } elseif ($value > 999999) {
        return floor($value / 1000000) . 'M';
    } else {
        return number_format($value);
    }
}

/**
 * Check that a Nylas Webhook Request is signed with the Nylas Secret Key.
 * This is used to make sure that the request really came from <PERSON><PERSON><PERSON>.
 *
 * @param $request
 * @return bool
 */
function checkNylasRequest($request)
{
    return true;

    $signature = $request->header('X-Nylas-Signature');
    $validator = hash_hmac('sha256', $request->getContent(), config('app.nylas.appSecret'));

    return $signature === $validator;
}

/**
 * Utility function to decode a json response (like from <PERSON><PERSON><PERSON>).
 *
 * @param mixed $content
 * @throws \Exception
 * @return array
 */
function parseJson($content)
{
    $data = json_decode($content, true);

    if (JSON_ERROR_NONE !== json_last_error()) {
        $msg = 'Unable to parse response body into JSON: ';

        throw new \Exception($msg.json_last_error());
    }

    return null === $data ? [] : $data;
}

/**
 * Get a response body contents into a parsed json.
 *
 * @param $response
 * @return array
 * @throws Exception
 */
function parseJsonResponse($response)
{
    return parseJson($response->getBody()->getContents());
}

/**
 * @param $message
 * @return bool|string
 */
function removeNylasTrackingCode($message)
{
    if (empty($message)) {

        return $message;
    }

    // Wrap the message text in a div tag.
    $message = "<div>$message</div>";

    $dom = new DOMDocument();
    // Load string into DOM object.
    // LIBXML_HTML_NOIMPLIED turns off the automatic adding of implied html/body elements.
    // LIBXML_HTML_NODEFDTD prevents a default doctype being added when one is not found.
    @$dom->loadHTML($message, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
    $dom->preserveWhiteSpace = false;

    // Remove images with width of 1.
    $images = $dom->getElementsByTagName('img');
    foreach ($images as $image) {
        foreach ($image->attributes as $attribute) {
            if ($attribute->name == 'width' && $attribute->value == '1') {
                $image->parentNode->removeChild($image);

                break;
            }
        }
    }

    $message = $dom->saveHTML($dom->documentElement);

    return $message;
}

/**
 * Return a number presented as string percentage.
 *
 * @param $number
 * @param int $decimals
 * @return string
 */
function getPercentAsString($number, $decimals = 1){
    return round(100*($number),$decimals).'%';
}

/**
 * Divide 2 integers evenly.
 * Split an $n amount into $k parts so that nothing remains.
 * E.g. dividing 13 into 3 parts returns an array of [5, 4, 4].
 *
 * @param int $n
 * @param int $k
 * @return array
 */
function divideEvenly($n, $k) {
    $result = array();

    // Avoid modulo by zero
    if ($k == 0) {
        $result[0] = 0;

        return $result;
    }

    // If need to divide by 1
    if ($k == 1) {
        $result[0] = $n;

        return $result;
    }

    // Handle other cases
    for ($i = 0; $i < $k; $i++) {
        $result[$i] = floor($n/$k);
    }

    for ($i = 0; $i < $n%$k; $i++) {
        $result[$i] += 1;
    }

    return $result;
}

function safeDecrement(int $value, int $decrementAmount = 1) {
    if ($value - $decrementAmount <= 0) {
        return 0;
    }

    return $value - $decrementAmount;
}

/**
 * Get a random integer between a min and max, weighted towards the min.
 * https://gamedev.stackexchange.com/questions/116832/random-number-in-a-range-biased-toward-the-low-end-of-the-range
 *
 * @param  $min
 * @param  $max
 * @return int
 */
function randomWeightedLow($min, $max) {
    return (int)floor(abs(rand(0,99) - rand(0,99))/100 * (1 + $max - $min) + $min);
}

/**
 * Get a random integer between a min and max, weighted towards the middle/average.
 *
 * @param $min
 * @param $max
 * @return int
 */
function randomWeightedMiddle($min, $max) {
    $r1 = rand($min, $max);
    $r2 = rand($min, $max);
    $r3 = rand($min, $max);

    return (int)floor(($r1 + $r2 + $r3)/3);
}

/**
 * Increase a model object related counter in Redis.
 * Can receive either a specific object, to increment its counter,
 * or a snake case class name.
 *
 * @param  mixed  $model
 * @param  string  $counter
 * @param  int  $key
 * @param  int  $increment
 * @return mixed
 */
function incrementRedisCounter($counter, $model, $key = 0, $increment = 1)
{
    if (is_object($model)) {
        $key = $model->getKey();
        $model = Str::snake(class_basename($model));
    }

    if ($key == 0) {
        return null;
    }

    return Redis::hincrby("laravel:$counter:$model", "$key", $increment);
}

/**
 * Reset (remove key) a model object related counter from Redis.
 * Can receive either a specific object, to delete its counter,
 * or a snake case class name.
 * Use key = '*' to delete counter for all models.

 *
 * @param  string  $counter
 * @param  mixed  $model
 * @param  mixed  $key
 * @return mixed
 */
function resetRedisCounter($counter, $model, $key = '*')
{
    if (is_object($model)) {
        $key = $model->getKey();
        $model = Str::snake(class_basename($model));
    }

    if ($key == '*') {
        Redis::del("laravel:$counter:$model");
    }

    return Redis::hdel("laravel:$counter:$model", "$key");
}

/**
 * Return all counters of a model class from Redis.
 * Can receive either a specific object, to get its counter,
 * or a snake case class name.
 * Use key = '*' to get all models.
 *
 * @param  mixed   $model
 * @param  string  $counter
 * @param  mixed   $key
 * @return \Illuminate\Support\Collection
 */
function getRedisCounter($counter, $model, $key = '*')
{
    if (is_object($model)) {
        $key = $model->getKey();
        $model = Str::snake(class_basename($model));
    }

    if ($key == '*') {
        return collect(Redis::hgetall("laravel:$counter:$model"));
    }

    return collect(Redis::hget("laravel:$counter:$model", "$key"));
}

/**
 * Flush a model cache tag.
 *
 * @param $model
 * @return mixed
 */
function flushModelCache($model)
{
    if (is_object($model)) {
        $modelName = Str::snake(class_basename($model));
    } elseif (is_string($model)) {
        $modelName = $model;
    }

    return Cache::store(config('cache.model.driver'))->tags($modelName)->flush();
}

function flushSearchDomainCache()
{
    return Cache::store(config('cache.model.driver'))->tags('search_domains')->flush();
}

/**
 * Apply html purifier (fix html).
 *
 * @param  string  $text
 * @return string
 */
function purifyMessageBody($text)
{
    $messageBody = removeNylasTrackingCode(clean($text, 'html5-definitions'));

    $messageBody = utf8_decode($messageBody);

    return $messageBody;
}

/**
 * Apply html purifier (fix html) and clear an email message body of unwanted characters.
 *
 * @param  string  $text
 * @return string
 */
function purifyAndClearMessageBody($text)
{
    $messageBody = removeNylasTrackingCode(clean($text, 'html5-definitions'));

    // Remove non breaking space
    if (preg_match('/\xC2\xA0/u', $messageBody, $matches) > 0) {
        $messageBody = preg_replace('/\xC2\xA0/u', '', $messageBody);
    } elseif (preg_match('/\xA0/u', $messageBody, $matches) > 0) {
        $messageBody = preg_replace('/\xA0/u', '', $messageBody);
    }

    $messageBody = utf8_decode($messageBody);

    return $messageBody;
}

/**
 * Get the model id from a hashid value.
 *
 * @param $model
 * @param $hash
 * @param  null  $default
 * @return |null
 */
function decodeModelHashid($model, $hash, $default = null)
{
    if ($hash !== $default) {
        return Hashids::connection($model)->decode($hash)[0] ?? $default;
    }

    return $default;
}

/**
 * Return object ids from job tags from jobs that are currently pending.
 * Query with specific job class, queue and tag prefix.
 *
 * @param string $queue  The queue to search for pending jobs
 * @param string $job  The fully qualified job class (eg. "App\Jobs\Linkedin\ScrapeLinkedinSearch")
 * @param string $tagPrefix  The prefix of the tag we are searchign for
 * @param  null  $excludedJobId
 * @return \Illuminate\Support\Collection
 */
function getPendingJobTagIds($queue, $job, $tagPrefix, $excludedJobId = null)
{
    $pending = getJobTagIds('lrange', "queues:$queue", $job, $tagPrefix, $excludedJobId);
    $delayed = getJobTagIds('zrange', "queues:$queue:delayed", $job, $tagPrefix, $excludedJobId);
    $reserved = getJobTagIds('zrange', "queues:$queue:reserved", $job, $tagPrefix, $excludedJobId);

    return $pending->merge($delayed)->merge($reserved)->unique();
}

/**
 * Helper method to fetch job tag ids from redis lists.
 *
 * @param string $listType  'lrange' or 'zrange'
 * @param string $queue
 * @param string $job
 * @param string $tagPrefix
 * @param  null  $excludedJobId
 * @return \Illuminate\Support\Collection
 */
function getJobTagIds($listType, $queue, $job, $tagPrefix, $excludedJobId = null)
{
    $connection = Redis::connection('default');

    return collect(call_user_func_array([$connection, $listType], [$queue, 0, -1]))->map(function ($item) {
            return json_decode($item);
        })->filter(function ($item) use ($job, $excludedJobId) {
            return $item->displayName == $job && $item->id != $excludedJobId;
        })->map(function ($item) use ($tagPrefix) {
            foreach ($item->tags as $tag) {
                if (Str::startsWith($tag, $tagPrefix)) {
                    return Str::after($tag, $tagPrefix);
                }
            }
        });
}

function getLinkedinCompanySlug($url)
{
    $urlStart = 'https://www.linkedin.com/sales/company/';

    if (!is_null($url) && Str::startsWith($url, $urlStart)) {
        $companyId = str_replace($urlStart, '', $url);

        return 'https://www.linkedin.com/company/'.$companyId;
    }

    return null;
}

function getLinkedinPersonSlug($url)
{
    /*
    $urlStart = 'https://www.linkedin.com/sales/people/';
    $urlStartLead = 'https://www.linkedin.com/sales/lead/';
    $urlStartTalent = 'https://www.linkedin.com/talent/profile/';

    if (!is_null($url) && Str::startsWith($url, $urlStartLead)) {
        $urlString = str_replace($urlStartLead, '',$url);
        $urlParams = explode(',', $urlString);

        return 'https://www.linkedin.com/in/'.$urlParams[0];
    }

    if (!is_null($url) && Str::startsWith($url, $urlStart)) {
        $urlString = str_replace($urlStart, '',$url);
        $urlParams = explode(',', $urlString);

        return 'https://www.linkedin.com/in/'.$urlParams[0];
    }
    */

    $baseUrls = [
        'https://www.linkedin.com/sales/people/',
        'https://www.linkedin.com/sales/lead/',
        'https://www.linkedin.com/talent/profile/'
    ];

    if(is_null($url)) {
        return null;
    }

    foreach ($baseUrls as $baseUrl) {
        if (Str::startsWith($url, $baseUrl)) {
            $urlString = str_replace($baseUrl, '',$url);
            $urlParams = explode(',', $urlString);

            return 'https://www.linkedin.com/in/'.$urlParams[0];
        }
    }

    return null;
}

function parseLinkedinProfileUrl($url)
{
    $decodedUrl = urldecode($url);
    if (Str::startsWith($decodedUrl, 'https://www.linkedin.com/in/')) {
        $url = Str::after($decodedUrl, 'https://www.linkedin.com/in/');
    } elseif (Str::startsWith($decodedUrl, '/in/')) {
        $url = Str::after($decodedUrl, '/in/');
    }
    if (Str::endsWith($url, '/')) {
        $url = Str::beforeLast($url, '/');
    }

    return $url;
}

function parseLinkedinFsdProfile($liUrl)
{
    $fsdProfile = null;
    $urlDecoded = urldecode($liUrl);
    $urlParts = parse_url($urlDecoded);

    if(empty($urlParts['query'])) {
        return $fsdProfile;
    }

    parse_str($urlParts['query'], $arrQueries);

    if(!empty($arrQueries['profileUrn'])) {
        $fsdProfile = Str::after($arrQueries['profileUrn'], 'fsd_profile:');
    }

    return $fsdProfile;
}

/*
 * Helper function to parse perfectly formatted merge fields (except dynamic)
 * for simpler validation of broken snippet format
 */
function parseMergeFieldContent($content, $customFields = [], $defaultFields = [], $calculatedFields = [])
{
    $calFields = ['today', 'sender_lastname', 'sender_firstname', 'time_of_day'];
    $defFields = [
        'first_name', 'last_name', 'email', 'company', 'industry', 'website',
        'title', 'phone', 'address', 'city', 'state', 'country', 'tags'
    ];

    $defaultFields = array_unique(array_merge($defaultFields, $defFields));
    $calculatedFields = array_unique(array_merge($calculatedFields, $calFields));

    // replace default snippets
    foreach ($defaultFields as $key => $field) {
        $content = str_replace('{{'.$field.'}}', $field, $content);
    }

    // replace custom snippets
    foreach ($customFields as $key => $field) {
        $content = str_replace('{{'.$field.'}}', $field, $content);
    }

    // replace dynamic dates
    $pattern = "/{{today[^}]*}}/";
    preg_match_all($pattern, $content, $arrDynamicDates);
    $dateTZ = config('app.timezone');
    $campTZ = str_replace(' ', '_', $dateTZ);

    if (@count(data_get($arrDynamicDates, '0'))) {
        foreach (data_get($arrDynamicDates, '0') as $key => $date) {
            $strDate = '';

            // check if date is today, tomorrow, or N
            if($date == '{{today}}') {
                $strDate = Carbon::now($campTZ)->format('l');
            } elseif ($date == '{{today+1}}') {
                $strDate = Carbon::now($campTZ)->addDay()->format('l');
            } else {
                $intDays = data_get(explode('+', str_replace(['{{', '}}'], '',$date)), '1', 0);

                // check if days is numeric
                if (is_numeric($intDays)) {
                    $strDate = Carbon::now($campTZ)->addDays($intDays)->format('l');
                }
            }

            $content = str_replace($date, $strDate, $content);
        }
    }

    // replace calculated snippets
    foreach ($calculatedFields as $key => $field) {
        $content = str_replace('{{'.$field.'}}', $field, $content);
    }

    // parse default mergefields with fallback
    $fallbackFields = getRequiredMergeFields($content);
    foreach ($fallbackFields as $key => $field) {
        $fallbackParts = explode('||', $field);

        if(count($fallbackParts) != 2) {
            continue;
        }

        $fieldValue = trim($fallbackParts[0]);
        $fallbackValue = trim($fallbackParts[1]);

        if(empty($fallbackValue) || !Str::contains($fallbackValue, 'default:')) {
            continue;
        }

        $fallbackValue = str_replace('default:', '', $fallbackValue);
        $fallbackValue = trim(str_replace('"', '', $fallbackValue));

        // check field if in default mergefields
        if(in_array($fieldValue, $defaultFields)) {
            $content = str_replace('{{'.$field.'}}', $fallbackValue, $content);
        }

        // check field if in snippet mergefields
        if(in_array($fieldValue, $customFields)) {
            $content = str_replace('{{'.$field.'}}', $fallbackValue, $content);
        }
    }

    return $content;
}

/**
 * Find the necessary fields that are needed to compile the template succesfully.
 *
 * @return array
 */
function getRequiredMergeFields($content)
{
    preg_match_all('/{{([^}}]*)}}/', $content, $matches);

    return $matches[1];
}

function getGlobalPromptTemplate()
{
    $contents = array(
        "are_you" => array(
            "name" => "Are you…",
            "system" => "<p>You are an expert at cold email outreach. </p><p> Our company, [[name & value prop]]. </p><p> Write a sentence to ask if the target company is [[deliverable]]. Use a second person perspective. </p><p> This is the second sentence in the email so do not include a greeting. </p><p> Customize the sentence to the target company by using the name, title and description provided in the user prompt. </p><p> Use best practices for cold email by only using 6 to 12 words.</p>",
            "user" => "<p>name: {{company}} </p><p> title: {{website_title}} </p><p> description: {{website_description}}</p>",
            "help_text" => "Create a sentence that can be used as a relevant opening line that sparks curiosity. It can also be used in the body of an email to increase relevance and personalization."
        ),
        "handle_more" => array(
            "name" => "Can you handle more…",
            "system" => "<p>You are an expert at cold email outreach. </p><p> Our company, [[name & value prop]]. </p><p> Write a sentence to ask if the target company can handle any more [[result]]. Use a second person perspective.</p><p> This is the second sentence in the email so do not include a greeting. </p><p> Customize the sentence to the target company by using the name, title and description provided in the user prompt. </p><p> Use best practices for cold email by only using 6 to 12 words.</p>",
            "user" => "<p>name: {{company}} </p><p> title: {{website_title}} </p><p> description: {{website_description}}</p>",
            "help_text" => "Another prompt to create a sentence that can be used as a relevant opening line, leveraging curiosity. It can also be used in the body of an email to increase relevance and personalization."
        ),
        "satisfied_with" => array(
            "name" => "Are you satisfied with…",
            "system" => "<p>You are an expert at cold email outreach. </p><p> Our company, [[name & value prop]]. </p><p> Write a sentence to ask if the target company is satisfied with the results from their current [[activity]] approach. Use a second person perspective. </p><p> This is the second sentence in the email so do not include a greeting. </p><p> Customize the sentence to the target company by using the name, title and description provided in the user prompt. </p><p> Use best practices for cold email by only using 6 to 12 words.</p>",
            "user" => "<p>name: {{company}} </p><p> title: {{website_title}} </p><p> description: {{website_description}}</p>",
            "help_text" => "Third approach to creating a sentence that can be used as a relevant opening line that creates curiosity. It can also be used in the body of an email to increase relevance and personalization."
        ),
        "subject_line" => array(
            "name" => "Subject line generator",
            "system" => "<p>You are an expert at cold email outreach. </p><p> Our company, [[name & value prop]]. </p><p> Write an email subject line for a cold email that will get the target company to open the email. </p><p> Customize the subject line to match the first sentence of the email and incorporate the name, title and description for the target company provided in the user prompt. Reference their product or their name but not [[name]]. </p><p> Use best practices for cold email subject line length by using 6 to 9 words and only capitalize names and the first word in the subject line.</p>",
            "user" => "<p>first sentence: {{opening_line}} </p><p> name: {{company}} </p><p> title: {{website_title}} </p><p> description: {{website_description}}</p>",
            "help_text" => "Generate a subject line customized to the first line of your email. Use it together with the opening sentence prompts above to increase relevance and personalization. You can include modifiers to turn it into a question, rhyme etc. to cut through inbox noise."
        ),
        "compliment" => array(
            "name" => "Compliment generator",
            "system" => "<p>You are an expert at cold email outreach. </p><p> Write a sentence to complement the target company about their product from the first person perspective in a neutral tone. </p><p> Customize the sentence to the target company by using the name, title and description provided in the user prompt. </p><p> Use best practices for cold email by only using 6 to 15 words.</p>",
            "user" => "<p>name: {{company}} </p><p> title: {{website_title}} </p><p> description: {{website_description}}</p>",
            "help_text" => "Create a compliment about the target company from the first person perspective (ie. 'I like…'). Creates goodwill while increasing relevance and personalization."
        ),
        "observation" => array(
            "name" => "Observation generator",
            "system" => "<p>You are an expert at cold email outreach. </p><p> Write a sentence providing an insight about the target company. </p><p> Start the sentence with I and phrase it as though you are speaking directly to someone at the target company. </p><p> The name, title and description of the target company is provided in the user prompt. </p><p> Use best practices for cold email by only using 8 to 16 words.</p>",
            "user" => "<p>name: {{company}} </p><p> title: {{website_title}} </p><p> description: {{website_description}}</p>",
            "help_text" => "Create an observation about the target company from the second person perspective. It shows that you did some research about the prospect, while increasing relevance and personalization.  Depending on the quality of the input variables lifted from the targets website, the results from this prompt can sometimes be a bit lame. But it still goes well beyond a standard merge field."
        ),
        "call_to_action" => array(
            "name" => "Call to action generator",
            "system" => "<p>You are an expert at cold email outreach. </p><p> Our company, [[name & value prop]]. </p><p> Write a sentence asking the target company if they are interested in how we can help. Let them know we have [[value]] to share. Use a second person perspective. </p><p> This is the last sentence in the email so including a greeting is not necessary. </p><p> Customize the sentence to the target company by using the title and description provided in the user prompt. Reference their product but not their name. </p><p> Use best practices for cold email by only using 8 to 15 words.</p>",
            "user" => "<p>title: {{website_title}} </p><p> description: {{website_description}}</p>",
            "help_text" => "Creates a call to action customized to your prospect based on what they do and the value you're offering in exchange for their attention. The value can be in the form of ideas specific to their business or resources like a lead magnet."
        ),
        "idea_generator" => array(
            "name" => "Idea generator",
            "system" => "<p>You are an expert at [[delivery vehicle]]. </p><p> Our company, [[name & value prop]]. </p><p> Come up with one idea for the target company suggesting how they can [[result]]. </p><p> Return a sentence no longer than 24 words, include details about the target company provided in the user prompt. Write in a second person perspective starting with company can.",
            "user" => "<p>name: {{company}} </p><p> title: {{website_title}} </p><p> description: {{website_description}}</p>",
            "help_text" => "Generate an idea for your prospect by creating a use case for what you do and how you can help them specifically. Can be used by agencies, software companies and a variety of other B2B product/service providers to get a prospect's attention"
        ),
    );

    return $contents;
}

/*
 * List of allowed chatgpt models so we dont display all hundreds in the dropdown
 */
function allowedChatGptModels()
{
    return array(
        "gpt-3.5-turbo",
        "gpt-4",
        "gpt-4o-mini"
    );
}

/*
 * List of common company email that are not personal
 */
function commonCompanyEmailNames()
{
    return array(
        "team", "info", "contact", "support", "hello", "hi", "admin", "order", "orders", "editor", "customer", "service", "cs",
        "customerservice", "careers", "career", "frontdesk", "booking", "membership", "privacy", "email", "reception", "marketing",
        "reservation", "reservations", "sales", "sale", "inquires", "inquiry", "store", "stores"
    );
}

/*
 * List of consonants from the alphabet
 */
function consonants()
{
    return array(
        "b", "c", "d", "f", "g", "h", "j", "k", "l", "m", "n", "p", "q", "r", "s", "t", "v", "w", "x", "y", "z",
    );
}

/**
 * Remove url scheme and www
 */
function urlTrim($url)
{
    $url = explode('?', $url)[0];
    $url = trim(strtolower($url));
    $url = rtrim($url, '/');

    // check if starts with because "ltrim" check per letter, not the whole word
    if (Str::startsWith($url, 'https://')) {
        $url = Str::replaceFirst('https://', '', $url);
    }

    if (Str::startsWith($url, 'http://')) {
        $url = Str::replaceFirst('http://', '', $url);
    }

    if (Str::startsWith($url, 'www.')) {
        $url = Str::replaceFirst('www.', '', $url);
    }

    return $url;
}

function getMainDomain($url)
{
    $parts = explode('.', $url);
    $end = end($parts);
    
    // for domain with secondary tld like "squabstorage.co.uk"
    if(count($parts) > 2 && strlen($end) == 2) {
        return implode('.', array_slice($parts, -3));
    }

    return $url;
}