<?php

namespace App\Exports;

use App\Prospect;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;


class DataTablesExport implements FromQuery, WithHeadings, WithMapping
{
	use Exportable;


	public function __construct($filters, $agency_id)
    {
    	$this->filters = $filters;
    	$this->agency_id = $agency_id;

    	$intClient = data_get($this->filters, 'client');
    	$team = $intClient ? $intClient : null;

    	$this->defaultfields = [
            'email', 'first_name', 'last_name', 'emails_sent', 'last_contacted', 'company', 'industry', 'tags', 'website',
            'title', 'phone', 'address', 'city', 'state', 'country', 'status', 'interested', 'last_replied', 'campaign_id'
        ];
    }

    public function query()
    {
    	$intCompletedSteps = data_get($this->filters, 'completed_steps');
    	$completedSteps = $intCompletedSteps ? $intCompletedSteps : null;

    	$intCampaign = data_get($this->filters, 'campaign');
    	$campaign = $intCampaign ? $intCampaign : null;

    	$intClient = data_get($this->filters, 'client');
    	$team = $intClient ? $intClient : null;

    	$strStatus = data_get($this->filters, 'status');
    	$status = $strStatus ? $strStatus : 'all';

    	$strInterest = data_get($this->filters, 'interested');
    	$interest = $strInterest ? $strInterest : 'all';

    	$strSource = data_get($this->filters, 'source', 'contacts_page');
    	$strKeywords = data_get($this->filters, 'keywords', '');

    	$prospects = Prospect::ofKeywords($strKeywords, ['campaign_id' => $campaign, 'team_id' => $team, 'agency_id' => $this->agency_id])
    			->ofStatus($status)
                ->ofInterest($interest)
                ->ofCompletedSteps($completedSteps)
                ->ofCampaign($campaign)
                ->ofTeam($team)
                ->ofAgency($this->agency_id)
                ->select($this->defaultfields)
                ->orderBy('email');

        if($strSource == 'contacts_page'){
        	$prospects->whereHas('campaign', function ($q) {
                $q->where('status', '!=', 'DRAFT');
            });
        }

        return $prospects;
    }

    public function map($prospect): array
    {
    	$last_contacted = $prospect->last_contacted ?
                            $prospect->last_contacted->format('M jS Y, g:iA e') : '';

        $last_replied = $prospect->last_replied ?
                            $prospect->last_replied->format('M jS Y, g:iA e') : '';

    	$arrFormattedData = [
            $prospect->email,
			$prospect->first_name,
			$prospect->last_name,
			$prospect->company,
			$prospect->industry,
			$prospect->tags,
			$prospect->website,
			$prospect->title,
			$prospect->phone,
			$prospect->address,
			$prospect->city,
			$prospect->state,
			$prospect->country,
        ];

        array_push($arrFormattedData, $prospect->interested);
        array_push($arrFormattedData, $prospect->status);
        array_push($arrFormattedData, (string)$prospect->emails_sent); // 0 is empty in xls so cast to string
        array_push($arrFormattedData, $last_contacted);
        array_push($arrFormattedData, $last_replied);

        return $arrFormattedData;
    }

    public function headings(): array
    {
    	$headers = [];
        $fields = array_diff($this->defaultfields, ['last_contacted', 'emails_sent', 'status', 'interested', 'last_replied', 'campaign_id']);

        foreach ($fields as $field) {
            array_push($headers, ucwords(str_replace('_', ' ', $field)));
        }

        array_push($headers, 'Interested');
        array_push($headers, 'Status');
        array_push($headers, 'Emails Sent');
        array_push($headers, 'Last Contacted');
        array_push($headers, 'Last Replied');

        return $headers;
    }
}
