<?php

namespace App;

use App\Elastic\IndexConfigurators\ProspectIndexConfigurator;
use App\Elastic\SearchRules\ProspectSearchRule;
use App\Events\Prospect\ProspectSaved;
use App\Events\Prospect\ProspectSaving;
use App\Events\Prospect\ProspectUpdated;
use App\Traits\HasAgencyTenants;
use App\Traits\Hashidable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use ScoutElastic\Searchable;

class Prospect extends Model
{
    use Searchable, Hashidable, HasAgencyTenants;

    const STATUSES = [
        'OK',               // 'ACTIVE'
        'REPLIED',
        'UNSUBSCRIBED',     // 'INVALID'
        'BOUNCED',
        'AUTOREPLIED',
//        'MISSING-DATA',
        'STOPPED',           // stopped from zapier action
        'LI-REPLIED',
    ];

    const COMPLETED_STATUSES = [
        'REPLIED',
        'UNSUBSCRIBED',     // 'BLACKLIST', 'INVALID'
        'BOUNCED',
        'STOPPED',           // stopped from zapier action
        'LI-REPLIED',
    ];

    const INTEREST_LEVELS = [
        'POSITIVE',         // 'INTERESTED'
        'NEGATIVE',         // 'NOT INTERESTED'
        'NEUTRAL'           // 'MAYBE LATER'
    ];

    const LINKEDIN_CONNECT_STATUSES = [
        'PENDING',
        'ACCEPTED',             // liaccount and prospect already connected
        'FAILED',
        'EXTERNAL',             // connection request was already sent outside of wavo
        'LI-CONNECT-INCOMING',  // prospect has sent connection request to liaccount first
        // 'ACCEPTED',
        'OPEN',                 // prospect was set to 'Free to Open Profile'
    ];

    // If prospect gets this connect request status, then it should skip next LinkedIn steps,
    // and try other types of outreach if available
    const LINKEDIN_CONNECT_STATUSES_THAT_SKIP_LI = [
        'FAILED',
        'EXTERNAL'
    ];

    protected $guarded = ['id'];

    protected $casts = [
        'merge_fields' => 'array',
        'missing_fields' => 'array',
        'last_contacted' => 'datetime',
        'last_replied' => 'datetime',
        'last_opened' => 'datetime',
        'paused_until' => 'datetime',
        'wait_until' => 'datetime',
    ];

    protected $appends = [
        'full_name',
        'paused_date',
        'carbon_timezone',
        'last_contacted_date',
        'last_replied_date',
        'last_activity_date',
        'last_opened_date',
        'hashid',
        'linkedin_slug',
    ];

    /**
     * The event map for the model.
     *
     * @var array
     */
    protected $dispatchesEvents = [
        'updated' => ProspectUpdated::class,
        'saving' => ProspectSaving::class,
        'saved' => ProspectSaved::class,
    ];

    // Used to create the index in Elasticsearch
    protected $indexConfigurator = ProspectIndexConfigurator::class;

    // Default elastic scout search rule
    protected $searchRules = [ProspectSearchRule::class];

    // Field mapping for Elasticsearch.
    protected $mapping = ProspectIndexConfigurator::MAPPING;

    /**
     * Get the indexable data array for the model to be used by Laravel Scout.
     *
     * @return array
     */
    public function toSearchableArray()
    {
        return [
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'email' => $this->email,
            'company' => $this->company,
            'industry' => $this->industry,
            'website' => $this->website,
            'title' => $this->title,
            'phone' => $this->phone,
            'address' => $this->address,
            'city' => $this->city,
            'state' => $this->state,
            'country' => $this->country,
            'merge_fields' => collect($this->merge_fields)->values()->implode(', '),
            'agency_id' => $this->agency_id,
            'team_id' => $this->team_id,
            'campaign_id' => $this->campaign_id,
            'email_account_id' => $this->email_account_id,
        ];
    }

    /**
     * Get the prospect's full name.
     *
     * @return string
     */
    public function getFullNameAttribute()
    {
        return "{$this->first_name} {$this->last_name}";
    }

    /**
     * Format paused_until field for the frontend requirement.
     *
     * @return string
     */
    public function getPausedDateAttribute()
    {
        return $this->paused_until ? $this->paused_until->format('m/d/Y') : '';
    }

    /**
     * Add a field to return the timezone in Carbon format.
     *
     * @return string
     */
    public function getCarbonTimezoneAttribute()
    {
        return $this->timezone ? str_replace(' ', '_', $this->timezone) : config('app.timezone');
    }

    /**
     * Format last_contacted field for the frontend requirement.
     *
     * @return string
     */
    public function getLastContactedDateAttribute()
    {
        return $this->timezone && $this->last_contacted ? $this->last_contacted->tz($this->carbon_timezone)->format('M jS Y, g:iA') : '';
    }

    /**
     * Format last_replied field for the frontend requirement.
     *
     * @return string
     */
    public function getLastRepliedDateAttribute()
    {
        return $this->timezone && $this->last_replied ? $this->last_replied->tz($this->carbon_timezone)->format('M jS Y, g:iA') : '';
    }

    /**
     * Format last_opened field for the frontend format requirement.
     *
     * @return string
     */
    public function getLastOpenedDateAttribute()
    {
        return $this->timezone && $this->last_opened ? $this->last_opened->tz($this->carbon_timezone)->format('M jS Y, g:iA') : '';
    }

    /**
     * Format last_contacted field for the frontend requirement.
     *
     * @return string
     */
    public function getLastActivityDateAttribute()
    {
        $lastActivityDate = null;

        if ($this->last_replied && $this->last_contacted) {
            $lastActivityDate = $this->last_contacted->lt($this->last_replied) ? $this->last_replied : $this->last_contacted;
        } elseif ($this->last_contacted && ! $this->last_replied) {
            $lastActivityDate = $this->last_contacted;
        }

        return $lastActivityDate && $this->timezone ? $lastActivityDate->tz($this->carbon_timezone)->format('M jS Y, g:iA') : '';
    }

    /**
     * Get the Linkedin slug to view the profile.
     *
     * @return string|null
     */
    public function getLinkedinSlugAttribute()
    {
        if (! is_null($this->linkedin_public_url)) {
            return 'https://www.linkedin.com/in/' . $this->linkedin_public_url;
        }

        if (! is_null($this->linkedin_hash)) {
            return 'https://www.linkedin.com/in/' . $this->linkedin_hash;
        }

        return null;
    }

    public function setInterestedAttribute($value)
    {
        if (is_null($value)) {
            $this->attributes['interested'] = null;
        } elseif (in_array(strtoupper($value), self::INTEREST_LEVELS)) {
            $this->attributes['interested'] = strtoupper($value);
        }
    }

    public function setStatusAttribute($value)
    {
        if (in_array(strtoupper($value), self::STATUSES)) {
            $this->attributes['status'] = strtoupper($value);
        }
    }

    public function isActive(){
        return $this->status == 'OK' && ! $this->is_missing_data && ! $this->is_suppressed;
    }

    public function scopeActive($query)
    {
        return $query->where('status', '=', 'OK')->where('is_suppressed', '=', false)->where('is_missing_data', '=', false);
    }

    public function scopeInactive($query)
    {
        return $query->whereNotIn('status', ['OK']);
    }

    public function scopeNotScheduled($query)
    {
        return $query->whereNull('scheduled_email_template_id')->whereNull('schedule_id');
    }

    public function scopeWithoutQueuedMessages($query)
    {
        // TODO: move to pending_email_messages
        return $query->whereDoesntHave('emailMessages', function (Builder $q) {
            $q->whereNull('nylas_message_id')
                ->where('origin', 'self')
                ->whereNotNull('ee_queue_id')
                ->whereNull('ee_id');
        });
    }

    public function scopeCompleted($query)
    {
        return $query->where('is_complete', true);

//        return $query->where(function ($query) {
//            $query->whereIn('status', ['REPLIED', 'UNSUBSCRIBED', 'BOUNCED', 'STOPPED'])
//                ->orWhere(function ($query) {
//                    $query->where('status', 'AUTOREPLIED')->whereNull('paused_until');
//                })
//                ->orWhere('is_suppressed', true);
//        });
    }

    public function scopeIncomplete($query)
    {
        return $query->where('is_complete', false);

//        return $query->where(function ($query) {
//            $query->where('status', 'OK')
//                ->orWhere(function ($query) {
//                    $query->where('status', 'AUTOREPLIED')->whereNotNull('paused_until');
//                });
//        })->where('is_suppressed', false);
    }

    public function scopeContacted($query)
    {
        return $query->where('contacted', true);
    }

    /**
     * Filter prospects by campaign id.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $campaignId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfCampaign($query, $campaignId)
    {
        if ($campaignId) {
            return $query->where('campaign_id', $campaignId);
        }

        return $query;
    }

    /**
     * Filter prospects by amount of emails sent.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $amount
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfEmailsSent($query, $amount)
    {
        if ($amount !== 'all' && $amount !== null && $amount !== 'null') {
            return $query->where('emails_sent', $amount);
        }

        return $query;
    }

    /**
     * Filter prospects by current campaign stage.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $amount
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfCompletedSteps($query, $completedSteps)
    {
        if ($completedSteps !== 'all' && $completedSteps !== null && $completedSteps !== 'null') {
            return $query->where('completed_steps', $completedSteps);
        }

        return $query;
    }

    /**
     * Filter prospects by status.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $status
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfStatus($query, $status)
    {
        if ($status == 'invalids') {
            return $query->whereIn('status', ['INVALID', 'BOUNCED']);
        } elseif ($status != 'all') {
            return $query->where('status', $status);
        }

        return $query;
    }

    /**
     * Filter prospects by array of statuses.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param array $statuses
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfStatuses($query, $statuses)
    {
        if ($statuses != null || @count($statuses)) {
            return $query->whereIn('status', $statuses);
        }

        return $query;
    }

    /**
     * Filter prospects by import file.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $import
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfImport($query, $import)
    {
        if ($import == 'manual') {
            return $query->where('import_file', null);
        } elseif ($import != 'all') {
            return $query->where('import_file', $import);
        }

        return $query;
    }

    /**
     * Filter prospects by interested.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $interest
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfInterest($query, $interest)
    {
        if ($interest == null || $interest == 'null') {
            return $query->whereNull('interested');
        } elseif ($interest != 'all') {
            return $query->where('interested', $interest);
        }

        return $query;
    }

    public function scopeOfLinkedInConnectStatus($query, $linkedinConnectStatus)
    {
        if ($linkedinConnectStatus && $linkedinConnectStatus != 'null' && $linkedinConnectStatus != '') {
            return $query->where('linkedin_connect_status', $linkedinConnectStatus);
        }

        return $query;
    }

    /**
     * Filter out prospects without recent activity (OK & UNMARKED)
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfActivity($query)
    {
        return $query->where(function ($query) {
            $query->whereIn('status', ['REPLIED', 'LI-REPLIED', 'UNSUBSCRIBED', 'BOUNCED', 'AUTOREPLIED']);
        })->orwhere(function ($query) {
            $query->whereNotNull('interested');
        });
    }

    public function scopeOfMissingData($query, $missingData)
    {
        if (! is_null($missingData)) {
            return $query->where('is_missing_data', $missingData);
        }

        return $query;
    }

    public function scopeOfSuppressed($query, $suppressed)
    {
        if (! is_null($suppressed)) {
            return $query->where('is_suppressed', $suppressed);
        }

        return $query;
    }

    /**
     * Filter prospects by search keywords.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $keywords
     * @param array $filters
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfKeywords($query, $keywords, $filters = [])
    {
        if ($keywords) {

            if (config('scout.search')) {

                $scoutQuery = $this->search($keywords)->rule('App\Elastic\SearchRules\ProspectMinimumSearchRule');

                foreach ($filters as $key => $value) {
                    if (! empty($value)) {
                        if (is_array($value) || ($value instanceof Collection)) {
                            $scoutQuery = $scoutQuery->whereIn($key, $value);
                        } else {
                            $scoutQuery = $scoutQuery->where($key, $value);
                        }
                    };
                }

                $size = $scoutQuery->count();

                return $query->whereIn('id', $scoutQuery->take($size)->keys());
            }

            return $query->where('email', 'like', '%'.$keywords.'%')
                ->orWhere('first_name', 'like', '%'.$keywords.'%')
                ->orWhere('last_name', 'like', '%'.$keywords.'%')
                ->orWhere('merge_fields', 'like', '%'.$keywords.'%');
            //->whereRaw("MATCH (email) AGAINST ('{$keywords}')")
            //->where('email', 'like', '%'.$keywords.'%')
        }

        return $query;
    }

    /**
     * Filter prospects by their email messages.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $keywords
     * @param array $filters
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfEmailMessages($query, $keywords, $filters = [])
    {
        if ('' != $keywords) {

            if (config('scout.search')) {

                // Get prospects from the email messages.
                $scoutMessageQuery = EmailMessage::search($keywords);

                foreach ($filters as $key => $value) {
                    if (! empty($value)) {
                        $scoutMessageQuery = $scoutMessageQuery->where($key, $value);
                    };
                }

                $size = $scoutMessageQuery->count();

                $messageKeys = EmailMessage::whereIn('id', $scoutMessageQuery->take($size)->keys())
                    ->select('prospect_id')
                    ->pluck('prospect_id');

                // Also get prospects from the prospects table
                $scoutProspectQuery = $this->search($keywords)->rule('App\Elastic\SearchRules\ProspectInboxSearchRule');

                foreach ($filters as $key => $value) {
                    if (! empty($value)) {
                        $scoutProspectQuery = $scoutProspectQuery->where($key, $value);
                    };
                }

                $size = $scoutProspectQuery->count();

                $prospectKeys = $scoutProspectQuery->take($size)->keys();

                $keys = $messageKeys->merge($prospectKeys);

                return $query->whereIn('id', $keys);
            }

            return $query->whereHas('emailMessages', function ($q) use ($keywords) {
                $q->where('from_name', 'like', '%'.$keywords.'%')
                    ->orWhere('to_name', 'like', '%'.$keywords.'%')
                    ->orWhere('from', 'like', '%'.$keywords.'%')
                    ->orWhere('to', 'like', '%'.$keywords.'%')
                    ->orWhere('subject', 'like', '%'.$keywords.'%')
                    ->orWhere('message', 'like', '%'.$keywords.'%')
                    ->orWhere('snippet', 'like', '%'.$keywords.'%');
            })
                ->orwhere('first_name', 'like', '%'.$keywords.'%')
                ->orWhere('last_name', 'like', '%'.$keywords.'%')
                ->orWhere('company', 'like', '%'.$keywords.'%')
                ->orWhere('website', 'like', '%'.$keywords.'%')
                ->orWhere('email', 'like', '%'.$keywords.'%');
        }

        return $query;
    }

    /**
     * Filter prospects by their LinkedIn messages.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $keywords
     * @param array $filters
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfLinkedinMessages($query, $keywords, $filters = [])
    {
        if ('' != $keywords) {
            return $query->whereHas('linkedinMessages', function ($q) use ($keywords) {
                $q->where('from_name', 'like', '%'.$keywords.'%')
                    ->orWhere('to_name', 'like', '%'.$keywords.'%')
                    ->orWhere('message', 'like', '%'.$keywords.'%');
            })->orwhere('first_name', 'like', '%'.$keywords.'%')
                ->orWhere('last_name', 'like', '%'.$keywords.'%')
                ->orWhere('company', 'like', '%'.$keywords.'%')
                ->orWhere('website', 'like', '%'.$keywords.'%')
                ->orWhere('email', 'like', '%'.$keywords.'%');
        }

        return $query;
    }

    /**
     * Filter prospects by email account id.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $accountId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfAccount($query, $accountId)
    {
        if (0 != $accountId) {
            return $query->where('email_account_id', $accountId);
        }

        return $query;
    }

    public function scopeOfLinkedinAccount($query, $accountId)
    {
        if (0 != $accountId) {
            return $query->where('linkedin_account_id', $accountId);
        }

        return $query;
    }

    /**
     * Filter prospects by agency id.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $agencyId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfAgency($query, $agencyId)
    {
        if (0 != $agencyId) {
            return $query->where('agency_id', $agencyId);
        }

        return $query;
    }

    /**
     * Filter prospects by team id.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $teamId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfTeam($query, $teamId)
    {
        if (0 != $teamId) {
            return $query->where('team_id', $teamId);
        }

        return $query;
    }

    /**
     * Add scope to prospects based on declared filters that are used in current request.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param \App\ProspectFilters $filters
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFilter($query, ProspectFilters $filters)
    {
        return $filters->apply($query);
    }

    /**
     * return prospect that are linkedin profile
     * ie. not cancelled and authenticated.
     *
     * @param $query
     * @return mixed
     */
    public function scopeLinkedinProspect($query)
    {
        return $query->where(function($query) {
            return $query->whereNotNull('linkedin_hash')->orWhereNotNull('linkedin_public_url');
        });
    }

    /**
     * Return prospect that have valid email
     * ie. not cancelled and authenticated.
     *
     * @param $query
     * @return mixed
     */
    public function scopeEmailProspect($query)
    {
        return $query->whereNotNull('email');
    }

    public function scopeEmailOnly($query)
    {
        return $query->whereNotNull('email')->whereNull('linkedin_hash');
    }

    public function scopeLinkedinOnly($query)
    {
        return $query->whereNotNull('linkedin_hash')->whereNull('email');
    }

    /**
     * A prospect belongs to an agency.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function agency()
    {
        return $this->belongsTo(Agency::class);
    }

    /**
     * A prospect belongs to a campaign. It is a campaign prospect.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function campaign()
    {
        return $this->belongsTo(Campaign::class);
    }

    /**
     * A prospect belongs to a team.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function team()
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * A prospect is a contact used for a specific campaign.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function contact()
    {
        return $this->belongsTo(Contact::class);
    }

    /**
     * A prospect can be in many email threads (or in just one?).
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function emailThreads()
    {
        return $this->hasMany(EmailThread::class);
    }

    /**
     * A prospect can have many email messages.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function emailMessages()
    {
        return $this->hasMany(EmailMessage::class);
    }

    public function linkedinThreads()
    {
        return $this->hasMany(LinkedinThread::class);
    }

    public function linkedinMessages()
    {
        return $this->hasMany(LinkedinMessage::class);
    }

    /**
     * We are logging the prospect activity.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function prospectActivities()
    {
        return $this->hasMany(ProspectActivity::class);
    }

    /**
     * This prospect can be bound to a day's schedule through the scheduled_prospects table.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function scheduledProspect()
    {
        return $this->hasOne(ScheduledProspect::class);
    }

    public function pendingEmailMessages()
    {
        return $this->hasMany(PendingEmailMessage::class);
    }
}
