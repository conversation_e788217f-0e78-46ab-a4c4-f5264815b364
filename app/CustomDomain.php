<?php

namespace App;

use App\Traits\HasTeamTenants;
use Illuminate\Database\Eloquent\Model;

/*
 * Custom unsubscribe domain
 */
class CustomDomain extends Model
{
    use HasTeamTenants;

    protected $guarded = ['id'];

    /**
     * Team of this custom domain.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function team()
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * Filter custom_domains by type. 'custom: domains by agency', 'default: platform domains'
     *
     * @param mixed $query
     * @param mixed $intTeamId
     * @return mixed $query
     */
    public function scopeOfType($query, $strType)
    {
        if($strType == 'custom')
        {
            return $query->whereNotNull('team_id');
        }
        else
        {
            return $query->whereNull('team_id');
        }
    }

    /**
     * Filter custom_domains by team id.
     *
     * @param mixed $query
     * @param mixed $intTeamId
     * @return mixed $query
     */
    public function scopeOfTeam($query, $intTeamId)
    {
        if($intTeamId)
        {
            return $query->where('team_id', $intTeamId);
        }
        else
        {
            return $query;
        }
    }

    /**
     * Filter custom_domains by array of team ids.
     *
     * @param mixed $query
     * @param mixed $intTeamId
     * @return mixed $query
     */
    public function scopeOfTeams($query, $arrTeamIds)
    {
        if(@count($arrTeamIds))
        {
            return $query->whereIn('team_id', $arrTeamIds);
        }
        else
        {
            return $query;
        }
    }
}
