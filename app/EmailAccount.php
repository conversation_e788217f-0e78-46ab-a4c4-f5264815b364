<?php

namespace App;

use App\Elastic\IndexConfigurators\EmailAccountIndexConfigurator;
use App\Elastic\SearchRules\EmailAccountSearchRule;
use App\Exceptions\StripeException;
use App\Jobs\Email\CheckEmailSchedules;
use App\Jobs\Email\GetEmailFolders;
use App\Jobs\Email\RescheduleEmailAccount;
use App\Jobs\Email\EmailEngineSendConnectionTest;
use App\Jobs\Warmup\CheckWarmupSchedules;
use App\Jobs\Warmup\StopWarmup;
use App\Mail\NylasPaymentRequiredNotification;
use App\Services\AccountBillingService;
use App\Services\CampaignSchedulerService;
use App\Services\EmailIntegrationServices\EmailEngineApiService;
use App\Services\EmailIntegrationServices\EmailIntegrationFactory;
use App\Services\EmailIntegrationServices\NylasApiService;
use App\Services\GoogleAuthService;
use App\Traits\HasAgencyTenants;
use App\Traits\Hashidable;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use ScoutElastic\Searchable;

class EmailAccount extends Model
{
    use Searchable, Hashidable, HasAgencyTenants, HasFactory;

    public const EMAIL_SERVER_TYPES = [
        'gmail' => 'Gmail OAuth', //'Gmail API',
        'gmail_imap' => 'Gmail Basic', //'Gmail IMAP',
        'office365' => 'Office365 OAuth',
        'outlook' => 'Office365 Basic',
        // 'exchange' => 'Exchange Server',
        'imap' => 'IMAP',
        // 'yahoo' => 'Yahoo',
        // 'aol' => 'AOL',
    ];

    public const INTEGRATIONS = [
        'nylas' => 'Nylas API',
        'ee' => 'EmailEngine API',
    ];

    protected $guarded = ['id'];

    protected $appends = [
        'full_name',
        'nice_signature',
        'email_with_alias',
        'hashid',
        'email_server_type_nice_name',
        'email_server_service_name',
    ];

    protected $casts = [
        'email_server_ssl' => 'boolean',
        'email_server_ssl_smtp' => 'boolean',
        'sync_paused_until' => 'datetime',
    ];

    // Used to create the index in Elasticsearch.
    protected $indexConfigurator = EmailAccountIndexConfigurator::class;

    // Default elastic scout search rule.
    protected $searchRules = [EmailAccountSearchRule::class];

    // Field mapping for Elasticsearch.
    protected $mapping = EmailAccountIndexConfigurator::MAPPING;

    protected const DB_READ_CONNECTION = 'mysql::read';

    /**
     * Get the indexable data array for the model to be used by Laravel Scout.
     *
     * @return array
     */
    public function toSearchableArray()
    {
        return [
            'email_address' => $this->email_address,
            'full_name'     => $this->full_name,
            'team_id'       => $this->team_id,
            'agency_id'     => $this->agency_id,
        ];
    }

    /**
     * Add a field to return the full name used in emails.
     *
     * @return string
     */
    public function getFullNameAttribute()
    {
        return trim($this->sender_title.' '.$this->sender_first_name.' '.$this->sender_last_name);
    }

    /**
     * Return signature string transforming the "{{UNSUBSCRIBE}}" to fix interpolation issue in VUE,
     * or show `No Signature Available` message.
     *
     * @return mixed|string
     */
    public function getNiceSignatureAttribute()
    {
        return $this->signature ? str_replace('{{UNSUBSCRIBE}}', '#', $this->signature) : 'No Signature Available';
    }

    /**
     * Return the email address with the alias shown first, if an alias is enabled.
     *
     * @return mixed|string
     */
    public function getEmailWithAliasAttribute()
    {
        return $this->alias_enabled ? $this->alias_email_address . ' ('. $this->email_address .')' : $this->email_address;
    }

    public function getNylasProviderAttribute()
    {
        return $this->email_server_type == 'gmail_imap' ? 'imap' : $this->email_server_type;
    }

    public function getEmailEngineProviderAttribute()
    {
        return $this->email_server_type == 'gmail_imap' ? 'imap' : $this->email_server_type;
    }

    public function getEmailServerTypeNiceNameAttribute()
    {
        return static::EMAIL_SERVER_TYPES[$this->email_server_type] ?? $this->email_server_type;
    }

    public function getIntegrationType()
    {
        return static::INTEGRATIONS[$this->integration_type] ?? $this->integration_type;
    }

    // detect email if gmail/gsuite/outlook/office365/other
    public function getEmailServerServiceNameAttribute()
    {
        if(in_array($this->email_server_type, ["gmail", "gmail_imap"]) || $this->email_server_imap_address == "imap.gmail.com") {

            return Str::endsWith($this->email_address, "@gmail.com") ? "gmail" : "gsuite";

        } elseif ($this->email_server_type == "outlook" || $this->email_server_imap_address == "outlook.office365.com") {

            $msFreeEmails = ["@outlook.com", "@hotmail.com"];
            return Str::endsWith($this->email_address, $msFreeEmails) ? "outlook" : "office365";

        } elseif ($this->email_server_type == "office365") {

            return "office365";

        }

        return "other";
    }

    /**
     * Filter email_accounts by team id.
     *
     * @param mixed $query
     * @param mixed $intTeamId
     * @return mixed $query
     */
    public function scopeOfTeam($query, $intTeamId)
    {
        if ($intTeamId) {
            return $query->where('team_id', $intTeamId);
        }

        return $query;
    }

    /**
     * Filter email_accounts by team id.
     *
     * @param mixed $query
     * @param mixed $arrTeamIds
     * @return mixed $query
     */
    public function scopeOfTeams($query, $arrTeamIds)
    {
        if (@count($arrTeamIds)) {
            return $query->whereIn('team_id', $arrTeamIds);
        }

        return $query;
    }

    /**
     * Filter email_accounts by agency.
     *
     * @param mixed $query
     * @param int $agencyId
     * @return mixed $query
     */
    public function scopeOfAgency($query, $agencyId)
    {
        if ($agencyId) {
            return $query->where('agency_id', $agencyId);
        }

        return $query;
    }

    /**
     * Filter email_accounts by status (active || inactive).
     *
     * @param mixed $query
     * @param mixed $strStatus
     * @return mixed $query
     */
    public function scopeOfStatus($query, $strStatus)
    {
        if ('active' == $strStatus) {
            return $query->where('is_cancelled', false)->where('is_authenticated', true);
        } elseif ('inactive' == $strStatus) {
            return $query->where('is_cancelled', true)->where('is_authenticated', true);
        }

        return $query;
    }

    /**
     * Filter email_accounts by server type.
     *
     * @param mixed $query
     * @param mixed $strServer
     * @return mixed $query
     */
    public function scopeOfServer($query, $strServer)
    {
        if ($strServer) {
            return $query->where('email_server_type', $strServer);
        }

        return $query;
    }

    /**
     * Filter email_accounts by integration_type.
     *
     * @param mixed $query
     * @param mixed $integrationType
     * @return mixed $query
     */
    public function scopeOfIntegrationType($query, $integrationType)
    {
        if (in_array(strtolower($integrationType), ['ee', 'nylas'])) {
            return $query->where('integration_type', $integrationType);
        }

        return $query;
    }

    /**
     * Filter email accounts by search keywords.
     *
     * @param  mixed  $query
     * @param  mixed  $strKeywords
     * @param  array  $filters
     * @return mixed $query
     */
    public function scopeOfKeywords($query, $strKeywords, $filters = [])
    {
        if ($strKeywords) {

            if (config('scout.search')) {

                $scoutQuery = $this->search($strKeywords);

                foreach ($filters as $key => $value) {
                    if (!empty($value)) {
                        $scoutQuery = $scoutQuery->where($key, $value);
                    };
                }

                $size = $scoutQuery->count();

                return $query->whereIn('id', $scoutQuery->take($size)->keys());
            }

            return $query->where('email_address', 'like', '%'.$strKeywords.'%');
        }
    }

    /**
     * Return active email accounts.
     * ie. not cancelled and authenticated.
     *
     * @param $query
     * @return mixed
     */
    public function scopeActive($query)
    {
        return $query->where('is_cancelled', false)->where('is_authenticated', true);
    }

    /**
     * Get all the running email accounts.
     * Those that have a nylas/emailengine account, are not cancelled nor stopped due to error.
     *
     * @param $query
     * @return mixed
     */
    public function scopeRunning($query)
    {
        return $query->where('is_cancelled', false)
            ->whereNull('error')
            ->whereHas('emailEngineAccount', function($query) {
                $query->running();
            });
//            ->where(function ($query) {
//                $query->whereHas('nylasAccount', function($query) {
//                    $query->running();
//                })
//                ->orWhereHas('emailEngineAccount', function($query) {
//                    $query->running();
//                });
    }

    /**
     * Filter email_accounts to those not currently frozen.
     *
     * @param $query
     * @return mixed
     */
    public function scopeNotFrozen($query)
    {
        $time = Carbon::now();

        return $query->whereDoesntHave('freezes', function ($query) use ($time) {
            $query->where('start_at', '<=', $time)->where('end_at', '>=', $time);
        });
    }

    public function scopeSendsWarmupMessages($query)
    {
        return $query->where('sends_warmup_messages', true);
    }

    /**
     * Filter email_accounts to those that currently have running campaigns.
     *
     * @param $query
     * @return mixed
     */
    public function scopeWithRunningCampaigns($query)
    {
        return $query->whereHas('campaigns', function ($query) {
            $query->running();
        });
    }

    public function scopeOfNylas($query)
    {
        return $query->where('integration_type', 'nylas');
    }

    public function scopeOfEmailEngine($query)
    {
        return $query->where('integration_type', 'ee');
    }

    /**
     * Each Email Account can be used in many Campaigns and each campaign can have many email accounts.
     * Inactive campaign email accounts (that are removed from a campaign) have an `active` = false pivot value.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function campaigns()
    {
        return $this->belongsToMany(Campaign::class)
            ->withPivot('active')
            ->withTimestamps()
            ->wherePivot('active', true);
    }

    public function inactiveCampaigns()
    {
        return $this->belongsToMany(Campaign::class)
            ->withPivot('active')
            ->withTimestamps()
            ->wherePivot('active', false);
    }

    public function allCampaigns()
    {
        return $this->belongsToMany(Campaign::class)
            ->withPivot('active')
            ->withTimestamps();
    }

    /**
     * These are campaigns that are displayed in the list by default.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function listedCampaigns()
    {
        return $this->campaigns()->whereNotIn('status', ['ARCHIVED']);
    }

    /**
     * Each Email Account can have a freezing period.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function freezes()
    {
        return $this->hasMany(EmailFreeze::class);
    }

    /**
     * Each email account can use one nylas account.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function nylasAccount()
    {
        return $this->hasOne(NylasAccount::class);
    }

    /**
     * Each email account can use one emainengine account.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function emailEngineAccount()
    {
        return $this->hasOne(EmailEngineAccount::class);
    }

    public function mailReachAccount()
    {
        return $this->hasOne(MailReachAccount::class);
    }

    /**
     * Each email account has warmup configuration.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function emailWarmup()
    {
        return $this->hasOne(EmailWarmup::class);
    }

    /**
     * Each email account can have many email schedules.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function schedules()
    {
        return $this->hasMany(Schedule::class);
    }

    public function agency()
    {
        return $this->belongsTo(Agency::class);
    }

    public function team()
    {
        return $this->belongsTo(Team::class);
    }

    public function emailConfiguration()
    {
        return $this->belongsTo(EmailConfiguration::class);
    }

    public function emailMessages()
    {
        return $this->hasMany(EmailMessage::class);
    }

    public function emailThreads()
    {
        return $this->hasMany(EmailThread::class);
    }

    public function emailFolders()
    {
        return $this->hasMany(EmailFolder::class);
    }

    public function dailyStats()
    {
        return $this->hasMany(DailyStats::class);
    }

    /**
     * Get the owner of the email account.
     */
    public function owner()
    {
        return $this->belongsTo(Spark::userModel(), 'owner_id');
    }

    public function postponedEEWebhooks()
    {
        return $this->hasMany(PostponedEEWebhook::class);
    }

    public function failedEmailMessages()
    {
        return $this->hasMany(FailedEmailMessage::class);
    }

    public function emailMessageTests()
    {
        return $this->hasMany(EmailMessageTest::class);
    }

    public function incomingEmailMessages()
    {
        return $this->hasMany(IncomingEmailMessage::class);
    }

    /**
     * Each email account has warmupInbox schedules.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function warmupSchedules()
    {
        return $this->hasMany(WarmupSchedule::class);
    }

    /**
     * Each email account has warmupInbox calendar.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function warmupCalendar()
    {
        return $this->hasOne(WarmupCalendar::class);
    }

    public function warmupStats()
    {
        return $this->hasOne(WarmupStat::class);
    }

    public function warmupDailyStats()
    {
        return $this->hasMany(WarmupDailyStats::class);
    }

    public function warmupLatestStats()
    {
        $startDay = now()->subDays(14);

        return $this->hasMany(WarmupDailyStats::class)
            ->where('day', '>=', $startDay);
    }

    public function sentWarmupMessages()
    {
        return $this->hasMany(WarmupMessage::class, 'email_account_id');
    }

    public function receivedWarmupMessages()
    {
        return $this->hasMany(WarmupMessage::class, 'recipient_account_id');
    }

    public function sentWarmupInteractions()
    {
        return $this->hasMany(WarmupInteraction::class, 'email_account_id');
    }

    public function receivedWarmupInteractions()
    {
        return $this->hasMany(WarmupInteraction::class, 'recipient_account_id');
    }

    /**
     * Get a random interval in seconds based on email account's min and max interval.
     *
     * @return int
     */
    public function getRandomInterval()
    {
        $minInterval = $this->warmup_status == 'on' ? $this->emailWarmup->min_interval : $this->min_interval;
        $maxInterval = $this->warmup_status == 'on' ? $this->emailWarmup->max_interval : $this->max_interval;
        $throttles = (int) getRedisCounter('account_throttles', $this)->first();
        $throttleInterval = $throttles <= 20 ? $throttles * 60 : 1200;

        return randomWeightedLow($minInterval, $maxInterval) + $throttleInterval;
    }

    /**
     * Calculate a maximum amount of emails that can be sent per hour.
     *
     * @return integer
     */
    public function getMaxEmailsPerHour()
    {
        $minInterval = $this->warmup_status == 'on' ? $this->emailWarmup->min_interval : $this->min_interval;
        $maxInterval = $this->warmup_status == 'on' ? $this->emailWarmup->max_interval : $this->max_interval;
        $sendLimit = $this->warmup_status == 'on' ? $this->emailWarmup->send_limit : $this->send_limit;

        $averageSendTime = ($minInterval + $maxInterval) / 2 + 5;
        $hourlyEmails = floor(3600 / $averageSendTime);

        return $hourlyEmails < $sendLimit ? $hourlyEmails : $sendLimit;
    }

    /**
     * Calculate a maximum amount of emails that can be sent per hour.
     *
     * @return integer
     */
    public function getMaxEmailsPerDay()
    {
        $minInterval = $this->warmup_status == 'on' ? $this->emailWarmup->min_interval : $this->min_interval;
        $maxInterval = $this->warmup_status == 'on' ? $this->emailWarmup->max_interval : $this->max_interval;
        $sendLimit = $this->warmup_status == 'on' ? $this->emailWarmup->send_limit : $this->send_limit;

        $averageSendTime = ($minInterval + $maxInterval) / 2 + 5;
        $dailyEmails = floor(24 * 3600 / $averageSendTime);

        return $dailyEmails < $sendLimit ? $dailyEmails : $sendLimit;
    }

    /**
     * Get daily emails sent counter from Redis.
     *
     * @return int
     */
    public function getEmailsSentCounter()
    {
        return (int) getRedisCounter('emails_sent', $this)->first();
    }

    /**
     * Check that an email account has running campaigns.
     *
     * @return bool
     */
    public function hasRunningCampaigns()
    {
        if ($this->relationLoaded('campaigns')) {

            return $this->campaigns->where('status','RUNNING')->where('is_ready', true)->count() > 0;
        }

        return $this->campaigns()->running()->count() > 0;
    }

    /**
     * Check that an email account has not reached its daily email send limit.
     *
     * @return bool
     */
    public function hasNotReachedDailyLimit()
    {
        $sendLimit = $this->warmup_status == 'on' ? $this->emailWarmup->send_limit : $this->send_limit;
        //return $this->emails_sent < $sendLimit;

        return $this->getEmailsSentCounter() < $sendLimit;
    }

    /**
     * See if this email account is queued for checking email sending schedules.
     *
     * @param $jobId  the id of the job if it is currently running
     * @return bool
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function isCheckingSchedules($jobId = null)
    {
        return getPendingJobTagIds('schedule', 'App\Jobs\Email\CheckEmailSchedules', 'email-account-', $jobId)->contains($this->id);
    }

    public function isCheckingWarmupSchedules($jobId = null)
    {
        return getPendingJobTagIds('schedule', 'App\Jobs\Warmup\CheckWarmupSchedules', 'email-account-', $jobId)->contains($this->id);
    }

    /**
     * Check if this email account is currently active.
     *
     * @return bool
     */
    public function isActive()
    {
        return is_null($this->cancelled_at) && !is_null($this->authenticated_at);
    }

    /**
     * Check if the email account is frozen now or on a given datetime.
     *
     * @param null $time
     * @return bool
     */
    public function isFrozen($time = null)
    {
        if (null === $time) {
            $time = Carbon::now();
        } elseif (is_string($time)) {
            $time = Carbon::parse($time);
        }

        if ($this->relationLoaded('freezes')) {
            return $this->freezes
                ->where('start_at', '<=', $time)
                ->where('end_at', '>=', $time)
                ->count() > 0;
        }

        return $this->freezes()
                ->where('start_at', '<=', $time)
                ->where('end_at', '>=', $time)
                ->exists();
    }

    /**
     * Check an email account is running and can send emails.
     *
     * @return bool
     */
    public function isRunning()
    {
        if($this->integration_type == 'nylas') {
            return $this->isRunningNylas();
        } else {
            return $this->isRunningEmailEngine();
        }
    }

    /**
     * Check an email account is running and can send emails.
     *
     * @return bool
     */
    public function isRunningEmailEngine()
    {
        if ($this->relationLoaded('emailEngineAccount')) {
            return is_null($this->cancelled_at) && is_null($this->error) && !is_null($this->emailEngineAccount) && $this->emailEngineAccount->isRunning();
        }

        return is_null($this->cancelled_at) && is_null($this->error) && $this->emailEngineAccount()->running()->exists();
    }

    /**
     * Check an email account is running and can send emails.
     *
     * @return bool
     */
    public function isRunningNylas()
    {
        if ($this->relationLoaded('nylasAccount')) {
            return is_null($this->cancelled_at) && is_null($this->error) && !is_null($this->nylasAccount) && $this->nylasAccount->isRunning();
        }

        return is_null($this->cancelled_at) && is_null($this->error) && $this->nylasAccount()->running()->exists();
    }

    /**
     * Start sending emails through this account.
     * Dispatch job for checking schedules.
     *
     * @param int $delay
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function startSending($delay = 0)
    {
        if (!$this->isCheckingSchedules()) {
            $logChannel = $this->integration_type == 'ee' ? 'emailengine' : 'nylas';

            Log::channel($logChannel)
                ->info('Dispatch schedule checking for email account: '.$this->id);
            CheckEmailSchedules::dispatch($this->id)
                ->onQueue('schedule')
                ->delay($delay);
        }
    }

    public function startSendingWarmup($delay = 0)
    {
        if (!$this->isCheckingWarmupSchedules()) {
            Log::channel('emailengine')
                ->info('Dispatch warmup schedule checking for email account: '.$this->id);
            CheckWarmupSchedules::dispatch($this->id)
                ->onQueue('schedule')
                ->delay(now()->addSeconds($delay));
        }
    }

    /**
     * Activate an email account and update subscription quantity.
     * DEPRECATED
     *
     * @return EmailAccount
     * @throws Exception
     */
    public function activate()
    {
        $nylas = new NylasApiService($this);

        // First, check the email account credentials.
        try {
            $nylasAuthCode = $nylas->authorize();
        } catch (\Throwable $e) {
            $errorMessage = $e->getMessage();
            Log::error($errorMessage);

            if (Str::contains($errorMessage, '402 PAYMENT REQUIRED')) {
                Mail::to(Role::where('name', 'admin')->first()->users)
                    ->queue(new NylasPaymentRequiredNotification($errorMessage, $this->email_address));
            }

            throw new Exception('Invalid credentials, please try again.');
        }

        // Then, update subscription on Stripe.
        $billingService = new AccountBillingService($this->owner, 'default');
        $billingSuccess = $billingService->activateAccount($this);

        if (! $billingSuccess) {

            throw new StripeException('Cannot update subscription. Please check your billing information.');
        }

        // Subscription created/updated on stripe, now create account on nylas.
        try {
            if (null === $this->cancelled_at) {
                $nylas->updateOrCreateAccountWithAuthCode($nylasAuthCode);
            } else {
                $nylas->reactivateAccount($nylasAuthCode);
            }
        } catch (\Throwable $e) {
            Log::error($e->getMessage());

            throw new Exception('Cannot create email inbox');
        }

        $this->update([
            'authenticated_at' => now(),
            'is_authenticated' => true,
            'error'            => null,
            'restart_count'    => 0
        ]);
        resetRedisCounter('api_errors', $this);
        resetRedisCounter('account_throttles', $this);
        GetEmailFolders::dispatch($this)->delay(now()->addMinutes(5));
        RescheduleEmailAccount::dispatch($this);

        return $this;
    }

    /**
     * Deactivate an email account and update subscription quantity.
     *
     * @return bool
     * @throws \Exception
     */
    public function deactivate()
    {
        if (($this->integration_type == 'nylas' && $this->nylasAccount()->exists()) || ($this->integration_type == 'ee' && $this->emailEngineAccount()->exists())) {
            $factory = new EmailIntegrationFactory;
            $emailApi = $factory->make($this);

            try {
                $emailApi->cancelAccount();
            } catch (Exception $e) {
                Log::error($e->getMessage());

                return false;
            }
        } else {
            $this->update([
                'cancelled_at' => now(),
                'is_cancelled' => true,
                'sends_warmup_messages' => false,
            ]);
        }

        $billingService = new AccountBillingService($this->owner, 'default');
        $billingSuccess = $billingService->deactivateAccount($this);

        if (!$billingSuccess) {

            return false;
        }

        // In case of gmail, also revoke token from google api.
        if ($this->email_server_type == "gmail") {
            try {
                $api = new GoogleAuthService();
                $api->revokeToken($this);
            } catch (\Throwable $e) {
                Log::error($e->getMessage());
            }
        }

        StopWarmup::dispatch($this)->delay(now()->addSeconds(10));

        return true;
    }

    /**
     * Activate an email account and update subscription quantity.
     *
     * @return EmailAccount
     * @throws Exception
     */
    public function activateEmailEngine()
    {
        $emailEngine = new EmailEngineApiService($this);

        // First, check the email account credentials.
        try {
            $emailEngineAuthVerify = $emailEngine->authorize();
        } catch (\Throwable $e) {
            $errorMessage = $e->getMessage();
            Log::error($errorMessage);

            throw new Exception('Invalid credentials, please try again.');
        }

        // determine if all success $emailEngineAuthVerify
        if ($this->email_engine_provider == 'imap' &&
            (empty($emailEngineAuthVerify['imap']['success']) || !$emailEngineAuthVerify['imap']['success'])
        ) {
            Log::error($emailEngineAuthVerify);
            throw new Exception('Invalid credentials, please try again.');
        }

        // Then, update subscription on Stripe.
        $billingService = new AccountBillingService($this->owner, 'default');
        $billingSuccess = $billingService->activateAccount($this);

        if (! $billingSuccess) {
            $errorMsg = 'Cannot update subscription. Please check your billing information or contact support.';
            if ($this->agency->wavo_version == 3) {
                $errorMsg = 'Cannot activate account. Please contact support.';
            }

            throw new StripeException($errorMsg);
        }

        // Subscription created/updated on stripe, now create account on emailengine.
        try {
            if (null === $this->cancelled_at) {
                $emailEngine->updateOrCreateAccount();
            } else {
                $emailEngine->reactivateAccount();
            }
        } catch (\Throwable $e) {
            Log::error($e->getMessage());

            throw new Exception('Cannot create email inbox');
        }

        $this->update([
            'cancelled_at'     => null,
            'is_cancelled'     => false,
            'authenticated_at' => now(),
            'is_authenticated' => true,
            'error'            => 'account.connecting',
            'restart_count'    => 0,
            'sync_paused_until'=> null,
        ]);
        resetRedisCounter('api_errors', $this);
        resetRedisCounter('sync_errors', $this);
        resetRedisCounter('account_throttles', $this);
        GetEmailFolders::dispatch($this)->delay(now()->addMinutes(5));

        Log::info('Authenticate by user Success, email-account-'.$this->id.', EE account-'.$this->emailEngineAccount->account_id);

        // after the account was successfully activated (IMAP sync check)
        // let's verify that the SMTP is working
        EmailEngineSendConnectionTest::dispatch($this->id)
                ->onQueue('api')
                ->delay(now()->addMinutes(1));

        return $this;
    }

    /**
     * Remove the email account from all or some campaigns.
     *
     * @param  int|array  $campaignId
     */
    public function removeFromCampaigns($campaignId = null)
    {
        if (is_null($campaignId)) {
            $campaigns = $this->campaigns()->pluck('id');
        } else {
            $campaigns = collect($campaignId); // Turn it into collection from int or array.
        }

        $campaigns->each(function ($id) {
            $this->campaigns()->updateExistingPivot($id, ['active' => false]);
        });
        flushModelCache('campaign');
    }

    /**
     * Add the email account to a campaign.
     *
     * @param $campaignId
     */
    public function addToCampaign($campaignId)
    {
        if ($this->inactiveCampaigns()->where('id', $campaignId)->exists()) {
            $this->inactiveCampaigns()->updateExistingPivot($campaignId, ['active' => true]);
        } elseif ($this->campaigns()->where('id', $campaignId)->doesntExist()) {
            $this->campaigns()->attach($campaignId, ['active' => true]);
        }
    }

    /**
     * Detach any belongsToMany related models and
     * destroy the email account if it has no linked data.
     */
    public function detachRelatedModelsAndDestroyIfUnused()
    {
        // Move account to campaigns' previous(inactive) email accounts.
        $this->removeFromCampaigns();

        // Remove it from any prospects.
        //Prospect::where('email_account_id', '=', $this->id)->update([
        //    'email_account_id' => null
        //]);

        if ($this->nylasAccount()->exists() && $this->integration_type === 'nylas') {
            $nylas = new NylasApiService($this);

            try {
                $nylas->revokeTokens();
            } catch (\Exception $exception) {
                Log::warning("Revoking tokens for EmailAccount-{$this->id} resulted in Nylas error: {$exception->getMessage()}");
            }
        }

        DB::beginTransaction();
        try {
            $this->freezes()->delete();  // Mass Delete
            flushModelCache('email_freeze');
            $this->campaigns()->running()->each(function ($campaign) {
                $campaignScheduler = new CampaignSchedulerService($campaign);
                $campaignScheduler->removeEmailAccount($this);
            });

            // If it has no linked messages, clear any other relations and delete.
            if (
                $this->emailMessages()->count() == 0 &&
                $this->emailThreads()->count() == 0 &&
                $this->sentWarmupMessages()->count == 0 &&
                $this->receivedWarmupMessages()->count() == 0
            ) {
                // Warmup related
                $this->warmupCalendar()->delete();
                $this->warmupStats()->delete();
                $this->warmupDailyStats()->delete();
                $this->inactiveCampaigns()->sync([]);
                if ($this->nylasAccount()->exists()) {
                    $this->nylasAccount->delete();
                }
                $this->delete();
            }

            DB::commit();
        } catch (\Throwable $e) {
            DB::rollBack();
            Log::error('Error removing email account:'.$this->id.'. '.$e->getMessage());
        }
    }

    /**
     * Check if email account has warmup 'on'
     * Then enable/continue its paused warmup configuration settings
     */
    public function enablePausedWarmup()
    {
        if($this->warmup_status == 'on' && $this->emailWarmup->warmup_status == 'off') {
            $this->emailWarmup->update([
                'warmup_status' => 'on',
                'last_started_at' => Carbon::now()
            ]);
        }
    }

    /**
     * Check if email account has warmup 'on'
     * Then turn off its running warmup configuration settings
     */
    public function pauseRunningWarmup()
    {
        if($this->warmup_status == 'on' && !$this->hasRunningCampaigns()
            && $this->emailWarmup->warmup_status == 'on'
        ) {
            $this->emailWarmup->update([
                'warmup_status' => 'off',
                'last_paused_at' => Carbon::now()
            ]);
        }
    }
}
