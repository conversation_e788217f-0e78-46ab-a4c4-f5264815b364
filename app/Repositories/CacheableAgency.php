<?php

namespace App\Repositories;

use App\Agency;
use Illuminate\Support\Facades\Cache;

class CacheableAgency
{
    protected $cache;
    protected $lifetime;

    public function __construct()
    {
        $this->lifetime = config('cache.model.lifetime');
        $this->cache = Cache::store(config('cache.model.driver'))->tags('agency');
    }

    public function all()
    {
        return $this->cache->remember("agency:all", $this->lifetime, function () {
            return Agency::all();
        });
    }

    public function find($id)
    {
        return $this->cache->remember("agency:$id", $this->lifetime, function () use ($id) {
            return Agency::find($id);
        });
    }

    public function getByDomain($domain)
    {
        $agency = $this->all()->where('domain', $domain)->first();

        if ($agency == null) {
            return null;
        }

        return $this->cache->remember("agency:{$agency->id}", $this->lifetime, function () use ($agency) {
            return $agency;
        });
    }
}