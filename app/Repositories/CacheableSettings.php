<?php

namespace App\Repositories;

use App\Setting;
use Illuminate\Support\Facades\Cache;

class CacheableSettings
{
    protected $cache;
    protected $lifetime;

    public function __construct()
    {
        $this->lifetime = config('cache.model.lifetime');
        $this->cache = Cache::store(config('cache.model.driver'))->tags('setting');
    }

    public function all()
    {
        return $this->cache->remember("settings:all", $this->lifetime, function () {
            return Setting::all();
        });
    }
}
