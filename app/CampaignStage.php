<?php

namespace App;

use Carbon\Carbon;
use App\Traits\Hashidable;
use Illuminate\Database\Eloquent\Model;

class CampaignStage extends Model
{
    use Hashidable;

    const FOLLOW_UP = 4;

    public const STAGE_TYPES = [
        1 => 'Email Message',
        2 => 'Manual Task',
        3 => 'Linked In Connect Request',
        4 => 'Linked In Message',
    ];

    public const DELAY_TYPES = [
        1 => 'day',
        2 => 'hour',
        3 => 'minute',
    ];

    public const DELAY_DENOMINATORS = [
        1 => 86400,
        2 => 3600,
        3 => 60,
    ];

    protected $guarded = ['id'];

    protected $appends = ['hashid', 'delay'];

    protected $casts = ['enabled' => 'boolean'];

    public function getStageTypeAttribute()
    {
        return static::STAGE_TYPES[$this->stage_type_id];
    }

    public function getDelayAttribute()
    {
        return $this->follow_up * static::DELAY_DENOMINATORS[$this->delay_type_id];
    }

    public function getWaitTimeAttribute()
    {
        return $this->wait_amount * static::DELAY_DENOMINATORS[$this->wait_type_id];
    }

    public function campaign() {
        return $this->belongsTo(Campaign::class);
    }

    public function emailTemplates()
    {
        return $this->hasMany(EmailTemplate::class);
    }

    public function linkedinMessageTemplates()
    {
        return $this->hasMany(LinkedinMessageTemplate::class);
    }

    public function templates($type)
    {
        if ($type == 'email') {
            return $this->emailTemplates();
        } elseif ($type == 'linkedin') {
            return $this->linkedinMessageTemplates();
        }
    }

    public function schedules()
    {
        return $this->hasMany(Schedule::class);
    }

    public function scopeWithoutTemplates($query)
    {
        return $query->where(function($query) {
            return $query->where(function($query) {
                return $query->where('stage_type_id', 1)
                    ->whereDoesntHave('emailTemplates', function($query){
                        $query->active()->enabled();
                    });
            })->orWhere(function($query) {
                return $query->whereIn('stage_type_id', [3,4])
                    ->whereDoesntHave('linkedinMessageTemplates', function($query){
                        $query->active()->enabled();
                    });
            });
        });
    }

    public function scopeOfType($query, $id)
    {
        return $query->where('stage_type_id', $id);
    }

    public function scopeLinkedin($query)
    {
        return $query->whereIn('stage_type_id', [3, 4]);
    }

    public function scopeEmail($query)
    {
        return $query->whereIn('stage_type_id', [1]);
    }

    public function scopeNotLinkedin($query)
    {
        return $query->whereNotIn('stage_type_id', [3, 4]);
    }

    public function scopeOfCampaign($query, $campaignId)
    {
        return $query->where('campaign_id', $campaignId);
    }

    public function isEmail()
    {
        return $this->stage_type_id == 1;
    }

    public function isLinkedin()
    {
        return $this->stage_type_id == 3 || $this->stage_type_id == 4;
    }

    public function isConnectRequest()
    {
        return $this->stage_type_id == 3;
    }

    public function isLastLinkedinStep()
    {
        if (! $this->isLinkedin()) {

            return false;
        }

        $nextStep = $this->nextCampaignStage();
        if ($nextStep && ! $nextStep->isLinkedin()){

            return false;
        }

        return true;
    }

    /**
     * Get the next campaign stages after this one.
     *
     * @return Illuminate\Database\Eloquent\Builder
     */
    public function nextCampaignStages()
    {
        return CampaignStage::ofCampaign($this->campaign_id)->where('number', '>', $this->number);
    }

    public function nextCampaignStage()
    {
        return CampaignStage::ofCampaign($this->campaign_id)->where('number', $this->number+1)->first();
    }

    /**
     * Get the amount of currently available prospects for this campaign stage.
     *
     * @return int
     */
    public function getPendingProspects()
    {
        return $this->campaign->prospects()
            ->active()
            ->whereNull('interested')
            ->where('next_step', $this->number)
            ->where(function ($query) {
                // Who have not been contacted yet or were contacted before follow_up days
                return $query->whereNull('last_contacted')
                    ->orWhere('last_contacted', '<', Carbon::now()->subSeconds($this->delay + 60));
            })
            ->count();
    }

    public function getPositiveCountAttribute()
    {
        return $this->campaign->prospects()
            ->where('interested', 'POSITIVE')
            ->where('completed_steps', '=', $this->number)
            ->count();
    }

    public function isFirstOfType()
    {
        return CampaignStage::ofCampaign($this->campaign_id)
            ->ofType($this->stage_type_id)
            ->where('number', '<', $this->number)
            ->doesntExist();
    }

    public function getAcceptedProspectStatuses()
    {
        if ($this->isEmail()) {
            return [
                'OK'
            ];
        } else {
            $campaign = $this->campaign;
            $statuses = ['OK'];

            if (! $this->campaign->stop_bounced) {
                array_push($statuses, 'BOUNCED');
            }

            if (! $this->campaign->stop_unsubscribed) {
                array_push($statuses, 'UNSUBSCRIBED');
            }

            return $statuses;
        }
    }
}
