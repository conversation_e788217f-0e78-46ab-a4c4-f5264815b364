<?php

namespace App;

use App\Traits\Hashidable;
use App\Traits\HasTags;
use Illuminate\Database\Eloquent\Model;

class WarmupThread extends Model
{
    use Hashidable, HasTags;

    protected $guarded = ['id'];

    protected $appends = ['hashid'];

    /**
     * EmailAccount of a thread.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function emailAccount()
    {
        return $this->belongsTo(EmailAccount::class);
    }

    /**
     * Email Message of a thread.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function warmupMessages()
    {
        return $this->hasMany(WarmupMessage::class);
    }

    /**
     * Get the latest message of a thread.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function getLatestMessage()
    {
        return $this->warmupMessages->sortByDesc('created_at')->first();
    }

    /**
     * Filter threads by account id.
     *
     * @param mixed $query
     * @param mixed $intAccountId
     * @return mixed $query
     */
    public function scopeOfAccount($query, $intAccountId)
    {
        if (0 != $intAccountId) {
            return $query->where('email_account_id', $intAccountId);
        }

        return $query;
    }

    /**
     * Filter threads by account id.
     *
     * @param mixed $query
     * @param mixed $intAccountId
     * @return mixed $query
     */
    public function scopeOfRecipient($query, $intRecipientId)
    {
        if (0 != $intRecipientId) {
            return $query->where('recipient_account_id', $intRecipientId);
        }

        return $query;
    }
}
