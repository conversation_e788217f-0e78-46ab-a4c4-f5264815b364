<?php

namespace App;

use App\Traits\Hashidable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class LinkedinSearch extends Model
{
    use Hashidable;

    const STATUSES = [
        'DRAFT',                // newly created
        'WAITING',              // if liAccount of this search has currently running search
        'SEARCHING',            // when searching salesnav/recruiter search
        'SEARCHED',             // when finished scraping the results of a search
        'RUNNING',              // when getting emails of each individual profile results
        'PAUSED',               // stop a running search (2nd phase)
        'DONE',                 // when all profiles were scraped and fetched email
        'ARCHIVED',             // delete status of a search if it has billed data
        'STOPPED',              // when login was redirected to a challenge / checkpoint page or manually stopped (1st phase)
        'INVALID',              // invalid token for recruiter type
    ];

    protected $guarded = ['id'];

    protected $appends = ['hashid', 'searched_date'];

    protected $casts = [
        'cookies' => 'array',
        'searched_at' => 'datetime',
    ];

    public function agency()
    {
        return $this->belongsTo(Agency::class);
    }

    public function team()
    {
        return $this->belongsTo(Team::class);
    }

    public function linkedinAccount()
    {
        return $this->belongsTo(LinkedinAccount::class);
    }

    public function linkedinProfiles()
    {
        return $this->hasMany(LinkedinProfile::class);
    }

    public function linkedinSearchStats()
    {
        return $this->hasMany(LinkedinSearchStats::class);
    }

    public function linkedinSearchCreditUsage()
    {
        return $this->hasOne(LinkedinSearchCreditUsage::class);
    }

    public function linkedinSearchMonthlyUsages()
    {
        return $this->hasMany(LinkedinSearchMonthlyUsage::class);
    }

    public function proxy()
    {
        return $this->belongsTo(Proxy::class);
    }

    /**
     * Filter linkedin searches by search keywords.
     *
     * @param  mixed  $query
     * @param  mixed  $keywords
     * @param  array  $filters
     * @return mixed $query
     */
    public function scopeOfKeywords($query, $keywords, $filters = [])
    {
        if ($keywords) {
            return $query->where('name', 'like', '%'.$keywords.'%');
        }

        return $query;
    }

    /**
     * Filter linkedin searches by status.
     *
     * @param mixed $query
     * @param mixed $status
     * @return mixed $query
     */
    public function scopeOfStatus($query, $status)
    {
        if ($status) {
            return $query->where('status', $status);
        }

        // return all searches except archived
        return $query->where('status', '!=', 'ARCHIVED');
    }

    /**
     * Filter linkedin searches by team id.
     *
     * @param mixed $query
     * @param mixed $teamId
     * @return mixed $query
     */
    public function scopeOfTeam($query, $teamId)
    {
        if ($teamId) {
            return $query->where('team_id', $teamId);
        }

        return $query;
    }

    /**
     * Filter linkedin searches by linkedinAccountId.
     *
     * @param mixed $query
     * @param mixed $teamId
     * @return mixed $query
     */
    public function scopeOfLinkedinAccount($query, $linkedinAccountId)
    {
        if ($linkedinAccountId) {
            return $query->where('linkedin_account_id', $linkedinAccountId);
        }

        return $query;
    }

    /**
     * Filter linkedin searches by agency.
     *
     * @param mixed $query
     * @param int $agencyId
     * @return mixed $query
     */
    public function scopeOfAgency($query, $agencyId)
    {
        if ($agencyId) {
            return $query->where('agency_id', $agencyId);
        }

        return $query;
    }

    /**
     * Format searched_at field for the frontend display date.
     *
     * @return string
     */
    public function getSearchedDateAttribute()
    {
        return $this->searched_at ? $this->searched_at->format('M jS Y, g:iA') : '';
    }

    public function incrementCreditUsage($quantity = 1, $cached = false)
    {
        if ($this->linkedinSearchCreditUsage()->exists()) {
            if ($cached) {
                $this->linkedinSearchCreditUsage->update([
                    'credit_usage' => DB::raw( "credit_usage + $quantity" ),
                    'cached_credit_usage' => DB::raw( "cached_credit_usage + $quantity" ),
                ]);
            } else {
                $this->linkedinSearchCreditUsage->increment('credit_usage', $quantity);
            }
        } else {
            $this->linkedinSearchCreditUsage()->create([
                'agency_id' => $this->agency_id,
                'credit_usage' => $quantity,
                'cached_credit_usage' => $cached ? $quantity : 0
            ]);
        }
    }

    /**
     * The "booting" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        /*
         * Add a 'user' global scope to restrict search access for simple users.
         * Agency admins can access all searches of their agency.
         * Admins & Staff skip global scope and can access all searches.
         */
        static::addGlobalScope('user', function (Builder $builder) {
            if (! app()->runningInConsole() && auth()->user()) {
                if (!auth()->user()->isAdmin() && !auth()->user()->isStaff() && !auth()->user()->isSupport()) {
                    if (auth()->user()->can('agency-admin')) {
                        $builder->where('agency_id', auth()->user()->agency_id);
                    }
                }
            }
        });
    }
}
