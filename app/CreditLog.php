<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class CreditLog extends Model
{
    protected $fillable = [
        'agency_id',
        'amount',
        'reason',
        'type',
    ];

    protected $appends = ['created_date'];

    public function agency()
    {
        return $this->belongsTo(Agency::class);
    }

    public function getCreatedDateAttribute()
    {
        return $this->created_at->format('M d, Y');
    }

    /**
     * Filter threads by agency id.
     *
     * @param mixed $query
     * @param mixed $agencyId
     * @return mixed $query
     */
    public function scopeOfAgency($query, $agencyId)
    {
        if (0 != $agencyId) {
            return $query->where('agency_id', $agencyId);
        }

        return $query;
    }
}
