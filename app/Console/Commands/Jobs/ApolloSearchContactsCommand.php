<?php

namespace App\Console\Commands\Jobs;

use App\Jobs\ApolloSearchContacts;
use Illuminate\Console\Command;

class ApolloSearchContactsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'jobs:apollo-search-contacts
                            {--filter=estimated_sales : The filter name same as the query param in passed in search}
                            {--value= : The id of filter name same as the query value in passed in search}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Dispatch job to search domain contacts in apollo based on the filter results.';

    protected $delay = 1;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $filters = [
            "categories",
            "plans",
            "platforms",
            "estimated_sales",
            "countries",
            "product_counts",
            "apps",
            "contact_types",
            "currencies",
            "features",
            "months",
            "phone_country_codes",
            "sales_channels",
            "shipping_carriers",
            "tags",
            "technologies",
            "top_level_domains",
        ];

        $filter = $this->option('filter');
        $value = $this->option('value');

        if(empty($filter) || !in_array($filter, $filters)) {
            $this->error("Invalid filter - $filter");
            return;
        }
        
        if(empty($value) && $value !== "0") {
            $this->error("Missing value for filter $filter. Pass actual filter ID in through '--value={FILTER_ID}'");
            return;
        }

        ApolloSearchContacts::dispatch($filter, $value)->onQueue('emailfinder');
    }
}
