<?php

namespace App\Http\Controllers;

use App\Agency;
use App\Contact;
use App\Campaign;
use App\EmailAccount;
use App\EmailThread;
use App\ChatgptPrompt;
use App\ChatgptPromptTemplate;
use App\Jobs\DbMigrations\StopECProspects;
use App\Models\ExternalService;
use App\Models\StoreLeads\ImportBatch;
use App\Models\StoreLeads\Search;
use App\Models\StoreLeads\Domain;
use App\Models\StoreLeads\Plan;
use App\Models\StoreLeads\AgencyDomain;
use App\Services\ExternalServiceHandler;
use App\WarmupDailyStats;
use App\WarmupThread;
use App\WarmupMessage;
use App\Jobs\Email\CheckEmailSchedules;
use App\Jobs\Email\EmailEngineMigrate;
use App\Jobs\Email\SendWarmupInboxMessage;
use App\Jobs\Email\GetEmailFolders;
use App\Jobs\Linkedin\ProxyTemporaryTest;
use App\Jobs\Linkedin\ScrapeLinkedinSearchProfiles;
use App\Jobs\Linkedin\GetLinkedinMessages;
use App\Jobs\Nylas\GetNylasEmailMessage;
use App\Jobs\StoreLeadsFindContacts;
use App\Jobs\EmailFinder;
use App\Jobs\PdlEnrichment;
use App\Jobs\SendWebhook;
use App\Jobs\StoreLeadsImport;
use App\LinkedinAccount;
use App\LinkedinProfile;
use App\LinkedinSearch;
use App\LinkedinSearchActivity;
use App\ProspectActivity;
use App\ScheduledProspect;
use App\Services\LinkedinThreadService;
use App\Services\WarmupSenderService;
use App\Services\EmailsFinderService;
use App\Services\Wavo3CreditsBillingService;
use App\Team;
use App\Prospect;
use App\Services\EmailIntegrationServices\EmailIntegrationFactory;
use Carbon\Carbon;
use Google_Client;
use Illuminate\Console\Scheduling\Event;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Laravel\Spark\Contracts\Repositories\NotificationRepository;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Validator;
use App\Services\ChatgptPromptService;
use GuzzleHttp\Client as HttpClient;
use App\Services\WebsiteDataScrapperService;
use Illuminate\Support\Str;
use Illuminate\Support\Arr;
use Intervention\Image\Facades\Image;
use Illuminate\Support\Facades\File;
use Spatie\ScheduleMonitor\Support\ScheduledTasks\ScheduledTaskFactory;
use Spatie\ScheduleMonitor\Support\ScheduledTasks\ScheduledTasks;
use Spatie\ScheduleMonitor\Support\ScheduledTasks\Tasks\Task;
use Faker\Factory as Faker;

class ApiTestController extends Controller
{
    protected $notifications;
    protected $factory;

    public function __construct(NotificationRepository $notifications, EmailIntegrationFactory $factory)
    {
        $this->notifications = $notifications;
        $this->factory = $factory;
    }

    public function test1()
    {
        $linkedinSend = config('app.linkedin.send') ? 'true' : 'false';
        $api = $this->factory->make(EmailAccount::inRandomOrder()->first());
        return "Email: {$api->getNylasAccount()} - LinkedinSend: {$linkedinSend}";
    }

    public function notify($user = null)
    {
        if (!$user) {
            $user = auth()->user();
        }

        $this->notifications->create($user, [
            'icon'        => 'fa-users',
            'body'        => 'New Notification!',
            'action_text' => 'View Prospects',
            'action_url'  => '/prospects',
        ]);

        return redirect('/home');
    }

    /*
     * Test json field in prospects table.
     */
    public function prospectsJson()
    {
        $prospect1 = factory(Prospect::class)->create([
            'campaign_id' => 1,
            'agency_id'   => 1,
            'team_id'     => factory(Team::class)->create(
                ['agency_id' => 1]
            ),
        ]);
        $prospect1->merge_fields = [
            'hobby'     => 'photography',
            'fav_color' => 'orange',
        ];
        $prospect1->save();

        $prospect2 = factory(Prospect::class)->create([
            'campaign_id' => 1,
            'agency_id'   => 1,
            'team_id'     => factory(Team::class)->create(
                ['agency_id' => 1]
            ),
        ]);
        $prospect2->merge_fields = [
            'hobby' => 'diving',
            'sea'   => 'aegean',
        ];
        $prospect2->save();

        $prospects = Prospect::whereIn('id', [$prospect1->id, $prospect2->id])->get();

        // Get distinct keys
        $keys = $prospects->pluck('merge_fields')->collapse()->keys();

        return $keys;
    }

    /*
     * Redirect a user to be authenticated by Google, granting us access to his mailbox, calendar, and contacts.
     */
    public function googleAuth()
    {
        $client = new Google_Client();

        $client->setClientId(config('app.googleOauth.clientId'));
        $client->setClientSecret(config('app.googleOauth.clientSecret'));
        $client->setApplicationName('Huron Testing');
//        $client->setAuthConfig($authConfig);

        $client->setAccessType('offline');        // offline access
        $client->setIncludeGrantedScopes(true);   // incremental auth
        $client->addScope(\Google_Service_Gmail::MAIL_GOOGLE_COM);
        $client->addScope(\Google_Service_People::CONTACTS);
        $client->addScope(\Google_Service_Calendar::CALENDAR);
        $client->setRedirectUri(url('oauth2callback'));

        $auth_url = $client->createAuthUrl();

        return redirect($auth_url);
    }

    /*
     * This is called by google after trying to authenticate a use. On success we should receive a refresh token.
     */
    public function goolgleAuthCallback(Request $request)
    {
        if ($request->has('error')) {
            return 'Error: '.$request->input('error');
        } elseif ($request->has('code')) {
            $client = new Google_Client();
            $client->setClientId(config('app.googleOauth.clientId'));
            $client->setClientSecret(config('app.googleOauth.clientSecret'));
            $client->setAccessType('offline');        // offline access
            $client->setRedirectUri(url('oauth2callback'));

            $token = $client->fetchAccessTokenWithAuthCode($request->input('code'));

            return $token;
            /* Example Token:
            [
                "access_token" => "*********************************************************************************************************************************"
                "token_type" => "Bearer"
                "expires_in" => 3600
                "refresh_token" => "1/yxuhwk7l08qHxTCLAq3DiMPD6DR3r8ubSiemSydDCi4"
                "created" => **********
            ]
            */
        }
    }

    // Reset email account limits at midnight.
    public function scheduleDaily()
    {
        $time = Carbon::parse('2018-08-09 00:00:00');

        // reset accounts
        EmailAccount::where('emails_sent', '>', 0)
            ->update(['emails_sent' => 0]); // Mass Update
        flushModelCache('email_account');
    }

    // Run hourly and dispacth email account schedule checks.
    public function scheduleByHour()
    {
        $activeEmailAccounts = EmailAccount::whereHas('campaigns', function ($query) {
            $query->running();
        })->get();

        foreach ($activeEmailAccounts as $emailAccount) {
            $delay = 0;
            // Use remaining minutes of hour
            while ($delay < (60 - Carbon::now()->minute) * 60) {
                Log::info('Dispatch check schedules of email account id: '.$emailAccount->id.' with delay of '.$delay);
                CheckEmailSchedules::dispatch($emailAccount->id)->onQueue('schedule')->delay(now()->addSeconds($delay));
                $delay = $delay + rand($emailAccount->min_interval, $emailAccount->max_interval);
            }
        }
    }

    public function endAgencyTrial(Agency $agency)
    {
        $n = Carbon::now();
        $n->addMinutes(2);
        $t = $n->timestamp;
        $subscription = $agency->subscription();
        \Stripe\Stripe::setApiKey(config('services.stripe.secret'));
        \Stripe\Subscription::update(
            $subscription->stripe_id,
            [
                'trial_end' => $t,
                'prorate' => false
            ]
        );

        $subscription->trial_ends_at = $t;
        $subscription->save();
        $agency->trial_ends_at = $t;
        $agency->save();

//        $subscription->cancelNow();
//
//        if ($subscription->trial_ends_at > $subscription->ends_at) {
//            $subscription->trial_ends_at = $subscription->ends_at;
//            $subscription->save();
//        }
//
//        if ($agency->trial_ends_at > $subscription->ends_at) {
//            $agency->trial_ends_at = $subscription->ends_at;
//            $agency->save();
//        }
    }

    /**
     * Fetch all incoming messages of an email account, that were received after February 1st.
     *
     * @param $id
     * @throws \Exception
     */
    public function fetchMessages(Request $request, $id)
    {
        $timeStamp = $request->input('ts', '**********'); // feb default time
        $emailAccount = EmailAccount::findOrFail($id);
        $emailApi = new NylasApiService($emailAccount);

        $messageIds = [];

        Log::info('Manually fetching messages of email account id: '.$id);

        // Fetch TO
        $offset = 0;
        do {
            $response = $emailApi->getEmailMessages("view=ids&offset=$offset&to={$emailAccount->email_address}&received_after=$timeStamp");
            if ($response) {
                $data = parseJsonResponse($response);
                $messageIds = array_merge($messageIds, $data);
                $amountFetched = count($data);
                $offset += 100;
            } else {
                $amountFetched = 0;
            }
        } while ($amountFetched == 100);

        // Fetch CC
        $offset = 0;
        do {
            $response = $emailApi->getEmailMessages("view=ids&offset=$offset&cc={$emailAccount->email_address}&received_after=$timeStamp");
            if ($response) {
                $data = parseJsonResponse($response);
                $messageIds = array_merge($messageIds, $data);
                $amountFetched = count($data);
                $offset += 100;
            } else {
                $amountFetched = 0;
            }
        } while ($amountFetched == 100);

        // Fetch BCC
        $offset = 0;
        do {
            $response = $emailApi->getEmailMessages("view=ids&offset=$offset&bcc={$emailAccount->email_address}&received_after=$timeStamp");
            if ($response) {
                $data = parseJsonResponse($response);
                $messageIds = array_merge($messageIds, $data);
                $amountFetched = count($data);
                $offset += 100;
            } else {
                $amountFetched = 0;
            }
        } while ($amountFetched == 100);

        Log::info("Fetched " . count($messageIds) ." messages");

        $delay = 0;
        foreach ($messageIds as $messageId) {
            GetNylasEmailMessage::dispatch($messageId, $emailAccount->nylasAccount->id)->onQueue('api')->delay($delay);
            $delay += 5;
        }
    }

    public function updateProspectsTimezone()
    {
        foreach (Campaign::all() as $campaign) {
            $timezone = $campaign->timezone;
            $prospectIds = $campaign->prospects()
                ->whereNotNull('last_contacted')
                ->whereNull('timezone')
                ->pluck('id');

            Prospect::whereIn('id', $prospectIds)->update(['timezone' => $timezone]); // Mass Update
        }

        return 'Prospects updated.';
    }

    public function updateCampaignEmails()
    {
        Campaign::all()->each(function ($campaign) {
            $allEmailAccounts = $campaign->emailThreads()->distinct()->pluck('email_account_id');

            $currentEmailAccounts = $campaign->emailAccounts()->pluck('id');
            $currentInactiveEmailAccounts = $campaign->inactiveEmailAccounts()->pluck('id');
            $allCurrentAccounts = $currentEmailAccounts->merge($currentInactiveEmailAccounts);

            $allEmailAccounts->reject(function ($id) use ($allCurrentAccounts) {
                return $allCurrentAccounts->contains($id);
            })->each(function ($id) use($campaign) {
                $campaign->emailAccounts()->attach($id, ['active' => false]);
            });
        });
    }

    /**
     * Create or update contacts and campaign prospects.
     *
     * @param $prospects
     * @param mixed $campaign
     */
    protected function updateProspects($campaign, $prospects)
    {
        foreach ($prospects as $prospect) {
            // prepare values
            if (!array_key_exists('sent_mails', $prospect)) {
                $prospect['sent_mails'] = 0;
            }

            if (isset($prospect['last_contacted']) && !empty($prospect['last_contacted'])) {
                $prospect['last_contacted'] = Carbon::parse($prospect['last_contacted']);
            } else {
                $prospect['last_contacted'] = null;
            }

            if (isset($prospect['last replied']) && !empty($prospect['last replied'])) {
                $prospect['last replied'] = Carbon::parse($prospect['last replied']);
            } else {
                $prospect['last replied'] = null;
            }

            if (!array_key_exists('interested', $prospect)) {
                $prospect['interested'] = null;
            }

            // Update or create contact.
            $contact = Contact::updateOrCreate(
                [
                    'email' => $prospect['email'],
                ],
                [
                    'first_name'     => $prospect['first_name'],
                    'last_name'      => $prospect['last_name'],
                    'company'        => $prospect['company'],
                    'industry'       => $prospect['industry'],
                    'website'        => $prospect['website'],
                    'tags'           => $prospect['tags'],
                    'title'          => $prospect['title'],
                    'phone'          => $prospect['phone'],
                    'address'        => $prospect['address'],
                    'city'           => $prospect['city'],
                    'state'          => $prospect['state'],
                    'country'        => $prospect['country'],
                    'last_contacted' => $prospect['last_contacted'],
                    'last_replied'   => $prospect['last replied'],
                ]
            );

            // Update or create campaign prospect.
            Prospect::updateOrCreate(
                [
                    'api_id'      => $prospect['id'],
                ],
                [
                    'contact_id'     => $contact->id,
                    'team_id'        => $this->campaign->team_id,
                    'campaign_id'    => $campaign->id,
                    'last_contacted' => $prospect['last_contacted'],
                    'last_replied'   => $prospect['last replied'],
                    'emails_sent'    => $prospect['sent_mails'],
                    'snippet1'       => substr($prospect['snipet1'], 0, 255),
                    'snippet2'       => substr($prospect['snipet2'], 0, 255),
                    'snippet3'       => substr($prospect['snipet3'], 0, 255),
                    'snippet4'       => substr($prospect['snipet4'], 0, 255),
                    'status'         => $prospect['status'],
                    'interested'     => $prospect['interested'],
                    'email'          => $prospect['email'],
                    'first_name'     => $prospect['first_name'],
                    'last_name'      => $prospect['last_name'],
                    'company'        => $prospect['company'],
                    'industry'       => $prospect['industry'],
                    'website'        => $prospect['website'],
                    'tags'           => $prospect['tags'],
                    'title'          => $prospect['title'],
                    'phone'          => $prospect['phone'],
                    'address'        => $prospect['address'],
                    'city'           => $prospect['city'],
                    'state'          => $prospect['state'],
                    'country'        => $prospect['country'],
                ]
            );
        }
    }

    public function fileTest()
    {
        $disk = Storage::disk('gcs-public');
        $url = $disk->url('wave3.jpg');
        echo $url . '<br>';

        $disk->copy('wave3.jpg', 'wave-new.jpg');
        $url = $disk->url('wave-new.jpg');
        echo $url;
    }

    /**
     * Duplicate a linkedin search to avoid reusing linkedin account for testing purposes
     *
     * @param $prospects
     * @param mixed $campaign
     */
    public function linkedinSearchDuplicate($linkedinSearch)
    {
        $linkedinSearch->load(['linkedinProfiles']);
        $userId = Auth::check() ? Auth::user()->id : null;
        $searchedTime = now()->addMinutes(rand(2,5));

        $newSearch = LinkedinSearch::create([
            'name' => 'duplicate '.time(),
            'url' => $linkedinSearch->url,
            'agency_id' => $linkedinSearch->agency_id,
            'team_id' => $linkedinSearch->team_id,
            'current_page' => $linkedinSearch->current_page,
            'initial_page' => $linkedinSearch->initial_page,
            'last_page' => $linkedinSearch->last_page,
            'total_profiles' => $linkedinSearch->total_profiles,
            'linkedin_account_id ' => $linkedinSearch->linkedin_account_id ,
            'status' => 'SEARCHED',
            'searched_at' => $searchedTime,
        ]);

        foreach ($linkedinSearch->linkedinProfiles as $profile) {
            $profile = LinkedinProfile::create([
                "agency_id" => $profile->agency_id,
                "team_id" => $profile->team_id,
                "contact_id" => $profile->contact_id,
                "linkedin_search_id" => $newSearch->id,
                "first_name" => $profile->first_name,
                "last_name" => $profile->last_name,
                "title" => $profile->title,
                "address" => $profile->address,
                "linkedin_url" => $profile->linkedin_url,
                "status" => 'PENDING',
                "created_at" => now(),
                "updated_at" => now(),
            ]);
        }

        LinkedinSearchActivity::create([
            'linkedin_search_id' => $newSearch->id,
            'user_id' => $userId,
            'activity' => 'SEARCHED',
            'created_at' => $searchedTime,
            'updated_at' => $searchedTime
        ]);

        return redirect('/linkedin-searches/'.$newSearch->getRouteKey())->with([
                'status'=> 'success',
                'msg'=>'Search successfully created',
            ]);
    }

    /**
     * Create a duplicate profile in linkedinSearch to test importation of duplicate profile result to a campaign
     *
     * @param $linkedinSearch
     * @param mixed $campaign
     */
    public function linkedinSearchDuplicateProfile($linkedinSearch)
    {
        $liProfile = LinkedinProfile::where('linkedin_search_id', $linkedinSearch->id)
            ->whereNotNull('email')
            ->where('status', 'DONE')
            ->inRandomOrder()
            ->first();

        if(empty($liProfile)) {
            return 'no linkedinProfile to duplicate';
        }

        $newLiProfile = LinkedinProfile::create([
            "agency_id" => $liProfile->agency_id,
            "team_id" => $liProfile->team_id,
            "contact_id" => $liProfile->contact_id,
            "linkedin_search_id" => $liProfile->linkedin_search_id,
            "email" => $liProfile->email,
            "email_class" => $liProfile->email_class,
            "first_name" => $liProfile->first_name,
            "last_name" => $liProfile->last_name,
            "title" => $liProfile->title,
            "address" => $liProfile->address,
            "linkedin_url" => $liProfile->linkedin_url,
            "profile_free_open" => $liProfile->profile_free_open,
            "status" => $liProfile->status,
            "email_meta" => $liProfile->email_meta,
        ]);

        return $newLiProfile->full_name;
    }

    /**
     * Manually run stopped linkedin search to process profiles
     *
     * @param $prospects
     * @param mixed $campaign
     */
    public function linkedinSearchProfiles($linkedinSearch)
    {
        if($linkedinSearch->status != 'STOPPED') {
            return 'Invalid lisearch staatus: '.$linkedinSearch->status;
        }

        Log::info("LinkedinSearch-{$linkedinSearch->id}. Manually updated status to RUNNING");

        $linkedinSearch->update([ 'status' => 'SEARCHED', 'searched_at' => now() ]);
        $linkedinSearch->update([ 'status' => 'RUNNING' ]);

        ScrapeLinkedinSearchProfiles::dispatch($linkedinSearch)->onQueue('browser');

        return 'OK';
    }

    /**
     * Update the email of linkedinProfile before exporting to campaign for outreach testing
     *
     * @param $prospects
     * @param mixed $campaign
     */
    public function linkedinProfileSetEmail(Request $request, $linkedinProfile)
    {
        $validateProspectFields = Validator::make(['email'=>$request->email], [
            'email' => 'required|email|max:255',
        ]);

        if ($validateProspectFields->fails()) {
            return "Required a valid email in query param, use '%2b' for '+' sign";
        }

        $origEmail = $linkedinProfile->email;
        $linkedinProfile->update(['email'=>$request->email]);

        return "{$linkedinProfile->first_name} {$linkedinProfile->last_name}: '{$origEmail}' was changed to '{$request->email}'.";
    }

    /**
     * Spawn browser and test luminati proxy from different zones
     *
     */
    public function proxy(Request $request)
    {
        if(array_key_exists('settings', $request->all())) {
            return [
                'url' => config('app.luminati.proxy_url'),
                'name' => config('app.luminati.datacenter_username'),
                'pass' => substr(config('app.luminati.datacenter_password'), 0, -3)."......",
            ];
        }

        $logger = array_key_exists('logger', $request->all()) ? true : false;

        ProxyTemporaryTest::dispatch($request->proxy, $logger)
            ->onQueue('browser');
        return 'OK';
    }

    /**
     * Get all active nylas emails of given agency
     * and loop thru emailAccounts to run "EmailEngineMigrateJob"
     */
    public function emailEngineMigrateAgency(Agency $agency, Request $request)
    {
        $limit = (int) $request->input('limit', 10);
        $force = filter_var($request->input('force', false), FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
        $ignorePending = filter_var($request->input('ignorePending', false), FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);

        // get all active emailaccounts
        $emailAccounts = EmailAccount::active()
            ->ofNylas()
            ->ofAgency($agency->id)
            ->when(!$force, function(Builder $query) {
                return $query->whereHas('nylasAccount', function(Builder $q) {
                    $q->running();
                });
            })
            ->orderByDesc('authenticated_at')
            ->limit($limit)
            ->get();

        echo "Migrating " . $emailAccounts->count() . " emails of agency " . $agency->name . "<br/><br/>";

        // loop thru them
        foreach ($emailAccounts as $emailAccount) {
            echo "running  EmailEngineMigrateJob for " . $emailAccount->email_address . "<br/>";

            // run migration job
            EmailEngineMigrate::dispatch($emailAccount, $ignorePending, $force);
        }

        return "<br/>OK";
    }

    public function emailAccountDisableAlias(EmailAccount $emailAccount) {
        if(!$emailAccount->alias_enabled) {
            dd('no alias');
        }

        $emailAccount->update([
            'alias_enabled' => 0,
            'alias_email_address' => null
        ]);

        return "<br/>OK";
    }

    public function addBounceEmailMessage(Campaign $campaign)
    {
        return $this->addProspectEmailMessage($campaign, 'BOUNCED');
    }

    public function addReplyEmailMessage(Campaign $campaign)
    {
        return $this->addProspectEmailMessage($campaign, 'REPLIED');
    }

    public function addAutoReplyEmailMessage(Campaign $campaign)
    {
        return $this->addProspectEmailMessage($campaign, 'AUTOREPLIED');
    }

    public function addUnsubscribeEmailMessage(Campaign $campaign)
    {
        return $this->addProspectEmailMessage($campaign, 'UNSUBSCRIBED');
    }

    public function addAcceptLinkedinConnect(Campaign $campaign)
    {
        $prospect = $campaign->prospects()->linkedinProspect()
            ->ofStatus('OK')
            ->where('linkedin_connect_status', 'PENDING')
            ->inRandomOrder()->first();

        $prospect->update([
            'linkedin_connect_status' => 'ACCEPTED',
            'wait_until' => null,
        ]);

        ProspectActivity::create([
            'prospect_id' => $prospect->id,
            'activity' => 'ProspectLinkedinAccepted'
        ]);

        return $prospect->email . ' - ' . $prospect->linkedin_hash;
    }

    public function addReplyLinkedinMessage(Campaign $campaign)
    {
        $prospect = $campaign->prospects()->linkedinProspect()
            ->ofStatus('OK')
            ->where('linkedin_connect_status', 'ACCEPTED')
            ->where('linkedin_messages_sent', '>', 1)
            ->inRandomOrder()->first();

        $linkedinThread = $prospect->linkedinThreads()->latest()->first();
        // Find a date between last message and now.
        $messageDate = now()->subSeconds(30);
        $thread = [
            'id' => $linkedinThread->linkedin_thread_hash,
            'date' => $messageDate,
            'profileUrl' => $prospect->linkedin_hash,
            'messages' => [],
        ];
        array_push($thread['messages'], [
            'id' => "demo-liReply-".$prospect->id.'-'.rand(0,10000),
            'date' => $messageDate,
            'body' => 'demo-liReply',
            'senderUrl' => $prospect->linkedin_hash,
            'origin' => 'prospect'
        ]);

        if (! empty($prospect->linkedin_account_id)) {
            $linkedinAccount = LinkedinAccount::find($prospect->linkedin_account_id);
        } else {
            $linkedinAccount = $campaign->linkedinAccounts()->first();
        }

        $linkedinThreadService = new LinkedinThreadService($linkedinAccount);
        $linkedinThreadService->parseRawThread($thread);
        $linkedinAccount->update([
            'synced_at' => now(),
        ]);

        return $prospect->email . ' - ' . $prospect->linkedin_hash;
    }

    protected function addProspectEmailMessage(Campaign $campaign, $replyType)
    {
        $activities = [
            'REPLIED' => 'ProspectReplied',
            'AUTOREPLIED' => 'ProspectAutoReplied',
            'BOUNCED' => 'EmailBounced',
            'UNSUBSCRIBED' => 'ProspectReplied'
        ];

        if ($replyType == 'BOUNCED') {
            $prospect = $campaign->prospects()->ofStatus('OK')->has('emailMessages')
                ->where('emails_sent', 1)
                ->inRandomOrder()->first();
        } else {
            $prospect = $campaign->prospects()->ofStatus('OK')->has('emailMessages')
                ->inRandomOrder()->first();
        }

        if (empty($prospect)) {
            return 'no prospect found';
        }

        $emailThread = $prospect->emailThreads()
            ->orderBy('created_at', 'desc')
            ->first();
        $emailAccount = $emailThread->emailAccount;
        $emailApi = new NylasApiService($emailAccount);

        $emailMessage = $emailApi->saveEmailMessage(
            [
                'id' => "demo-$replyType-".$prospect->id.'-'.rand(0,10000),
                'thread_id' => $emailThread->nylas_thread_id,
                'from' => [
                    [
                        'email' => $prospect->email,
                        'name' => $prospect->fullname,
                    ],
                ],
                'to' => [
                    [
                        'email' => $emailAccount->email_address,
                        'name' => $emailAccount->name
                    ],
                ],
                'subject' => "demo $replyType",
                'body' => "demo $replyType",
                'date' => Carbon::now()->timestamp,
            ],
            null,
            $prospect,
            'prospect',
            null,
            $replyType
        );

        $prospect->last_replied = $emailMessage->submitted_at;
        $prospect->status = $replyType;
        $prospect->schedule_id = null;
        $prospect->save();

        $emailThread->status = $replyType;
        $emailThread->save();

        ProspectActivity::create([
            'prospect_id' => $prospect->id,
            'email_message_id' => $emailMessage->id,
            'activity' => $activities[$replyType],
        ]);

        ScheduledProspect::where('prospect_id', $prospect->id)->delete();

        return $prospect->email;
    }

    public function externalServiceStatus() {
        $externalServices = ExternalService::all();

        return $externalServices;
    }

    public function externalServiceActivate(Request $request, ExternalServiceHandler $externalServiceHandler) {
        if(empty($request->name)) {
            return '"name" not found in the query param.';
        }

        // Remove underscore in case the user writes it that way
        $name = Str::replace('_', '', $request->name);

        if ($name == 'anymailfinder') {
            $service = $externalServiceHandler->activateService('anymail_finder');
        } elseif ($name == 'tomba') {
            $service = $externalServiceHandler->activateService('tomba');
        } elseif ($name == 'serper') {
            $service = $externalServiceHandler->activateService('serper');
        } elseif ($name == 'rapidapi') {
            $service = $externalServiceHandler->activateService('rapid_api');
        } elseif ($name == 'reversecontact') {
            $service = $externalServiceHandler->activateService('reverse_contact');
        } else {
            return 'Invalid name';
        }

        return $service;
    }

    public function emailFinderDispatchJobs(Request $request) {
        $finders = ["anymail_finder", "tomba"];

        $externalServices = ExternalService::whereIn('name', $finders)
            ->where('status', 'active')
            ->get();

        if(!$externalServices->count()) {
            dd("no active email finders");
        }

        Artisan::call('jobs:dispatch-email-finder');
        // Artisan::call('jobs:dispatch-contact-finder');

        return $externalServices;
    }

    /*
     * Display info about emailThread
     * Help in tracing failed forward
     */
    public function emailThreadInfo($threadId) {
        $thread = EmailThread::where('id',$threadId)->with(['prospect', 'emailAccount', 'campaign'])->first();

        if(empty($thread)) {
            return '404';
        }

        return [
            "campaign" => $thread->campaign ? $thread->campaign->hashid : null,
            "account" => $thread->emailAccount ? $thread->emailAccount->hashid : null,
            "thread" => $thread->getAttributes(),
            "prospect" => $thread->prospect ? $thread->prospect->getAttributes() : null,
        ];

    }

    /*
     * Display info about emailThread
     * Help in tracing failed forward
     */
    public function syncLinkedMessages(LinkedinAccount $linkedinAccount)
    {
        if($linkedinAccount->status != 'ACTIVE') {
            return 'Not Active';
        }

        $linkedinAccount->loadCount(['campaigns']);

        if(empty($linkedinAccount->campaigns_count)) {
            return 'Account not found in any campaign';
        }

        GetLinkedinMessages::dispatch($linkedinAccount)->onQueue('browser');

        return "Synching {$linkedinAccount->name}";

    }

    public function pdlEnrichment()
    {
        $sUrl = "https://www.li.c/talent/search?searchContextId=fd031a45-41e2-4854-a7b9-6ee794e1daed&searchHistoryId=**********&searchJobTitleText=Senior%20PHP%20Developer&searchJobTitleUrn=urn%3Ali%3Ats_title%3A12490&searchRequestId=66bdc001-44dc-4c6d-b8ad-ec8746b13f04&start=0";

        // $liUrlPage0 = "https://www.li.c/talent/search?searchContextId=d5516c85-4e99-4116-89a3-894a8dd478c9&searchHistoryId=***********&searchRequestId=e43aa3d6-f8b0-4499-887f-a6c3b2634a00&start=0&uiOrigin=HISTORY_SEARCH";
        // $liUrlPage1 = "https://www.li.c/talent/search?searchContextId=d5516c85-4e99-4116-89a3-894a8dd478c9&searchHistoryId=***********&searchRequestId=379f6c8e-dae3-4a7c-8899-b041efdb394f&start=25&uiOrigin=PAGINATION";
        // $liUrlPage6 = "https://www.li.c/talent/search?searchContextId=d5516c85-4e99-4116-89a3-894a8dd478c9&searchHistoryId=***********&searchRequestId=768e1624-ddca-4e66-8e6b-3b9894e70f10&start=120&uiOrigin=PAGINATION";

        /*
        // getLinkedinSearchUrlPageNumber
        $urlParts = parse_url($liUrlPage0);
        if(isset($urlParts['query'])) {
            parse_str($urlParts['query'], $urlParams);
        }

        $pageNum = 1;

        $cursorStart = intval(data_get($urlParams, 'start', 0));

        if($cursorStart > 24) {
            $pageNum = (int) floor($cursorStart / 25) + 1;
            dd($pageNum);
        }

        dd($urlParts, $urlParams, $cursorStart, $pageNum);
        */


        /*
        // setLinkedinSearchUrlPageNumber
        $urlParts = parse_url($sUrl);
        if(isset($urlParts['query'])) {
            parse_str($urlParts['query'], $urlParams);
        }

        $pageNumber = 3;
        $urlParams['start'] = (($pageNumber - 1) * 25);
        $urlParams['uiOrigin'] = "PAGINATION";

        $nextUrl = $urlParts["scheme"] . "://" . $urlParts["host"] . $urlParts["path"] . "?" . http_build_query($urlParams);

        dd($urlParams, $nextUrl, $sUrl);
        */

        // dd(config('app.pdlApiKey'));
        $liProfile = LinkedinProfile::find(6);

        PdlEnrichment::dispatch($liProfile, config('app.pdlApiKey'))->onQueue('default');
        return "pdlEnrichment";
    }

    public function webhooks()
    {
        // $prospect = Prospect::find(15);
        // SendWebhook::dispatch($prospect, 'replied')->onQueue('webhook');

        return "ok";

    }

    public function emailScrapper()
    {
        $http = new HttpClient(['base_uri' => config('app.scrapper.url')]);

        $response = $http->post('validate-emails', [
                'json' => [
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>"
                ]
            ]
        );

        return response()->json($response->getBody()->getContents());
    }

    public function prompts()
    {
        // $prospect = Prospect::find(210);

        // $mfs = $prospect->merge_fields;
        // dd($mfs);

        // $web = new WebsiteDataScrapperService($prospect);
        // $data = $web->scrape();
        // dd($data);

        /*
        $prompt = ChatgptPrompt::find(22);
        $prospect = Prospect::find(188);
        $promptService = null;

        try {
            $promptService = new ChatgptPromptService($prompt, $prospect);
            $promptService->init();
        } catch (\Throwable $e) {
            dd($e->getMessage());
        }

        $user = $promptService->parsePrompt('user');
        $system = $promptService->parsePrompt('system');

        if($system['error'] || $user['error']) {
            dd("{$system['error']} {$user['error']}");
        }

        $res = $promptService->fetch($system['content'], $user['content']);
        */

        return "ok";
    }

    // update global prompt templates with the updated "getGlobalPromptTemplate" from helpers
    public function promptTemplatesRefresh()
    {
        $globalPrompts = getGlobalPromptTemplate();

        foreach ($globalPrompts as $prompt) {
            ChatgptPromptTemplate::where('prompt_name', $prompt['name'])
                ->update([
                    'prompt_name' => $prompt['name'],
                    'system_prompt' => $prompt['system'],
                    'user_prompt' => $prompt['user'],
                ]);
        }

        return 'ok';
    }

    public function emailFinder($linkedinProfileId)
    {
        $profile = LinkedinProfile::find($linkedinProfileId);

        EmailFinder::dispatch($profile)->onQueue('emailfinder');

        return $profile;
    }

    // activate a failed linkedin account in staging/prod for testing puphpeteer and failed lisearch only
    public function salesnavActivate(LinkedinAccount $linkedinAccount)
    {
        if (!in_array($linkedinAccount->status, ["INVALID", "ERROR"])) {
            return array(
                'error' => "account is not invalid or error",
                'linkedinAccount' => $linkedinAccount
            );
        }

        $linkedinAccount->update([
            'status' => 'ACTIVE',
            'linkedin_account_type_id' => 3,
            'cookie' => time()
        ]);

        $linkedinAccount->refresh();
        return $linkedinAccount;
    }

    // activate a failed linkedin account in staging/prod for testing puphpeteer and failed lisearch only
    public function viewLinkedinScreenshot($filename)
    {
        $folder = storage_path('app/screenshots/');
        if (!is_dir($folder)) {
            File::ensureDirectoryExists($folder);
        }

        $filePath = storage_path('app/screenshots/'.$filename);
        try {
            $avatarSrcImg = Image::make($filePath);
        } catch (\Throwable $th) {
            dd("File not found: {$filePath}");
        }

        return $avatarSrcImg->response('png');
    }

    public function failedTasks()
    {
        $tasks = DB::table('monitored_scheduled_tasks')->get()->filter(function ($task) {
            if (!empty($task->last_failed_at) && !empty($task->last_started_at)) {
                $startedAt = Carbon::parse($task->last_started_at);
                $failedAt = Carbon::parse($task->last_failed_at);
                return $failedAt->isAfter($startedAt->subSecond());
            }


            return false;
        });

        return $tasks;
    }

    public function emailAccountSendWarmupMessage(EmailAccount $emailAccount)
    {
        $recipient = EmailAccount::where('sends_warmup_messages', 1)
            ->where('id', '!=', $emailAccount->id)
            ->inRandomOrder()
            ->first();

        if(empty($recipient)) {
            dd('no recipient');
        }

        $warmupSender = new WarmupSenderService();
        $messageSent = $warmupSender->sendMessage($emailAccount, $recipient, false);

        return 'ok';
    }

    public function emailAccountGenerateWarmupMessage(EmailAccount $emailAccount, Request $request)
    {
        if(empty($request->date)) {
            dd("'date' query is required. try adding '?date=2024-04-29' in the query param");
        }

        $recipients = EmailAccount::where('sends_warmup_messages', 1)
            ->where('id', '!=', $emailAccount->id)
            ->with(['warmupStats'])
            ->get();

        if(empty($recipients)) {
            dd('no recipients');
        }

        $day = Carbon::parse($request->date);
        $dayStr = $day->format('Y-m-d');

        $accountWarmupDailyStats = $emailAccount->warmupDailyStats()->where('day', $dayStr)->first();
        $emailAccount->load(['warmupStats']);

        if (empty($accountWarmupDailyStats)) {
            $accountWarmupDailyStats = WarmupDailyStats::create([
                'agency_id' => $emailAccount->agency_id,
                'email_account_id' => $emailAccount->id,
                'day' => $dayStr,
            ]);
        }

        // loop thru recipients (will sent to all of them)
        foreach ($recipients as $recipient) {
            // get recipient's warmup_stats and warmup_daily_stats
            $recipientWarmupDailyStats = $recipient->warmupDailyStats()->where('day', $dayStr)->first();

            if (empty($recipientWarmupDailyStats)) {
                $recipientWarmupDailyStats = WarmupDailyStats::create([
                    'agency_id' => $recipient->agency_id,
                    'email_account_id' => $recipient->id,
                    'day' => $dayStr,
                ]);
            }

            // for loop from random number of sends
            $sendLimit = rand(3, 6);

            for ($i=0; $i < $sendLimit; $i++) {
                // \All, \Archive, \Drafts, \Flagged, \Junk, \Sent, \Trash, \Inbox
                $specialUse = Arr::random(["\All", "\All", "\Junk", "\All", "\All"]);

                $faker = Faker::create();
                $subject = "[WVWU] - ".$faker->realText($maxNbChars = 50, $indexSize = 1);
                $message = $faker->realText($maxNbChars = 200, $indexSize = 2);

                // create warmup_threads
                $thread = WarmupThread::create([
                    'email_account_id' => $emailAccount->id
                ]);

                // create warmup_messages
                $wuMsg = WarmupMessage::create([
                    'subject' => $subject,
                    'message' => $message,
                    'warmup_thread_id' => $thread->id,
                    'agency_id' => $emailAccount->agency_id,
                    'ee_queue_id' => $emailAccount->id."_".time(),
                    'message_id' =>  $emailAccount->id."_".time()."@warmup.demo",
                    'to_email' => $recipient->email_address,
                    'email_account_id' => $emailAccount->id,
                    'recipient_account_id' => $recipient->id,
                    'status' => 'complete',
                    'ee_id' => $emailAccount->id."_".time()."_demo",
                    'ee_email_id' => $emailAccount->id."_".time()."_demo",
                    'ee_thread_id' => $emailAccount->id."_".time()."_demo",
                    'specialUse' => $specialUse
                ]);

                // update warmup_stats and warmup_daily_stats for emailAccount
                $emailAccount->warmupStats->increment('sent');
                $accountWarmupDailyStats->increment('sent');

                if($specialUse == "\Junk") {
                    $emailAccount->warmupStats->increment('spam_landed');
                    $accountWarmupDailyStats->increment('spam_landed');

                    $emailAccount->warmupStats->increment('spam_removed');
                    $accountWarmupDailyStats->increment('spam_removed');

                    if(in_array($emailAccount->email_server_type, ['gmail', 'gmail_imap'])) {
                        $accountWarmupDailyStats->increment('server_gmail_spam');
                    } elseif (in_array($emailAccount->email_server_type, ['outlook', 'office365'])) {
                        $accountWarmupDailyStats->increment('server_outlook_spam');
                    } else {
                        $accountWarmupDailyStats->increment('server_other_spam');
                    }
                } else {
                    $emailAccount->warmupStats->increment('inbox_landed');
                    $accountWarmupDailyStats->increment('inbox_landed');

                    if(in_array($emailAccount->email_server_type, ['gmail', 'gmail_imap'])) {
                        $accountWarmupDailyStats->increment('server_gmail_inbox');
                    } elseif (in_array($emailAccount->email_server_type, ['outlook', 'office365'])) {
                        $accountWarmupDailyStats->increment('server_outlook_inbox');
                    } else {
                        $accountWarmupDailyStats->increment('server_other_inbox');
                    }
                }

                // update warmup_stats and warmup_daily_stats for recipientAccount
                $recipient->warmupStats->increment('received');
                $recipientWarmupDailyStats->increment('received');
            }

        }


        return 'ok';
    }

    public function emailAccountRenameCancelled(EmailAccount $emailAccount, Request $request)
    {
        if(empty($request->new_email)) {
            dd("'new_email' query param required. please provide new name for this cancelled email account");
        }

        if(strtolower($request->new_email) == strtolower($emailAccount->email_address)) {
            dd("'new_email' param and current email_address are the same");
        }

        $emailValidate = Validator::make(['new_email'=>$request->new_email], [
            'new_email' => 'required|email|max:255',
        ]);

        if ($emailValidate->fails()) {
            dd("'new_email' is not valid email address");
        }

        $emailAccount->load(['emailEngineAccount']);

        if($emailAccount->error != 'account.invalid' || !is_null($emailAccount->cancelled_at) || $emailAccount->emailEngineAccount->sync_state == 'running') {
            dd("account is active");
        }

        $emailAccount->update([
            'email_address' => strtolower($request->new_email)
        ]);

        $emailAccount->emailEngineAccount->update([
            'sync_state' => 'stopped',
            'email_address' => strtolower($request->new_email)
        ]);

        $emailAccount->refresh();

        dd($emailAccount);
    }

    public function slImportStatus()
    {
        $batchesQueued = getPendingJobTagIds('browser', 'App\Jobs\StoreLeadsImport', 'sl-import-batch-');
        $runningBatches = ImportBatch::where('is_complete', false)->get();
        $completeBatches = ImportBatch::where('is_complete', true)->get();
        $data = [
            'jobs-queued' => $batchesQueued,
            'running-batches' => $runningBatches,
            'complete-batches' => $completeBatches,
        ];

        return response()->json($data);
    }

    public function slDomainEmailsFinder(Request $request)
    {
        // $agency = Agency::find(3);
        // $customerio = new CustomerioService($agency);
        // $add = $customerio->addAgencyOwner();
        // $event = $customerio->triggerEvent('trial_started');

        if(empty($request->domain_id) && empty($request->search_id)) {
            dd('"domain_id" or "search_id" not found in the query param');
        }

        if($request->domain_id) {
            $domain = Domain::find($request->domain_id);

            if(empty($domain)) {
                dd("domain not found");
            }

            $emailsFinder = new EmailsFinderService($domain);
            $result = $emailsFinder->searchEmails();

            dd($domain, $result);
        } elseif($request->search_id) {
            $search = Search::find($request->search_id);

            if(empty($search)) {
                dd("search not found");
            }

            if($search->status == 'DONE') {
                dd("search done");
            }

            /*
            $domains = Domain::whereIn('id', $search->domain_ids)
                ->where(function($q) use($search) {
                    $q->whereDoesntHave('agencyDomains', function($o) use($search) {
                        $o->where('agency_id', $search->agency_id);
                    });
                })
                ->with(['agencyDomains'])
                ->limit(5)
                ->dontCache()
                ->get();

            $agencyDomains = AgencyDomain::whereIn('domain_id', $search->domain_ids)
                ->where('agency_id', $search->agency_id)
                ->get();

            dd($search->getAttributes(), $domains->pluck('id')->toArray(), $agencyDomains->pluck('domain_id')->toArray());
            */

            StoreLeadsFindContacts::dispatch($search);
            dd("StoreLeadsFindContacts dispatched", $search);
        }
    }

    public function apifyTest()
    {
        $key = config('services.apify.api_key');

        $http = new HttpClient(['base_uri' => 'https://api.apify.com/v2/acts/anchor~email-check-verify-validate/']);

        $response = $http->post('run-sync-get-dataset-items?token='.$key, [
                'json' => [
                    "emails" => [
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                    ]
                ]
            ]
        );

        $status = $response->getStatusCode();

        $results = json_decode($response->getBody()->getContents());

        return $results;

        /* example results
        [
            {
                "validFormat": true,
                "validMx": true,
                "validSmtp": null,
                "disposableEmail": false,
                "freeEmail": false,
                "email": "<EMAIL>",
            },
            {
                "validFormat": true,
                "validMx": true,
                "validSmtp": null,
                "disposableEmail": false,
                "freeEmail": false,
                "email": "<EMAIL>",
            },
            {
                "validFormat": true,
                "validMx": true,
                "validSmtp": null,
                "disposableEmail": false,
                "freeEmail": false,
                "email": "<EMAIL>",
            },
            {
                "validFormat": true,
                "validMx": true,
                "validSmtp": null,
                "disposableEmail": false,
                "freeEmail": false,
                "email": "<EMAIL>",
            },
            {
                "validFormat": true,
                "validMx": true,
                "validSmtp": null,
                "disposableEmail": false,
                "freeEmail": false,
                "email": "<EMAIL>",
            },
            {
                "validFormat":true,
                "validMx":true,
                "validSmtp":null,
                "disposableEmail":false,
                "freeEmail":false,
                "email":"<EMAIL>"
            },
            {
                "validFormat":true,
                "validMx":true,
                "validSmtp":null,
                "disposableEmail":false,
                "freeEmail":false,
                "email":"<EMAIL>"
            }
          ]
        */
    }

    public function emailListVerify()
    {
        $http = new HttpClient(['base_uri' => 'https://apps.emaillistverify.com/api/']);

        $key = 'VdpEmSX3NNlwBPk0CUrPJ';
        $email = '<EMAIL>';

        $response = $http->get("verifyEmail?secret=$key&email=$email");

        $status = $response->getStatusCode();

        $results = $response->getBody()->getContents();

        return $results;

        /*
         * Results is a string response
         *
         *  Main Status Responses:
            Status="ok"- A ok response is an address that has passed all tests.
            The address provided passed all tests.

            Status="fail"- A failed response is an address that has failed 1 or more tests.
            The address provided does not exist.
            The mailbox is full.
            The address provided is a disposable email address.
            The address provided is not in a valid format.
            The address provided is a role account.
            The address provided does not have a valid dns.
            The address provided does not have a mx server.
            The address provided was found in your internal blacklist containing previously failed addresses.
            The domain provided was found in your domain blacklist.

            Status="unknown"- A unknown response is an address that can not be accurately tested.
            Is a Catchall mx server config.
            Greylisting is active on this server, please try again in a few minutes.
            Greylisting is suspected to be active on this server, please try again in a few minutes.
            The address provided can not be verified at this time.

            Status="incorrect"- No email provided in request.

            Email syntax error (example: myaddress[at]gmail.com, <NAME_EMAIL>)

            Status=" key_not_valid"- No api key provided in request or invalid.

            Status=" missing parameters"- There are no validations remaining to complete this attempt.
         */
    }

    public function proofy()
    {
        $http = new HttpClient(['base_uri' => 'https://api.proofy.io/']);

        $user = '68843';
        $key = '3ByIUHqfOjaB0gzPD1xK';
        $email = '<EMAIL>';

        $response = $http->get("verifyaddr?aid=$user&key=$key&email=$email");

        $status = $response->getStatusCode();

        $results = $response->getBody()->getContents();
        $cid = json_decode($results, true)['cid'];

        //"{"cid":38180739}"

//        $cid = '38180739';
        $response = $http->get("getresult?aid=$user&key=$key&cid=$cid");
        $results = $response->getBody()->getContents();

        return $results;
    }

    public function emailVerify()
    {
        $http = new HttpClient(['base_uri' => config('app.goVerifier.url')]);
        $emails = [
            '<EMAIL>',
            '<EMAIL>',
            "<EMAIL>",
//            "<EMAIL>",
//            "<EMAIL>",
//            "<EMAIL>",
//            "<EMAIL>",
//            "<EMAIL>",
//            "<EMAIL>",
//            "<EMAIL>",
//            "<EMAIL>",
//            "<EMAIL>",
//            "<EMAIL>",
//            "<EMAIL>",
//            "<EMAIL>",
//            "<EMAIL>",
//            "<EMAIL>",
//            "<EMAIL>",
//            "<EMAIL>",
//            "<EMAIL>",
//            "<EMAIL>",
//            "<EMAIL>",
//            "<EMAIL>",
//            "<EMAIL>",
//            "<EMAIL>",
//            "<EMAIL>",
//            "<EMAIL>",
//            "<EMAIL>",
//            "<EMAIL>",
//            "<EMAIL>",
//            "<EMAIL>",
//            "<EMAIL>",
//            "<EMAIL>",
        ];

        $verifyResults = [];
        $valid = 0;
        $catchall = 0;
        $invalid = 0;
        $error = 0;

        foreach ($emails as $email) {
            $username = Str::before($email, '@');
            $domain = Str::after($email, '@');

            try {
                $response = $http->get("smtp-verify/$username/$domain");
//                $status = $response->getStatusCode();
                $results = json_decode($response->getBody()->getContents(), true);
                $verifyResults[] = [
                    'email' => $email,
                    'valid' => $results['deliverable'],
                    'catch_all' => $results['catch_all'],
                    'disabled' => $results['disabled'],
                ];
                if ($results['deliverable']) {
                    $valid++;
                } elseif (!$results['deliverable'] && $results['catch_all']) {
                    $catchall++;
                } elseif (!$results['deliverable'] && !$results['catch_all']) {
                    $invalid++;
                }
            } catch (\GuzzleHttp\Exception\ServerException $e) {
//                $error = $e->getMessage();
                $response = $e->getResponse();
                $status = $response->getStatusCode();
                $result = $response->getBody()->getContents();
                $verifyResults[] = [
                    'email' => $email,
                    'valid' => false,
                    'reason' => $result . "($status)"
                ];
                $error++;
            }
        }

        $data = [
            'total' => count($verifyResults),
            'valid' => $valid,
            'catch_all' => $catchall,
            'invalid' => $invalid,
            'error' => $error,
            'results' => $verifyResults,
        ];

        return $data;

        /*
         [
            "host_exists" => true,
            "full_inbox" => false,
            "catch_all" => false,
            "deliverable" => true,
            "disabled" => false,
          ]
        */

    }

    public function slDomainFixPlan()
    {
        Artisan::call('data:sl-fix-plans');

        $plans = Plan::get();
        return $plans;
    }

    public function slDomainImproveName(Request $request)
    {
        $limit = 50;

        if($request->limit) {
            $limit = (int) $request->limit;
        }

        Artisan::call('data:sl-improve-name', ['--limit' => $limit]);

        return '"data:sl-improve-name" called';
    }

    /**
     * Manually update campaign in staging for testing purposes without going thru sending of emails
    */
    public function updateCampaignStatus(Request $request)
    {
        if(config('app.env') !== 'staging') {
            return "not staging";
        }

        if(empty($request->campaign_id) || empty($request->status) ||
            !in_array($request->status, ['DRAFT', 'EDITED', 'RUNNING', 'PAUSED', 'COMPLETED', 'STOPPED', 'ARCHIVED'])
        ) {
            dd('"campaign_id" or "status" not found in the query param');
        }

        $campaign = Campaign::findOrFail($request->campaign_id);
        $prevStatus = $campaign->status;
        $campaign->update(['status' => $request->status]);

        return "{$campaign->name}'s status is updated from '{$prevStatus}' to '{$campaign->status}'.";
    }

    public function stopEcProspects(Request $request)
    {
        if (request()->has('campaign')) {
            $campaign = Campaign::findHash(request()->get('campaign'));
        } else {
            $campaign = Campaign::find('12719');
        }

        if (!$campaign) {
            abort(404);
        }

        if ($campaign->agency_id != 3 || $campaign->team_id != 5) {
            return 'Not the right campaign!';
        }

        StopECProspects::dispatch($campaign->id)->onQueue('default');

        return 'Job has started for campaign: '.$campaign->name;
    }

    public function slConvertSearchToFilter()
    {
        Artisan::call('data:sl-convert-search-filter');
        return 'done';
    }

    public function slApolloSearchContacts(Request $request)
    {
        if(empty($request->filter) || empty($request->value)) {
            dd('"filter" or "value" not found in the query param');
        }

        $command = "jobs:apollo-search-contacts --filter={$request->filter} --value={$request->value}";
        Artisan::call($command);
        return $command;
    }
}
