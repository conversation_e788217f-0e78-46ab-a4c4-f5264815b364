<?php

namespace App\Http\Controllers\EmailEngine\Webhooks;

use App\EmailEngineAccount;
use App\EmailAccount;
use App\EmailEngineWebhook;
use App\EmailMessageTracker;
use App\Http\Controllers\Controller;
use App\Jobs\Email\EmailEngineAccountError;
use App\Jobs\Email\EmailEngineMessageFailed;
use App\Jobs\Email\EmailEngineMessageSent;
use App\Jobs\Email\EmailEngineMessageDeliveryError;
use App\Jobs\Email\EmailEngineMessageLinkClicked;
use App\Jobs\Email\EmailEngineMessageOpened;
use App\Jobs\Email\EmailMessageSync;
use App\Jobs\Email\RescheduleEmailAccount;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;

class NotificationController extends Controller
{
    public function index(Request $request)
    {
        try {
            $requestData = $request->all();

            if (isset($requestData['data']) && !empty($requestData['data']['messageId'])) {
                Log::info('### EmailEngine Webhook: '.$request->event.', account-'.$request->account.', message-'.$requestData['data']['messageId']);
            } else {
                Log::info('### EmailEngine Webhook: '.$request->event.', account-'.$request->account);
            }

            if($request->event == 'messageNew') {
                if(!empty($requestData['data']['messageId'])) {
                    $data = $request->data;
                    $data['messageId'] = str_replace(['<', '>'], '', $data['messageId']);
                    EmailEngineWebhook::create([
                        'event' => $request->event,
                        'date' => Carbon::parse($request->date),
                        'account_id' => $request->account,
                        'data' => $data,
                        'created_at' => now(),
                    ]);

                    $emailAccount = EmailAccount::findHash($request->account);
                    if ($emailAccount) {
                        EmailMessageSync::dispatch($emailAccount)->onQueue('api')->delay(now()->addSeconds(30));
                    }
                } else {
                    Log::info('### skipping EmailEngineGetMessage, no messageId. account-'.$request->account);
                }
            }

            if($request->event == 'messageSent') {
                EmailEngineMessageSent::dispatch($requestData)->onQueue('api');
            }

            if($request->event == 'messageFailed') {
                EmailEngineMessageFailed::dispatch($requestData)->onQueue('api');
            }

            if($request->event == 'messageDeliveryError') {
                EmailEngineMessageDeliveryError::dispatch($requestData)->onQueue('api');
            }

            if($request->event == 'authenticationError') {
                $emailId = decodeModelHashid(EmailAccount::class, $requestData['account']);
                incrementRedisCounter('account_invalid', 'email_account', $emailId);
                EmailEngineAccountError::dispatch($requestData)->onQueue('api')->delay(now()->addMinutes(10));
            }

            if($request->event == 'authenticationSuccess') {
                $emailEngineAccount = EmailEngineAccount::where('account_id', $requestData['account'])->first();
                if ($emailEngineAccount) {
                    $emailAccount = $emailEngineAccount->emailAccount;
                    $accountStatus = array(
                        'account.reauth',       // doing reauth
                        'sending.failed',       // ?
                        'account.connecting',   // currently waiting for test connection result
                        'account.failed'        // initial SMTP test connection failed, need to fix error then reauthenticate
                    );

                    // reset counter here so prev EmailEngineAccountError stuck in queue wont send notification
                    resetRedisCounter('account_invalid', $emailAccount);

                    // Looks like we only update account.invalid errors
                    if ($emailAccount && !in_array($emailAccount->error, $accountStatus)) {
                        $emailAccount->update([
                            'error' => null,
                            'error_smtp_status' => null
                        ]);
                        $emailEngineAccount->update(['sync_state' => 'running']);
                        RescheduleEmailAccount::dispatch($emailAccount);
                        Log::info('### EmailEngine Webhook: authenticationSuccess, account-'.$request->account.', no error');
                    } else {
                        Log::info('### EmailEngine Webhook: authenticationSuccess, account-'.$request->account.', current account.error-'.$emailAccount->error);
                    }
                }
            }

            return response()->json([
                'status' => 'success'
            ], 200);

        } catch (\Throwable $e) {
            Log::error($e->getMessage());

            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function opened($tracker_id, Request $request)
    {
        $referrer = $request->header('Referer');

        if(!Str::contains($referrer, config('app.domain'))) {
            $trackerId = decodeModelHashid(EmailMessageTracker::class, $tracker_id);
            $tracker = EmailMessageTracker::where('id', $trackerId)->first();

            if($tracker && $tracker->email_message_id) {
                Log::info('### EmailEngine Webhook: trackOpen, tracker-'.$trackerId);
                EmailEngineMessageOpened::dispatch($tracker->email_message_id)->onQueue('api');
            }
        }

        $img = Image::make(public_path('img/1x1.png'));
        return $img->response('gif');
    }

    public function clicked($tracker_id, Request $request)
    {
        $referrer = $request->header('Referer');

        if(!Str::contains($referrer, config('app.domain'))) {
            $trackerId = decodeModelHashid(EmailMessageTracker::class, $tracker_id);
            $tracker = EmailMessageTracker::where('id', $trackerId)->first();

            if($tracker && $tracker->email_message_id && $request->url) {
                Log::info('### EmailEngine Webhook: trackClick, tracker-'.$trackerId);
                EmailEngineMessageLinkClicked::dispatch($tracker->email_message_id)->onQueue('api');
            }
        }

        $url = Str::startsWith($request->url, 'http') ? $request->url : '//'.$request->url;
        header("Location: ".$url);
        exit();

        return '';
    }
}
