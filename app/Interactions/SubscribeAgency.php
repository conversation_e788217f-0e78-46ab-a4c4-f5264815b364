<?php

namespace App\Interactions;

use App\Spark;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use App\Events\AgencySubscription\AgencySubscribed;
use App\Contracts\Interactions\SubscribeAgency as Contract;

class SubscribeAgency implements Contract
{
    /**
     * {@inheritdoc}
     */
    public function handle($agency, $plan, $fromRegistration, array $data)
    {
        if (!empty($plan->attributes['outreach_account_plan'])) {
            $outreachAccountPrice = $plan->attributes['outreach_account_plan'];
            $subscription = $agency->newSubscription('default', [
                $plan->id,
                $outreachAccountPrice
            ])
                ->quantity(0, $outreachAccountPrice);
        } else {
            $subscription = $agency->newSubscription('default', $plan->id);
        }

        // Apply any coupon code that was provided by the user to the subscription.
        if (isset($data['coupon'])) {
            $subscription->withCoupon($data['coupon']);
        } elseif ($agency->owner->coupon) {
            $coupons = app('Laravel\Spark\Contracts\Repositories\CouponRepository');
            if ($coupons->valid($agency->owner->coupon)) {
                $subscription->withCoupon($agency->owner->coupon);
            }
        }

        // Here we will check if we need to skip trial or set trial days on the subscription
        // when creating it on the provider. By default, we will skip the trial when this
        // interaction isn't from registration since they have already usually trialed.
        if (!$fromRegistration && $agency->hasEverSubscribedTo('default', $plan->id)) {
            $subscription->skipTrial();
        } elseif ($fromRegistration && $agency->registration_trial) {
            $subscription->trialDays(7);
        } elseif ($agency->registration_trial && !empty($data['trial_days'])) {
            $subscription->trialDays($data['trial_days']);
        }

        // Next, we need to check if this application is storing billing addresses and if so
        // we will update the billing address in the database so that any tax information
        // on the user will be up to date via the taxPercentage method on the billable.
        if (Spark::collectsBillingAddress()) {
            $agency->forceFill([
                'pm_country'             => Arr::get($data, 'pm_country'),
                'billing_address'        => Arr::get($data, 'address'),
                'billing_address_line_2' => Arr::get($data, 'address_line_2'),
                'billing_city'           => Arr::get($data, 'city'),
                'billing_state'          => Arr::get($data, 'state'),
                'billing_zip'            => Arr::get($data, 'zip'),
                'billing_country'        => Arr::get($data, 'country'),
            ])->save();
        }

        // If this application collects European VAT, we will store the VAT ID that was sent
        // with the request. It is used to determine if the VAT should get charged at all
        // when billing the customer. When it is present, VAT is not typically charged.
        if (Spark::collectsEuropeanVat()) {
            $agency->forceFill(['vat_id' => Arr::get($data, 'vat_id')])->save();
        }

        // Here we will create the actual subscription on the service and fire off the event
        // letting other listeners know a user has subscribed, which will allow any hooks
        // to fire that need to send the subscription data to any external metrics app.
        if (!empty($data['payment_method'])) {
            $subscription->create($data['payment_method'], [
                'email' => $agency->owner->email,
                'name' => $agency->owner->name
            ]);
        } else {
            $subscription->create();
        }

        try {
            if ($fromRegistration && $agency->registration_trial) {
                $agency->invoicePrice(config('app.billing.trialPriceId'), 1);
            }
        } catch (\Throwable $e) {
            Log::error('Billing error: '.$e->getMessage());
            if ($agency->wavo_version == 3) {
                $agency->subscription()->cancelNow();
            }
            throw new \Exception('Trial Billing Processing Unsuccessful');
        }

        // Update agency owner billing data to be the same as the agency's
        if ($fromRegistration) {
            $agency->owner->forceFill([
                'stripe_id'                 => $agency->stripe_id,
                'pm_type'                   => $agency->pm_type,
                'pm_last_four'              => $agency->pm_last_four,
                'pm_country'                => $agency->pm_country,
                'billing_address'           => $agency->billing_address,
                'billing_address_line_2'    => $agency->billing_address_line_2,
                'billing_city'              => $agency->billing_city,
                'billing_state'             => $agency->billing_state,
                'billing_zip'               => $agency->billing_zip,
                'billing_country'           => $agency->billing_country,
                'vat_id'                    => $agency->vat_id,
                'extra_billing_information' => $agency->extra_billing_information,
            ])->save();
        } elseif (empty($agency->owner->stripe_id)) {
            $agency->owner->forceFill([
                'stripe_id' => $agency->stripe_id,
            ])->save();
        }

        if ($plan->isWavoVersion(3)) {
            $agency->forceFill([
                'has_ingress_service' => false,
                'is_linkedin_enabled' => !$agency->has_ecommerce,
                'has_linkedin_outreach' => !$agency->has_ecommerce,
                'has_chat_gpt' => true,
                'billing_interval' => $plan->interval,
            ])->save();

            // generate global templates for chatgpt prompts
            if ($agency->chatGptPromptTemplates()->count() === 0) {
                $agency->addGlobalPrompts();
            }
        }

        event(new AgencySubscribed(
            $agency = $agency->fresh(), $plan, $fromRegistration
        ));

        return $agency;
    }
}
