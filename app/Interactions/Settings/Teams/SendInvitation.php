<?php

namespace App\Interactions\Settings\Teams;

use App\Spark;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;
use <PERSON><PERSON>\Spark\Invitation;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use <PERSON><PERSON>\Spark\Events\Teams\UserInvitedToTeam;
use <PERSON><PERSON>\Spark\Contracts\Interactions\Settings\Teams\SendInvitation as Contract;
use Laravel\Spark\Interactions\Settings\Teams\SendInvitation as SparkTeamSendInvitation;

class SendInvitation extends SparkTeamSendInvitation implements Contract
{
    protected $trigger;

    /**
     * {@inheritdoc}
     */
    public function handle($team, $email, $trigger = null)
    {
        $this->trigger = $trigger;
        $invitedUser = Spark::user()->where('email', $email)->first();

        $this->emailInvitation(
            $invitation = $this->createInvitation($team, $email, $invitedUser)
        );

        if ($invitedUser) {
            event(new UserInvitedToTeam($team, $invitedUser));
        }

        return $invitation;
    }

    /**
     * E-mail the given invitation instance.
     *
     * @param  Invitation  $invitation
     * @return void
     */
    protected function emailInvitation($invitation)
    {
        $agency = $invitation->team->agency;
        $invitationTemplate = $agency->getDefaultInvitationTemplate();
        $inviteLink = 'https://'.$invitation->team->agency->domain.'/register?invitation='.$invitation->token;
        $agencyInviteMsg = str_replace('{{INVITE_LINK}}', $inviteLink, $invitationTemplate->msg);

        Mail::send($this->view($invitation), compact('invitation', 'agencyInviteMsg'), function ($m) use ($invitation, $agency, $invitationTemplate) {
            $subject = 'New Invitation from '.$agency->name.'!';

            // Use custom email invite if invitation is sent by agency admin. If sent by client users, use the default.
            if (Auth::user()->can('agency-admin')) {
                $subject = $invitationTemplate->subject;
            }

            $m->to($invitation->email)->subject($subject)->from($agency->email, $agency->name);
        });
    }

    /**
     * Create a new invitation instance.
     *
     * @param  \Laravel\Spark\Team  $team
     * @param  string  $email
     * @param  \Illuminate\Contracts\Auth\Authenticatable|null  $invitedUser
     * @return \Illuminate\Database\Eloquent\Model|\Laravel\Spark\Invitation
     * @throws \Exception
     */
    protected function createInvitation($team, $email, $invitedUser)
    {
        // If the invitation is created by a simple user, then set the company.
        if (Auth::check() && Auth::user()->can('agency-admin')) {
            $company = null;
        } else {
            $company = $team->owner->company;
        }

        return $team->invitations()->create([
            'id'        => Uuid::uuid4(),
            'agency_id' => $team->agency->id,
            'company'   => $company,
            'user_id'   => $invitedUser ? $invitedUser->id : null,
            'email'     => $email,
            'token'     => Str::random(40),
        ]);
    }

    /**
     * Get the proper e-mail view for the given invitation.
     *
     * @param  \Laravel\Spark\Invitation  $invitation
     * @return string
     */
    protected function view(Invitation $invitation)
    {
        // Agency admins.
        if ((Auth::user() && Auth::user()->can('agency-admin')) || $this->trigger == 'zapier') {
            if ($invitation->team->agency->isUserDashboard()) {
                return $invitation->user_id
                    ? 'settings.emails.invitation-to-existing-user-from-teamdashboard'
                    : 'settings.emails.invitation-to-new-user-from-teamdashboard';
            } else {
                return $invitation->user_id
                    ? 'settings.emails.invitation-to-existing-user-from-agency'
                    : 'settings.emails.invitation-to-new-user-from-agency';
            }
        }

        // Non agency admins (client/team users).
        return $invitation->user_id
            ? 'spark::settings.teams.emails.invitation-to-existing-user'
            : 'spark::settings.teams.emails.invitation-to-new-user';
    }
}
