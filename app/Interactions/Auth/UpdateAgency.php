<?php

namespace App\Interactions\Auth;

use App\Agency;
use Illuminate\Support\Facades\Validator;

class UpdateAgency
{
    /**
     * Get a validator instance for the given data.
     *
     * @param Agency $agency
     * @param array $data
     * @return \Illuminate\Validation\Validator
     */
    public function validator($agency, array $data)
    {
        return Validator::make($data, [
            'agency_name'   => 'required|max:255',
            'agency_email'  => 'nullable|email|max:255',
            'agency_domain' => 'required|max:255|unique:agencies,domain,'.$agency->id,
        ]);
    }

    /**
     * Update the agency information.
     *
     * @param Agency $agency
     * @param array $data
     * @return Agency
     */
    public function handle($agency, array $data)
    {
        $agency->forceFill([
            'name'   => $data['agency_name'],
            'email'  => $data['email'],
            'domain' => $data['agency_domain'],
        ])->save();

        return $agency;
    }
}
