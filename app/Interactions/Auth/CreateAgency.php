<?php

namespace App\Interactions\Auth;

use App\Agency;
use Illuminate\Support\Facades\Validator;

class CreateAgency
{
    /**
     * Get a validator instance for the request.
     *
     * @param  $request
     * @return \Illuminate\Validation\Validator
     */
    public function validator($request)
    {
        return Validator::make($request->all(), [
            'agency_name'   => 'required|max:255',
            'agency_email'  => 'nullable|email|max:255',
            'agency_domain' => 'nullable|max:255|unique:agencies,domain',
        ]);
    }

    /**
     * Create a new agency instance in the database.
     *
     * @param  $request
     * @return \App\Agency
     */
    public function handle($request)
    {
        $agency = new Agency();

        $agency->forceFill([
            'name'   => $request->agency_name,
            'email'  => $request->email,
            'domain' => $request->agency_domain,
            'registration_billing_plan' => $request->selected_plan ?? null,
            'registration_trial' => $request->trial ?? false,
            'wavo_version' => 3,
        ])->save();

        return $agency;
    }
}
