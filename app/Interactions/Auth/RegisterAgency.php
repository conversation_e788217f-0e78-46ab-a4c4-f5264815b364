<?php

namespace App\Interactions\Auth;

use App\AgencyPlan;
use App\Http\Requests\Auth\AgencyRegisterRequest;
use App\Http\Requests\Auth\RegisterRequest;
use Illuminate\Support\Str;
use App\Interactions\SubscribeAgency;
use App\Spark;
use App\Agency;
use App\SubdomainBlock;
use Illuminate\Support\Facades\Log;
use App\AgencyInvitation;
use Illuminate\Support\Facades\DB;
use Laravel\Spark\Contracts\Interactions\Subscribe;
//use Laravel\Spark\Contracts\Http\Requests\Auth\RegisterRequest;
//use Laravel\Spark\Contracts\Interactions\Auth\Register as Contract;
use Laravel\Spark\Contracts\Interactions\Auth\CreateUser as CreateUserContract;

class RegisterAgency
{
    /**
     * The team created at registration.
     *
     * @var \Laravel\Spark\Team
     */
    private static $agency;

    /**
     * {@inheritdoc}
     */
    public function handle(AgencyRegisterRequest $request)
    {
        return DB::transaction(function () use ($request) {
            self::$agency = $this->setupAgency($request);

            return $this->createUser($request);
        });
    }

    /**
     * Create the user for the new registration.
     *
     * @param  AgencyRegisterRequest $request
     * @throws \Exception
     * @return \Illuminate\Contracts\Auth\Authenticatable
     */
    protected function createUser(AgencyRegisterRequest $request)
    {
        $user = Spark::interact(CreateUserContract::class, [$request]);

        if ($invitation = $this->getInvitation($request)) {
            $invitation->delete();
        }

        $user->assignRole('agency-admin');
        self::$agency->assignOwner($user->id);
//        self::$agency->fresh();

        return $user;
    }

    /**
     * Subscribe the given user to a subscription plan.
     *
     * @param  RegisterRequest|AgencyRegisterRequest $request
     * @param  \Illuminate\Contracts\Auth\Authenticatable $user
     * @return \Illuminate\Contracts\Auth\Authenticatable
     */
    protected function subscribe($request, $user)
    {
        // subscribe to user-dashboard if the selected_plan is user-dashboard
        if (data_get($request, 'selected_plan') == 'user-dashboard') {
            $agencyPlan = Spark::plans()->merge(Spark::agencyPlans())->where('id', 'user-dashboard')->first();
            if ($agencyPlan instanceof AgencyPlan) {
                Spark::interact(SubscribeAgency::class, [
                    self::$agency, $agencyPlan, true, $request->all(),
                ]);
                return $user;
            }
        }

        if (!$request->hasPaidPlan()) {
            Log::info('no paid plans set');
            return $user;
        }

        if ($request->plan() instanceof AgencyPlan) {
            Spark::interact(SubscribeAgency::class, [
                self::$agency, $request->plan(), true, $request->all(),
            ]);
        } else {
            Spark::interact(Subscribe::class, [
                $user, $request->plan(), true, $request->all(),
            ]);
        }

        return $user;
    }

    /**
     * Get the full invitation instance.
     *
     * @param  mixed  $request
     * @return AgencyInvitation|false
     */
    protected function getInvitation($request)
    {
        if ($request->has('agency_invitation')) {
            return AgencyInvitation::where('token', $request->agency_invitation)->first();
        }

        return false;
    }

    /**
     * Create or update the Agency information.
     *
     * @param $request
     * @return Agency
     */
    protected function setupAgency($request)
    {
        // Prepare agency data in case nothing was passed (we have removed the fields from the sign up view).
        if (!$request->agency_name) {
            $request['agency_name'] = $request->team ?? $request->name;
        }
        if (!$request->agency_domain) {
            $request['agency_domain'] = $this->generateSubDomain($request) . '.' . config('app.domain');
        }
        if (!$request->agency_email) {
            $request['agency_email'] = $request->email;
        }

        // If no invitation, we need to create the agency first.
        if (!$request->filled('agency_invitation')) {
            Spark::interact('App\Interactions\Auth\CreateAgency@validator', [$request])->validate();
            $agency = Spark::interact(CreateAgency::class, [$request]);

            $request->merge([
                'agency_id' => $agency->id,
            ]);
        } else {
            $agency = Agency::find($request->agency_id);

            Spark::interact('App\Interactions\Auth\UpdateAgency@validator', [$agency, $request->all()])->validate();
            Spark::interact(UpdateAgency::class, [$agency, $request->all()]); // Fill agency name, email, domain
        }

        return $agency;
    }

    protected function generateSubDomain($request){
        // remove any numbers at the end of agency_name
        $agencyName = preg_replace('/[0-9]*$/', '', $request->agency_name);
        $subdomain = Str::slug($agencyName);

        // check if subdomain is in the block list
        $blocks = SubdomainBlock::select('url')->get();
        $blocked = $blocks->first(function ($block, $key) use($subdomain) {
                return strpos($subdomain, $block->url) !== false;
            });

        // if blocked, use name for subdomain
        if($blocked){
            $subdomain = Str::slug($request->name);
        }

        // check for duplicate subdomain URL
        // increment suffix to prevent duplicate agency URL error on user-dashboard
        $sameAgencyCount = Agency::where('name', $agencyName)->count();
        $similarAgencyCount = Agency::where('domain', 'like', $subdomain.'%.'.config('app.domain'))
            ->where('name', '!=', $agencyName)
            ->count();
        if ($sameAgencyCount || $similarAgencyCount) {
            $subdomain .= $sameAgencyCount+$similarAgencyCount+1;
        }
        info('subdomain: '.$subdomain);

        return $subdomain;
    }
}
