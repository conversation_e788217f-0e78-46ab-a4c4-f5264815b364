<?php

namespace App;

use App\Traits\Hashidable;
use App\Traits\HasTags;
use Illuminate\Database\Eloquent\Model;

class EmailThread extends Model
{
    use Hashidable, HasTags;

    protected $guarded = ['id'];

    protected $appends = ['snippet', 'snippet_date', 'snippet_timestamp', 'hashid'];

    protected $with = ['emailMessages', 'campaign'];

    /**
     * Campaign of a thread.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function campaign()
    {
        return $this->belongsTo(Campaign::class);
    }

    /**
     * Prospect of a thread.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function prospect()
    {
        return $this->belongsTo(Prospect::class);
    }

    /**
     * EmailAccount of a thread.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function emailAccount()
    {
        return $this->belongsTo(EmailAccount::class);
    }

    /**
     * Email Message of a thread.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function emailMessages()
    {
        return $this->hasMany(EmailMessage::class);
    }

    /**
     * Get the latest message of a thread.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function getLatestMessage()
    {
        return $this->emailMessages->sortByDesc('submitted_at')->first();
    }

    /**
     * Get the message of latest email of a thread to display on thread list.
     *
     * @return string|null
     */
    public function getSnippetAttribute()
    {
        $msg = $this->getLatestMessage();
        return $msg ? $msg->snippet : null;
    }

    /**
     * Get the Formatted date of latest email of a thread to display on thread list vue UI.
     *
     * @return string|null
     */
    public function getSnippetDateAttribute()
    {
        $msg = $this->getLatestMessage();
        return $msg ? $msg->submitted_at->timezone($this->campaign->carbon_timezone)->format('d M, h:i A') : null;
    }

    /**
     * Get the timestamp date of latest email of a thread to display on thread list vue UI.
     *
     * @return string|null
     */
    public function getSnippetTimestampAttribute()
    {
        $msg = $this->getLatestMessage();
        return $msg ? $msg->submitted_at->timezone($this->campaign->carbon_timezone)->timestamp : null;
    }

    /**
     * Filter threads by campaign id.
     *
     * @param mixed $query
     * @param mixed $intCampaignId
     * @return mixed $query
     */
    public function scopeOfCampaign($query, $intCampaignId)
    {
        if (0 != $intCampaignId) {
            return $query->where('campaign_id', $intCampaignId);
        }

        return $query;
    }

    /**
     * Filter threads by account id.
     *
     * @param mixed $query
     * @param mixed $intAccountId
     * @return mixed $query
     */
    public function scopeOfAccount($query, $intAccountId)
    {
        if (0 != $intAccountId) {
            return $query->where('email_account_id', $intAccountId);
        }

        return $query;
    }

    /**
     * Filter threads by interest.
     *
     * @param mixed $query
     * @param mixed $strInterest
     * @return mixed $query
     */
    public function scopeOfInterest($query, $strInterest)
    {
        if ('' != $strInterest) {
            if ('null' == $strInterest) {
                return $query->whereNull('interested');
            }

            return $query->where('interested', $strInterest);
        }

        return $query;
    }

    /**
     * Filter threads by status.
     *
     * @param mixed $query
     * @param mixed $strStatus
     * @return mixed $query
     */
    public function scopeOfStatus($query, $strStatus)
    {
        if ('' != $strStatus) {
            return $query->where('status', $strStatus);
        }

        return $query;
    }

    /**
     * Filter threads by campaign id.
     *
     * @param  mixed  $query
     * @param  mixed  $strKeywords
     * @param  array  $filters
     * @return mixed $query
     */
    public function scopeOfKeywords($query, $strKeywords, $filters = [])
    {
        if ('' != $strKeywords) {

            if (config('scout.search')) {

                // Get prospects from the email messages.
                $scoutMessageQuery = EmailMessage::search($strKeywords);

                foreach ($filters as $key => $value) {
                    if (!empty($value)) {
                        $scoutMessageQuery = $scoutMessageQuery->where($key, $value);
                    };
                }

                $size = $scoutMessageQuery->count();
                $messageKeys = $scoutMessageQuery->take($size)->keys();

                // Also get prospects from the prospects table
                $scoutProspectQuery = Prospect::search($strKeywords)->rule('App\Elastic\SearchRules\ProspectInboxSearchRule');

                foreach ($filters as $key => $value) {
                    if (!empty($value)) {
                        $scoutProspectQuery = $scoutProspectQuery->where($key, $value);
                    };
                }

                $size = $scoutProspectQuery->count();
                $prospectKeys = $scoutProspectQuery->take($size)->keys();

                return $query->whereHas('emailMessages', function ($q) use ($strKeywords, $messageKeys) {
                    $q->whereIn('id', $messageKeys);
                })->orWhereHas('prospect', function ($q) use ($strKeywords, $prospectKeys) {
                    $q->whereIn('id', $prospectKeys);
                });
            }

            return $query->whereHas('emailMessages', function ($q) use ($strKeywords) {
                $q->where('to_name', 'like', '%'.$strKeywords.'%')
                    ->orWhere('to_name', 'like', '%'.$strKeywords.'%')
                    ->orWhere('from', 'like', '%'.$strKeywords.'%')
                    ->orWhere('to', 'like', '%'.$strKeywords.'%')
                    ->orWhere('subject', 'like', '%'.$strKeywords.'%')
                    ->orWhere('message', 'like', '%'.$strKeywords.'%')
                    ->orWhere('snippet', 'like', '%'.$strKeywords.'%');
            })
                ->orWhereHas('prospect', function ($q) use ($strKeywords) {
                    $q->where('first_name', 'like', '%'.$strKeywords.'%')
                        ->orWhere('last_name', 'like', '%'.$strKeywords.'%')
                        ->orWhere('company', 'like', '%'.$strKeywords.'%')
                        ->orWhere('website', 'like', '%'.$strKeywords.'%')
                        ->orWhere('email', 'like', '%'.$strKeywords.'%');
                });
        }

        return $query;
    }
}
