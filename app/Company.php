<?php

namespace App;

use App\Traits\Hashidable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Company extends Model
{
    use Hashidable;

    /**
     * Indicates if the IDs are auto-incrementing.
     *
     * @var bool
     */
    protected $guarded = ['id'];

    protected $appends = [
        'hashid',
        'linkedin_slug'
    ];

    protected $casts = [
        'linkedin_browsed_at' => 'datetime',
    ];

    /**
     * Get the Linkedin slug to view the company.
     *
     * @return string|null
     */
    public function getLinkedinSlugAttribute()
    {
        if (!is_null($this->linkedin_id)) {
            return 'https://www.linkedin.com/company/' . $this->linkedin_id;
        }

        return null;
    }

    public function canBeScraped()
    {
        return ! is_null($this->linkedin_id);
    }

    // Has a website value and was scraped within 3 months,
    // Or has been scraped within 5 days (even with no website value).
    public function hasRecentScrapedData($months = 3)
    {
        return (!is_null($this->website) && !is_null($this->linkedin_browsed_at) && $this->linkedin_browsed_at->gte(now()->subMonths($months))) ||
            (!is_null($this->linkedin_browsed_at) && $this->linkedin_browsed_at->gte(now()->subDays(5)));
    }
}
