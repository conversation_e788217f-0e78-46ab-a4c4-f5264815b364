<?php

namespace App;

use App\Services\SeniorityRankingService;
use App\Traits\Hashidable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;


class Contact extends Model
{
    use Hashidable, HasFactory;

    private const DEFAULT_EXTERNAL_CONFIDENCE = 50;

    const CONFIDENCE_THRESHOLDS = [
        'EXCELLENT' => 90,     // 90+ means replied and/or verified, or verified+external valid
        'HIGH'      => 70,     // 70-84 means verified or verified+contacted or verified+autoreplied, or verifned+external unknown
        'MODERATE'  => 60,     // 60-69 means contacted or autoreplied and not verified, or external valid and not verified
        'RISKY'     => 50,     // 50-59 means unverified and not contacted, or external unknown
        'BAD'       => 0       // Below 50 means bounced
    ];

    const EMAIL_ACTIVITY_CONFIDENCE = [
        'REPLIED' => 90, // 100 total if verified unknown or verified
        'AUTOREPLIED' => 65, // 80 total if verified unknown, 85 if verified
        'CONTACTED' => 60, // 75 total if verified unknown, 80 if verified
        'UNKNOWN' => 50, // 65 total if verified unknown, 70 if verified
        'BOUNCED' => 5,
    ];

    // External source confidence mappings
    private const EXTERNAL_SOURCE_MAPPINGS = [
        'anymailfinder' => [
            75 => 60, // AnymailFinder's valid -> our medium. 85 total if verified unknown, 95 if verified
            50 => 50, // AnymailFinder's low -> our unknown. 60 total if verified unknown, 70 if verified
            0 => 5,   // AnymailFinder's very low -> our bounced
        ],
        'tomba' => [
            99 => 90, // Tomba.io's verified valid-> our good score
            95 => 90, // Tomba.io's high -> our good score // NOT USED?
            90 => 80, // Tomba.io's high score -> our good score // NOT USED?
            80 => 70, // Tomba.io's medium-high -> our medium-high // NOT USED?
            77 => 60, // Tomba.io's verified accept-all -> our medium-high
            75 => 60, // Tomba.io's medium -> our medium // NOT USED?
            55 => 50, // Tomba.io's not-verified -> our contacted
            50 => 40, // Tomba.io's webmail and disposable emails -> our low
            0 => 5,   // Tomba.io's very low -> our bounced // NOT USED?
        ],
        'apollo_data' => [
            50 => 50, // Set all contacts from this source as risky
        ],
        // Add other sources as needed
    ];

    private const VERIFICATION_BONUS = [
        // Positive verification outcomes
        null => 20,           // Full bonus for verified valid (no error)

        // Inconclusive verification outcomes - moderate bonus
        'timeout' => 15,      // Server timed out but may be valid
        'blocked' => 15,      // Server blocked verification but may be valid
        'policy-restriction' => 15, // Policy prevents verification but may be valid
        'unknown' => 10,      // Unknown verification failure but may be valid

        // Negative verification outcomes - zero bonus
        'invalid' => 0,       // Verified as invalid
        'catch-all' => 0,     // Catch-all domain (risky)
        'server-not-exist' => 0, // Server doesn't exist
        'server-misbehaving' => 0, // Server misbehaving
        'api-error' => 0,     // API error during verification

        'default' => 10        // Default modest bonus for other cases
    ];

    /**
     * Indicates if the IDs are auto-incrementing.
     *
     * @var bool
     */
    protected $guarded = ['id'];

    protected $casts = [
        'linkedin_browsed_at' => 'datetime',
        'verified_at' => 'datetime',
    ];

    protected $appends = [
        'email_confidence_status',
        'seniority',
    ];

    /**
     * A conctact can be used as a prospect many times.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function prospects()
    {
        return $this->hasMany(Prospect::class);
    }

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function getEmailConfidenceStatusAttribute(): string
    {
        $totalConfidence = $this->email_confidence;

        if ($totalConfidence >= self::CONFIDENCE_THRESHOLDS['EXCELLENT']) {
            return 'Verified+';
        } elseif ($totalConfidence >= self::CONFIDENCE_THRESHOLDS['HIGH']) {
            return 'Verified';
        } elseif ($totalConfidence >= self::CONFIDENCE_THRESHOLDS['MODERATE']) {
            return 'Likely';
        } elseif ($totalConfidence >= self::CONFIDENCE_THRESHOLDS['RISKY']) {
            return 'Unverified';
        }

        return 'Unavailable';
    }

    // Accessor for email confidence including verification bonus
    public function getEmailConfidenceAttribute(): int
    {
        // Prefer activity-based confidence if available
        if ($this->email_activity_confidence > self::EMAIL_ACTIVITY_CONFIDENCE['UNKNOWN']) {
            $baseConfidence = $this->email_activity_confidence;
        } else {
            $baseConfidence = $this->translateExternalConfidence();
        }

        // Apply verification bonus based on verification error
        $verificationBonus = 0;
        if ($this->verified_at) {
            // Use error field directly for status
            $verificationBonus = self::VERIFICATION_BONUS[$this->verification_error]
                ?? self::VERIFICATION_BONUS['default'];
        }

        return min(100, $baseConfidence + $verificationBonus);
    }

    public function getSeniorityAttribute(): string|null
    {
        if ($this->seniority_id) {
            return SeniorityRankingService::getSeniorityLabel($this->seniority_id);
        }

        return null;
    }

//    public function getVerificationFailedAttribute(): bool
//    {
//        // if this has been verified, and verification bonus is 0, then it has failed
//        if ($this->verified_at && self::VERIFICATION_BONUS[$this->verification_error] === 0) {
//            return true;
//        }
//
//        return false;
//    }

    private function translateExternalConfidence(): int
    {
        if (!$this->external_email_confidence || !$this->source) {
            return self::EMAIL_ACTIVITY_CONFIDENCE['UNKNOWN'];
        }

        $mappings = self::EXTERNAL_SOURCE_MAPPINGS[strtolower($this->source)] ?? null;
        if (!$mappings) {
            return $this->external_email_confidence;
        }

        // Find the closest mapping threshold
        $thresholds = array_keys($mappings);
        foreach ($thresholds as $threshold) {
            if ($this->external_email_confidence >= $threshold) {
                return $mappings[$threshold];
            }
        }

        return self::DEFAULT_EXTERNAL_CONFIDENCE;
    }
}
