<?php

namespace App;

use App\Traits\Hashidable;
use App\Traits\HasTags;
use Illuminate\Database\Eloquent\Model;

class WarmupStat extends Model
{
    use Hashidable, HasTags;

    protected $guarded = ['id'];

    protected $appends = ['hashid'];

    /**
     * EmailAccount of a thread.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function emailAccount()
    {
        return $this->belongsTo(EmailAccount::class);
    }

    /**
     * Filter threads by account id.
     *
     * @param mixed $query
     * @param mixed $intAccountId
     * @return mixed $query
     */
    public function scopeOfAccount($query, $intAccountId)
    {
        if (0 != $intAccountId) {
            return $query->where('email_account_id', $intAccountId);
        }

        return $query;
    }
}
