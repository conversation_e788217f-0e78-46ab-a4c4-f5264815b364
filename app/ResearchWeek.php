<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class ResearchWeek extends Model
{
    protected $guarded = ['id'];

    protected $fillable = [
        'agency_id',
        'week_number', // The number of this agency research week (with active subscription)
        'week_start', // The date the week starts on
        'week_end',
        'max_quantity', // The max active projects during the week
        'max_hours', // The max hours to be worked
        'hours_worked', // The total hours worked in the week
        'hours_remaining', // The current remaining hours to be worked
        'hours_scheduled', // The current scheduled hours to be worked (only active projects)
    ];

    public function scopeOfAgency($query, $agency)
    {
        if (!is_null($agency)) {
            $agencyId = $agency instanceof Model ? $agency->id : $agency;

            return $query->where('agency_id', $agencyId);
        }
    }

    public function agency()
    {
        return $this->belongsTo(Agency::class);
    }

    public function researchProjectWeeks()
    {
        return $this->hasMany(ResearchProjectWeek::class);
    }

    public function removeWorkedHours(ResearchProject $researchProject, $hours)
    {
        $this->hours_remaining = ($this->hours_remaining - $hours) < 0 ? 0 : $this->hours_remaining - $hours;
        if ($researchProject->is_active) {
            $this->hours_scheduled = ($this->hours_scheduled - $hours) < 0 ? 0 : $this->hours_scheduled - $hours;
        }
        $this->hours_worked += $hours;
        $this->save();
    }

    public function removeScheduledHours($hours)
    {
        $this->hours_scheduled = ($this->hours_scheduled - $hours) < 0 ? 0 : $this->hours_scheduled - $hours;
        $this->save();
    }

    public function removeInactiveHours($hours)
    {
        $this->max_hours = ($this->max_hours - $hours) < 0 ? 0 : $this->max_hours - $hours;
        $this->hours_remaining = ($this->hours_remaining - $hours) < 0 ? 0 : $this->hours_remaining - $hours;
    }
}
