<?php

namespace App;

use App\Traits\Hashidable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Illuminate\Support\Arr;
use Carbon\Carbon;


class ChatgptPrompt extends Model
{
    use Hashidable, HasFactory;

    const STATUS = [
        'DRAFT',            // newly created or edited and needs to run to fetch prompt values for each prospects
        'PENDING',          // waiting from RUN command
        'PENDING_ALL',      // waiting from RUNALL command
        'RUNNING',          // running the jobs to call chatgpt api to only prospect who doesnt have prompt value yet 
        'RUNNING_ALL',      // running the jobs to call chatgpt api to all prospects including prospects with prompt value already 
        'WARNING',          // parsing related issue, can skip and proceed to next prospect
        'PAUSED',           // pause a running prompt
        'ERROR',            // has an error while generation prompt values
        'COMPLETED'         // all prospects have generated the prompts
    ];

    public const DEFAULT_FIELDS  = [
        'first_name', 'last_name', 'email', 'company', 'industry', 'website', 'title',
        'phone', 'address', 'city', 'state', 'country', 'tags', 'time_of_day'
    ];

    // these are the mergefields needed in prompts
    // but dont display in contacts and email templates
    const HIDDEN_FIELDS = [
        'website_title',
        'website_description',
        'linkedin_profile_about',
        'linkedin_profile_tenure_description',
        'linkedin_profile_tenure_company',
        'linkedin_profile_tenure_position',
    ];

    protected const DB_READ_CONNECTION = 'mysql::read';

    /**
     * Indicates if the IDs are auto-incrementing.
     *
     * @var bool
     */
    protected $guarded = ['id'];

    protected $appends = [
        'system_prompt_formatted',
        'user_prompt_formatted',
    ];

    protected $casts = [
        'skipped_prospect_ids' => 'array',
    ];

    public function campaign()
    {
        return $this->belongsTo(Campaign::class);
    }

    public function snippet()
    {
        return $this->belongsTo(Snippet::class);
    }

    /**
     * Format the prompt to be submitted in ChatGPT api
     *
     * @return string
     */
    public function getSystemPromptFormattedAttribute()
    {
        $systemPrompt = Str::replace('<p>', '', $this->system_prompt);
        $systemPrompt = Str::replace('<div>', '', $systemPrompt);
        $systemPrompt = Str::replace('</p>', ' \n ', $systemPrompt);
        $systemPrompt = Str::replace('</div>', ' \n ', $systemPrompt);
        $systemPrompt = Str::replace('<br />', ' \n ', $systemPrompt);
        $systemPrompt = Str::replace('&nbsp;', ' ', $systemPrompt);
        $systemPrompt = strip_tags($systemPrompt);

        return $systemPrompt;
    }

    /**
     * Format the prompt to be submitted in ChatGPT api
     *
     * @return string
     */
    public function getUserPromptFormattedAttribute()
    {
        $userPrompt = Str::replace('<p>', '', $this->user_prompt);
        $userPrompt = Str::replace('<div>', '', $userPrompt);
        $userPrompt = Str::replace('</p>', ' \n ', $userPrompt);
        $userPrompt = Str::replace('</div>', ' \n ', $userPrompt);
        $userPrompt = Str::replace('<br />', ' \n ', $userPrompt);
        $userPrompt = Str::replace('<br/>', ' \n ', $userPrompt);
        $userPrompt = Str::replace('&nbsp;', ' ', $userPrompt);
        $userPrompt = strip_tags($userPrompt);

        return $userPrompt;
    }

    /**
     * Copy of MessageTemplate parser with modification for prompt contents
     *
     * @return array
     */
    public function parseMessageContent(Prospect $prospect, $promptType)
    {
        $messageContent = $promptType == 'user' ? $this->user_prompt_formatted : $this->system_prompt_formatted;
        $prospectCustomFields = $prospect->merge_fields;
        $missingFields = [];

        // check if there are snippets created for this campaign
        $snippetFields = Snippet::on(self::DB_READ_CONNECTION)
            ->where('campaign_id', $this->campaign_id)
            ->pluck('name')->toArray();

        // check for the dynamic date
        $pattern = "/{{today[^}]*}}/";
        preg_match_all($pattern, $messageContent, $arrDynamicDates);
        $dateTZ = $prospect->timezone ? $prospect->timezone : config('app.timezone');
        $campTZ = str_replace(' ', '_', $dateTZ);

        if (@count(data_get($arrDynamicDates, '0'))) {
            foreach (data_get($arrDynamicDates, '0') as $key => $date) {
                $strDate = '';

                // check if date is today, tomorrow, or N
                if($date == '{{today}}') {
                    $strDate = Carbon::now($campTZ)->format('l');
                } elseif ($date == '{{today+1}}') {
                    $strDate = Carbon::now($campTZ)->addDay()->format('l');
                } else {
                    $intDays = data_get(explode('+', str_replace(['{{', '}}'], '',$date)), '1', 0);

                    // check if days is numeric
                    if (is_numeric($intDays)) {
                        $strDate = Carbon::now($campTZ)->addDays($intDays)->format('l');
                    }
                }

                $messageContent = str_replace($date, $strDate, $messageContent);
            }
        }

        $requiredFields = $this->getRequiredFields($messageContent);

        // loop through default fields and update the value
        foreach (self::DEFAULT_FIELDS as $key => $field) {
            // check if merge_field is for the email account sender details, if not get prospect details
            if($field == 'time_of_day'){
                $timeOfDay = $this->calculateTimeOfDay();
                $messageContent = str_replace('{{'.$field.'}}', $timeOfDay, $messageContent);
            } else {
                // if field is required but no value, stop
                if(in_array($field, $requiredFields) && empty($prospect[$field])) {
                    array_push($missingFields, "{{{$field}}}");
                }

                $messageContent = str_replace('{{'.$field.'}}', $prospect[$field], $messageContent);
            }
        }

        // loop through custom fields and update the value
        foreach ($snippetFields as $key => $field) {
            // if field is required but no value, stop
            if(in_array($field, $requiredFields) && empty($prospectCustomFields[$field])) {
                array_push($missingFields, "{{{$field}}}");
            }

            if (isset($prospectCustomFields[$field])) {
                $messageContent = str_replace('{{'.$field.'}}', $prospectCustomFields[$field], $messageContent);
            }
        }

        // loop through HIDDEN fields and update the value
        foreach (self::HIDDEN_FIELDS as $key => $field) {
            // if field is required but no value, stop
            if(in_array($field, $requiredFields) && empty($prospectCustomFields[$field])) {
                array_push($missingFields, "{{{$field}}}");
            }

            if (isset($prospectCustomFields[$field])) {
                $messageContent = str_replace('{{'.$field.'}}', $prospectCustomFields[$field], $messageContent);
            }
        }

        if(!empty($missingFields)) {
            $uMissingFields = array_unique($missingFields);

            $haWebsiteFieldErr = array_intersect(['{{website_title}}', '{{website_description}}'], $uMissingFields);
            $websiteMsgErr = !empty($haWebsiteFieldErr) ? "Provide contact with valid website and company." : "";

            return array(
                'status' => 'error',
                'error' => "Missing Data: ".implode(", ", $uMissingFields). ". ".$websiteMsgErr,
                'missng_fields' => $uMissingFields,
                'content' => null
            );
        }

        // loop any more merge fields and handle them as variable fields
        $pattern = "/{{.*?}}/";
        preg_match_all($pattern, $messageContent, $varFields);

        if (@count(data_get($varFields, '0'))) {
            foreach (data_get($varFields, '0') as $key => $field) {
                $text = str_replace(['{{', '}}'], '', $field);
                $vars = explode('||', $text);
                if (@count($vars)) {
                    $random = trim(Arr::random($vars));

                    $messageContent = str_replace($field, $random, $messageContent);
                }
            }
        }

        return array(
            'status' => 'success',
            'content' => $messageContent,
            'error' => null
        );
    }

    /**
     * Calculate the "time of day" based on campaign's timezone
     */
    protected function calculateTimeOfDay()
    {
        $hourOfDay = Carbon::now($this->campaign->carbon_timezone)->format('G');
        $greeting = 'day';


        if($hourOfDay >= 3 && $hourOfDay <= 11) {
            $greeting = 'morning';
        } elseif($hourOfDay >= 12 && $hourOfDay <= 17) {
            $greeting = 'afternoon';
        } elseif(($hourOfDay >= 18 && $hourOfDay <= 24) || ($hourOfDay >= 0 && $hourOfDay <= 2)) {
            $greeting = 'evening';
        }

        return $greeting;
    }

    /**
     * Find the necessary fields that are needed to compile the template succesfully.
     *
     * @return array
     */
    public function getRequiredFields($content)
    {
        preg_match_all('/{{([^}}]*)}}/', $content, $matches);

        return $matches[1];
    }

    public function countCompleted()
    {
        if (!$this->relationLoaded('snippet')) {
            $this->load(['snippet']);
        }

        $snippetName = $this->snippet->name;

        $completed = Prospect::where('campaign_id', $this->campaign_id)
            ->where(function($q) use ($snippetName) {
                $q->whereNotNull('merge_fields')
                    ->where("merge_fields->{$snippetName}", "!=", "")
                    ->whereNotNull("merge_fields->{$snippetName}");
            })
            ->count();

        return $completed;
    }
}
