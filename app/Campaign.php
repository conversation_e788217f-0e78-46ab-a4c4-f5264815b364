<?php

namespace App;

use App\Elastic\IndexConfigurators\CampaignIndexConfigurator;
use App\Elastic\SearchRules\CampaignSearchRule;
use App\Jobs\NotifyOfCampaignInterrupt;
use App\Services\CampaignSchedulerService;
use App\Traits\HasAgencyTenants;
use App\Traits\Hashidable;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use ScoutElastic\Searchable;
use Spark;

class Campaign extends Model
{
    use Searchable, Hashidable, HasAgencyTenants, HasFactory;

    const MAX_EMAILS_PER_DAY = 30;

    /**
     * The available values for the campaign status.
     *
     * @var array
     */
    public static $statuses = [
        'DRAFT'     => 'warning',
        'EDITED'    => 'info',
        'RUNNING'   => 'success',
        'PAUSED'    => 'danger',
        'COMPLETED' => 'primary',
        'STOPPED'   => 'danger',
        'ARCHIVED'  => 'default',
    ];

    protected $guarded = ['id'];

    protected $casts = [
        'deleted_at' => 'datetime',
    ];

    //protected $withCount = ['prospects'];

    protected $appends = ['carbon_timezone', 'hashid'];

    // Used to create the index in Elasticsearch.
    protected $indexConfigurator = CampaignIndexConfigurator::class;

    // Default elastic scout search rule.
    protected $searchRules = [CampaignSearchRule::class];

    // Field mapping for Elasticsearch.
    protected $mapping = CampaignIndexConfigurator::MAPPING;



    /**
     * Get the indexable data array for the model to be used by Laravel Scout.
     *
     * @return array
     */
    public function toSearchableArray()
    {
        return [
            'name'      => $this->name,
            'team_id'   => $this->team_id,
            'agency_id' => $this->agency_id,
        ];
    }

    /**
     * Add a field to return the timezone in Carbon format.
     *
     * @return string
     */
    public function getCarbonTimezoneAttribute()
    {
        return str_replace(' ', '_', $this->timezone);
    }

    /**
     * Scope a query to only include inactive campaigns,
     * ie. paused, stopped, completed, or archived.
     *
     * @param $query
     * @return mixed
     */
    public function scopeInactive($query)
    {
        return $query->whereNotIn('status', ['DRAFT', 'EDITED', 'RUNNING']);
    }

    /**
     * Scope a query to only include campaigns that are not archived.
     *
     * @param $query
     * @return mixed
     */
    public function scopeNotArchived($query)
    {
        return $query->where('status', '!=', 'ARCHIVED');
    }

    /**
     * Scope a query to only include active campaigns.
     *
     * @param $query
     * @return mixed
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', ['DRAFT', 'EDITED', 'RUNNING']);
    }

    /**
     * Scope a query to only include underway campaigns.
     *
     * @param $query
     * @return mixed
     */
    public function scopeUnderway($query)
    {
        return $query->whereIn('status', ['DRAFT', 'EDITED']);
    }

    /**
     * Scope a query to only include running campaigns.
     * Check not only that they have status `running`,
     * but also that they are ready to run.
     *
     * @param $query
     * @return mixed
     */
    public function scopeRunning($query)
    {
        return $query->where('status', 'RUNNING')->where('is_ready', true);
    }

    /**
     * Scope a query to only include campaigns that have not yet finished.
     * ie. draft, edited, running, paused or stopped.
     *
     * @param $query
     * @return mixed
     */
    public function scopeUnFinished($query)
    {
        return $query->whereNotIn('status', ['COMPLETED', 'ARCHIVED']);
    }

    /**
     * Filter campaigns by team id.
     *
     * @param mixed $query
     * @param mixed $intTeamId
     * @return mixed $query
     */
    public function scopeOfTeam($query, $intTeamId)
    {
        if ($intTeamId) {
            return $query->where('team_id', $intTeamId);
        }

        return $query;
    }

    /**
     * Filter campaigns by email account.
     *
     * @param mixed $query
     * @param int $emailAccountId
     * @return mixed $query
     */
    public function scopeOfEmailAccount($query, $emailAccountId)
    {
        if ($emailAccountId) {
            return $query->whereHas('emailAccounts', function ($query) use ($emailAccountId) {
                $query->where('id', $emailAccountId);
            });
        }

        return $query;
    }

    /**
     * Filter campaigns by email account.
     *
     * @param mixed $query
     * @param int $emailAccountId
     * @return mixed $query
     */
    public function scopeOfTeamUser($query, $teamUserId)
    {
        // dd($teamUserId);
        if ($teamUserId) {
            return $query->whereHas('campaignActivities', function ($query) use ($teamUserId) {
                $query->where('user_id', $teamUserId)
                    ->where('activity', 'CREATED');
            });
        }

        return $query;
    }

    /**
     * Filter campaigns by agency.
     *
     * @param mixed $query
     * @param int $agencyId
     * @return mixed $query
     */
    public function scopeOfAgency($query, $agencyId)
    {
        if ($agencyId) {
            return $query->where('agency_id', $agencyId);
        }

        return $query;
    }

    /**
     * Filter campaigns by search keywords.
     *
     * @param  mixed  $query
     * @param  mixed  $strKeywords
     * @param  array  $filters
     * @return mixed $query
     */
    public function scopeOfKeywords($query, $strKeywords, $filters = [])
    {
        if ($strKeywords) {

            if (config('scout.search')) {

                $scoutQuery = $this->search($strKeywords);

                foreach ($filters as $key => $value) {
                    if (!empty($value)) {
                        $scoutQuery = $scoutQuery->where($key, $value);
                    };
                }

                $size = $scoutQuery->count();

                return $query->whereIn('id', $scoutQuery->take($size)->keys());
            }

            return $query->where('name', 'like', '%'.$strKeywords.'%');
        }
    }
    /**
     * Filter campaigns by status.
     *
     * @param mixed $query
     * @param mixed $strStatus
     * @return mixed $query
     */
    public function scopeOfStatus($query, $strStatus)
    {
        if ($strStatus) {
            return $query->where('status', $strStatus);
        }

        // return all campaign except archived
        return $query->where('status', '!=', 'ARCHIVED');
    }

    /**
     * NOT USED ANYMORE!
     * Get the campaign end date based on subscription creation date.
     *
     * @return mixed
     */
    //public function getCompletesAtAttribute()
    //{
    //    $subscribedDate = $this->team->subscription()->created_at;
    //
    //    $now = Carbon::now('UTC');
    //    $months = $subscribedDate->diffInMonths($now) + 1;
    //    $renewsDate = $subscribedDate->copy()->addMonths($months);
    //    if ($renewsDate->month > $now->month && $renewsDate->day != $subscribedDate->day) {
    //        $renewsDate = $renewsDate->subMonth()
    //            ->lastOfMonth()
    //            ->addHours($subscribedDate->hour)
    //            ->addMinutes($subscribedDate->minute)
    //            ->addSeconds($subscribedDate->second);
    //    }
    //
    //    return $renewsDate;
    //}

    /**
     * DEPRECATED: Not used
     * Set the Campaign's monthly prospects based on the current subscription plan.
     */
//    public function setMonthlyProspects()
//    {
//        $current_plan = $this->team->subscription()->stripe_price;
//
//        $prospects = Spark::teamPlans()->filter(function ($plan) use ($current_plan) {
//            return $plan->id == $current_plan;
//        })->first()->attributes['prospects'];
//
//        if ($prospects) {
//            $this->monthly_prospects = $prospects;
//            $this->save();
//        }
//    }

    /**
     * A Campaign belongs to a Team.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function team()
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * A Campaign belongs to an Agency.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function agency()
    {
        return $this->belongsTo(Agency::class);
    }

    /**
     * A Campaign belongs to many Geographies.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function geographies()
    {
        return $this->belongsToMany(Geography::class);
    }

    /**
     * A Campaign belongs to many Industries.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function industries()
    {
        return $this->belongsToMany(Industry::class);
    }

    /**
     * A Campaign belongs to many Job Functions.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function jobFunctions()
    {
        return $this->belongsToMany(JobFunction::class);
    }

    /**
     * A Campaign belongs to many Seniorities.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function seniorities()
    {
        return $this->belongsToMany(Seniority::class);
    }

    /**
     * A Campaign belongs to many Company Sizes.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function companySizes()
    {
        return $this->belongsToMany(CompanySize::class);
    }

    /**
     * A Campaign uses an email account.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function emailAccounts()
    {
        return $this->belongsToMany(EmailAccount::class)
            ->withPivot('active')
            ->withTimestamps()
            ->wherePivot('active', true);
    }

    public function inactiveEmailAccounts()
    {
        return $this->belongsToMany(EmailAccount::class)
            ->withPivot('active')
            ->withTimestamps()
            ->wherePivot('active', false);
    }


    public function allEmailAccounts()
    {
        return $this->belongsToMany(EmailAccount::class)
            ->withPivot('active')
            ->withTimestamps();
    }

    public function allLinkedinAccounts()
    {
        return $this->belongsToMany(LinkedinAccount::class)
            ->withPivot('active')
            ->withTimestamps();
    }

    public function linkedinAccounts()
    {
        return $this->belongsToMany(LinkedinAccount::class)
            ->withPivot('active')
            ->withTimestamps()
            ->wherePivot('active', true);
    }

    public function inactiveLinkedinAccounts()
    {
        return $this->belongsToMany(LinkedinAccount::class)
            ->withPivot('active')
            ->withTimestamps()
            ->wherePivot('active', false);
    }

    public function accounts($type)
    {
        if ($type == 'email') {
            return $this->emailAccounts();
        } elseif ($type == 'linkedin') {
            return $this->linkedinAccounts();
        }
    }

    public function campaignStages()
    {
        return $this->hasMany(CampaignStage::class);
    }

    /**
     * A Campaign has many email templates.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function emailTemplates()
    {
        return $this->hasMany(EmailTemplate::class);
    }

    public function linkedinMessageTemplates()
    {
        return $this->hasMany(LinkedinMessageTemplate::class);
    }

    /**
     * A Campaign has many email messages.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function emailMessages()
    {
        return $this->hasMany(EmailMessage::class);
    }

    /**
     * A Campaign has many email threads.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function emailThreads()
    {
        return $this->hasMany(EmailThread::class);
    }

    public function linkedinMessages()
    {
        return $this->hasMany(LinkedinMessage::class);
    }

    public function linkedinThreads()
    {
        return $this->hasMany(LinkedinThread::class);
    }

    public function chatgptPrompts()
    {
        return $this->hasMany(ChatgptPrompt::class);
    }

    /**
     * The campaign stats.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function stats()
    {
        return $this->hasOne(Stats::class);
    }

    public function dailyStats()
    {
        return $this->hasMany(DailyStats::class);
    }

    public function dailyCampaignStats()
    {
        return $this->hasMany(DailyCampaignStats::class);
    }

    /**
     * A campaign has many prospects.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function prospects()
    {
        return $this->hasMany(Prospect::class);
    }

//    /**
//     * A campaign has many prospects that are NOT imported from linkedin profiles.
//     *
//     * @return \Illuminate\Database\Eloquent\Relations\HasMany
//     */
//    public function emailProspects()
//    {
//        return $this->hasMany(Prospect::class)->whereNull('linkedin_url');
//    }
//
//    /**
//     * A campaign has many prospects that are imported from linkedin profiles.
//     *
//     * @return \Illuminate\Database\Eloquent\Relations\HasMany
//     */
//    public function linkedinProspects()
//    {
//        return $this->hasMany(Prospect::class)->whereNotNull('linkedin_url');
//    }

    /**
     * Get the prospects activities for campaign.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasManyThrough
     */
    public function prospectActivities()
    {
        return $this->hasManyThrough(ProspectActivity::class, Prospect::class);
    }

    /**
     * Get the responses for campaign.
     * Response includes prospects that has replies in this campaign
     * Regardless of their current status/interest as long as the reply
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasManyThrough
     */
    public function prospectResponses()
    {
        return $this->hasManyThrough(ProspectActivity::class, Prospect::class)
            ->where('activity', 'ProspectReplied');
    }

    /**
     * A campaign has many prospects.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function prospects_trimmed()
    {
        return $this->hasMany(Prospect::class)->select(['emails_sent', 'completed_steps', 'status']);
    }

    /**
     * A campaign has many snippets (custom fields).
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function snippets()
    {
        return $this->hasMany(Snippet::class);
    }

    /**
     * A campaign has many schedules.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function schedules()
    {
        return $this->hasMany(Schedule::class);
    }

    /**
     * We are logging the prospect activity.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function campaignActivities()
    {
        return $this->hasMany(CampaignActivity::class);
    }

    /**
     * A Campaign may have email accounts that are not used any more.
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function emailHistories()
    {
        return $this->inactiveEmailAccounts()->get();
        //$allEmailAccountIds = $this->emailThreads()->distinct()->pluck('email_account_id')->toArray();
        //$currentEmailAccountIds = $this->emailAccounts()->pluck('id')->toArray();
        //
        //return EmailAccount::whereIn('id', $allEmailAccountIds)->whereNotIn('id', $currentEmailAccountIds)->get();
    }

    public function linkedinAccountHistories()
    {
        return $this->inactiveLinkedinAccounts()->get();
    }

    public function stagesWithTemplates()
    {
        return $this->campaignStages()
            ->orderBy('number')
            ->with([
                'emailTemplates' => function ($query) {
                    $query->active()->orderBy('number');
                },
                'linkedinMessageTemplates' => function ($query) {
                    $query->active()->orderBy('number');
                },
            ]);
    }

    /**
     * Return all email accounts that might have been used by the campaign,
     * including those that are not currently used but are found in the campaign's threads.
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function getAllEMailAccounts()
    {
        $allEmailAccountIds = $this->emailAccounts()->pluck('id')
            ->concat($this->inActiveEmailAccounts()->pluck('id'))
            ->toArray();

        return EmailAccount::whereIn('id', $allEmailAccountIds);
    }

    /**
     * Check if the campaign is running.
     *
     * @return bool
     */
    public function isRunning()
    {
        return 'RUNNING' == $this->status && $this->is_ready;
    }

    /**
     * Check if this campaign has completed sending emails.
     *
     * @return bool
     */
    public function isComplete()
    {
        return $this->status != 'DRAFT' && !$this->is_on_standby && $this->prospects()->incomplete()->doesntExist();
    }

    /**
     * Get this campaign completion percentage.
     *
     * @return float
     */
    public function getCompletedPercentage()
    {
        $totalEmailMessagesSent = $this->campaignStages()->sum('emails_sent');
        $totalToEmailMessagesToSend = $this->campaignStages()->sum('emails_to_send');

        $totalLinkedinMessagesSent = $this->campaignStages()->sum('linkedin_messages_sent');
        $totalLinkedinMessagesToSend = $this->campaignStages()->sum('linkedin_messages_to_send');

        $totalSent = $totalEmailMessagesSent + $totalLinkedinMessagesSent;
        $totalToSend = $totalToEmailMessagesToSend + $totalLinkedinMessagesToSend;

        if (($totalSent + $totalToSend) == 0) {
            return 0;
        }

        return floor(($totalSent / ($totalSent + $totalToSend) * 100));
    }

    /**
     * Get the total pending scheduled prospects for today.
     *
     * @return int
     */
    public function getPendingProspects()
    {
        // If the campaign is not running, return 0.
        if ($this->status != 'RUNNING') {

            return 0;
        }

        $day = Carbon::now()->timezone($this->carbon_timezone)->shortEnglishDayOfWeek;

        return $this->campaignStages->sum(function ($campaignStage) use ($day) {
            // If this campaign stage has no schedules for today, add 0.
            $pendingSchedules = $campaignStage->schedules()
                ->where('day', $day)
                ->whereDate('starts_at', '<=', Carbon::now()->timezone($this->carbon_timezone)->endOfDay()->timezone(config('app.timezone')))
                ->get();

            if ($pendingSchedules->count() == 0) {

                return 0;
            }

            $pending = $pendingSchedules->sum('amount_to_send') - $pendingSchedules->sum('amount_sent');

            // Don't go over the total available prospects for each stage.
            $availableProspects = $campaignStage->getPendingProspects();
            if ($availableProspects < $pending) {
                $pending = $availableProspects;
            }

            return $pending;
        });
    }

    /**
     * Update campaign as complete.
     */
    public function clearSchedulesAndComplete()
    {
        $campaignScheduler = new CampaignSchedulerService($this);
        $campaignScheduler->clear();
        if ($this->status != 'ARCHIVED' && $this->status != 'COMPLETED') {
            $this->update(['status' => 'COMPLETED']);
            NotifyOfCampaignInterrupt::dispatch($this, 'COMPLETED', 'The campaign has completed all sequence steps.');
        }
    }

    /**
     * Remove one or more email accounts from the campaign.
     *
     * @param int|array  $accountId
     * @param string  $accountType
     */
    public function removeAccounts($accountId, $accountType) {
        // Turn it into collection from int or array.
        $accounts = collect($accountId);

        $accounts->each(function ($id) use ($accountType) {
            if ($accountType === 'email') {
                $this->emailAccounts()->updateExistingPivot($id, ['active' => false]);
            } elseif ($accountType === 'linkedin') {
                $this->linkedinAccounts()->updateExistingPivot($id, ['active' => false]);
            }
        });
    }

    /**
     * Add an email account to the campaign.
     *
     * @param int|array  $accountId
     * @param string  $accountType
     */
    public function addAccounts($accountId, $accountType) {
        $accounts = collect($accountId);

        $accounts->each(function ($id) use ($accountType) {
            if ($accountType === 'email') {
                if ($this->inactiveEmailAccounts()->where('id', $id)->exists()) {
                    $this->inactiveEmailAccounts()->updateExistingPivot($id, ['active' => true]);
                } elseif ($this->emailAccounts()->where('id', $id)->doesntExist()) {
                    $this->emailAccounts()->attach($id, ['active' => true]);
                }
            } elseif ($accountType === 'linkedin') {
                if ($this->inactiveLinkedinAccounts()->where('id', $id)->exists()) {
                    $this->inactiveLinkedinAccounts()->updateExistingPivot($id, ['active' => true]);
                } elseif ($this->LinkedinAccounts()->where('id', $id)->doesntExist()) {
                    $this->LinkedinAccounts()->attach($id, ['active' => true]);
                }
            }
        });
    }

    public function setActiveEmailAccounts($accountIds) {
        $currentAccounts = $this->allEmailAccounts()
            ->get()
            ->keyBy("id")
            ->map(function ($account) {
                return $account->pivot->active;
            });

        // Determine updates and inserts needed
        $updates = [];
        foreach ($currentAccounts as $id => $active) {
            $shouldBeActive = in_array($id, $accountIds);
            if ($active != $shouldBeActive) {
                $updates[$id] = ["active" => $shouldBeActive ? 1 : 0];
            }
        }

        // Determine new inserts needed
        $inserts = [];
        $newRelations = array_diff($accountIds, $currentAccounts->keys()->all());
        foreach ($newRelations as $id) {
            $inserts[$id] = ["active" => 1];
        }

        // Execute necessary updates
        foreach ($updates as $id => $data) {
            $this->allEmailAccounts()->updateExistingPivot($id, $data);
        }

        // Insert new records
        foreach ($inserts as $id => $data) {
            $this->allEmailAccounts()->attach($id, $data);
        }
    }

    public function hasRunningAccounts()
    {
        $hasRunningEmailAccounts = true;
        $hasRunningLinkedinAccounts = true;

        if ($this->campaignStages()->email()->exists()) {
            $hasRunningEmailAccounts = $this->emailAccounts()->running()->exists();
        }
        if ($this->campaignStages()->linkedin()->exists()) {
            $hasRunningLinkedinAccounts = $this->linkedinAccounts()->running()->exists();
        }

        return $hasRunningEmailAccounts && $hasRunningLinkedinAccounts;
    }

//    public function updateEmailHistories() TODO: Check after merge if needed or if we need to add something similar for LinkedIn
//    {
//        $allEmailAccounts = $this->emailThreads()->distinct()->pluck('email_account_id');
//
//        $currentEmailAccounts = $this->emailAccounts()->pluck('id');
//        $currentInactiveEmailAccounts = $this->inactiveEmailAccounts()->pluck('id');
//        $allCurrentAccounts = $currentEmailAccounts->merge($currentInactiveEmailAccounts);
//
//        $allEmailAccounts->reject(function ($id) use ($allCurrentAccounts) {
//            return $allCurrentAccounts->contains($id);
//        })->each(function ($id) {
//            $this->emailAccounts()->attach($id, ['active' => false]);
//        });
//    }


    /**
     * Get any errors that may occur when we try to start a campaign due to missing accounts, templates, prospects etc
     *
     * @return array
     */
    public function getInitializeErrors() {
        $errors = [];

        // Validate custom fields used in templates
        $defaultFields = EmailTemplate::DEFAULT_FIELDS;
        $snippetFields = $this->snippets()->pluck('name')->toArray();
        $unmatchedFields = $this->emailTemplates
            ->reduce(function($carry, $emailTemplate) use ($defaultFields, $snippetFields) {
                $content = $emailTemplate->msg;

                // validate parsing of subject if not thread sequence
                if(!$emailTemplate->attach_follow) {
                    $content = $content . $emailTemplate->subject;
                }

                $parsedContent = parseMergeFieldContent($content, $snippetFields, $defaultFields);
                $reqFields = getRequiredMergeFields($parsedContent);

                foreach ($reqFields as $field) {
                    if (!in_array($field,$defaultFields) &&
                        !in_array($field, $snippetFields) &&
                        !Str::startsWith($field, 'today') &&
                        !Str::contains($field, '||') &&
                        $emailTemplate->enabled
                    ) {
                        $carry = $carry + 1;
                    }
                }

                return $carry;
            }, 0);

        if (!$this->is_on_standby && $this->prospects()->doesntExist()) {
            $errors[] = 'You must have at least one contact.';
        }

        if (!$this->hasRunningAccounts()) {
            $errors[] = 'You must have at least one active account.';
        }

        if ($this->campaignStages()->withoutTemplates()->exists()) {
            $errors[] = 'Make sure campaign steps are not empty.';
        }

        if ($this->emailTemplates()->hasDefaultSubject()->exists()) {
            $errors[] = 'Make sure active email templates are not using the default subject.';
        }

        if (!$this->max_emails_per_day) {
            $errors[] = 'Check that new contacts are added to the sequence per day.';
        }

        if ($unmatchedFields && $unmatchedFields > 0) {
            $errors[] = 'Check the merge fields used in the email templates for typos.';
        }

        if ($this->emailTemplates()->exists() && $this->prospects()->whereNull('email')->exists()) {
            $errors[] = 'Make sure that all contacts have an email address to receive email messages';
        }

        if ($this->linkedinMessageTemplates()->exists() && $this->prospects()->whereNull('linkedin_hash')->whereNull('linkedin_public_url')->exists()) {
            $errors[] = 'Make sure that all contacts have a Linked In profile url to receive Linked In messages';
        }

        return $errors;
    }
}
