<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class CsvUpload extends Model
{
    protected $guarded = [];

    protected $casts = [
        'headers' => 'json'
    ];

    public function csvUploadData()
    {
        return $this->hasMany(CsvUploadData::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function agency()
    {
        return $this->belongsTo(Agency::class);
    }

    public function team()
    {
        return $this->belongsTo(Team::class);
    }

}
