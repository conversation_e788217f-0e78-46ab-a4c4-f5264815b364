<?php

namespace App;

use App\Traits\Hashidable;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use ScoutElastic\Searchable;
use Illuminate\Database\Eloquent\Model;
use App\Elastic\SearchRules\EmailMessageSearchRule;
use App\Elastic\IndexConfigurators\EmailMessageIndexConfigurator;

class EmailMessage extends Model
{
    use Searchable, Hashidable;

	protected $guarded = ['id'];

    protected $with = ['campaign'];

    protected $appends = ['snippet_date'];

    protected $casts = [
        'submitted_at' => 'datetime:d M, h:i A',
        'recipients'   => 'array',
        'references'   => 'array',
        'opened_at' => 'datetime',
        'clicked_at' => 'datetime',
    ];

    // update the thread when a new message was created
    protected $touches = ['emailThread'];

    // Used to create the index in Elasticsearch.
    protected $indexConfigurator = EmailMessageIndexConfigurator::class;

    // Default elastic scout search rule.
    protected $searchRules = [EmailMessageSearchRule::class];

    // Field mapping for Elasticsearch.
    protected $mapping = EmailMessageIndexConfigurator::MAPPING;

    /**
     * Get the indexable data array for the model to be used by Laravel Scout.
     *
     * @return array
     */
    public function toSearchableArray()
    {
        return [
            'from_name'         => $this->from_name,
            'to_name'           => $this->to_name,
            'from'              => $this->from,
            'to'                => $this->to,
            'subject'           => $this->subject,
            'message'           => $this->message,
            'snippet'           => $this->snippet,
            'origin'            => $this->origin,
            'agency_id'         => $this->agency_id,
            'campaign_id'       => $this->campaign_id,
            'email_account_id'  => $this->email_account_id,
            'email_template_id' => $this->email_template_id,
            'nylas_message_id'  => $this->nylas_message_id,
            'nylas_thread_id'   => $this->nylas_thread_id,
            //'prospect_id'       => $this->prospect_id, // Do we need this to filter a prospect's messages?
        ];
    }

    /**
     * Campaign of a message.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function campaign()
    {
        return $this->belongsTo(Campaign::class);
    }

    /**
     * Prospect of a message.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function prospect()
    {
        return $this->belongsTo(Prospect::class);
    }

    /**
     * EmailAccount of a message.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function emailAccount()
    {
        return $this->belongsTo(EmailAccount::class);
    }

    /**
     * EmailThread of a message.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function emailThread()
    {
        return $this->belongsTo(EmailThread::class);
    }

    /**
     * A message may belong to an email template.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function emailTemplate()
    {
        return $this->belongsTo(EmailTemplate::class);
    }

    /**
     * A message may be related to one or more prospect activities.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function prospectActivities()
    {
        return $this->hasMany(ProspectActivity::class);
    }

    public function pendingEmailMessage()
    {
        return $this->hasOne(PendingEmailMessage::class);
    }

    /**
     * Filter messages by agency id.
     *
     * @param mixed $query
     * @param mixed $intAgencyId
     * @return mixed $query
     */
    public function scopeOfAgency($query, $intAgencyId)
    {
        if ($intAgencyId) {
            return $query->where('agency_id', $intAgencyId);
        }

        return $query;
    }

    /**
     * Filter messages by email_account_id.
     *
     * @param mixed $query
     * @param int $emailAccountId
     * @return mixed $query
     */
    public function scopeOfEmailAccount($query, $emailAccountId)
    {
        if ($emailAccountId) {
            return $query->where('email_account_id', $emailAccountId);
        }

        return $query;
    }

    /**
     * Filter emails by campaigns.
     *
     * @param mixed $query
     * @param mixed $arrCampaignIds
     * @return mixed $query
     */
    public function scopeOfCampaigns($query, $arrCampaignIds)
    {
        if($arrCampaignIds !== null)
        {
            return $query->whereIn('campaign_id', $arrCampaignIds);
        }

        return $query;
    }

    public function scopeClicked($query)
    {
        return $query->where('is_clicked', true);
    }

    public function scopeOpened($query)
    {
        return $query->where('is_opened', true);
    }

    /**
     * Get the Formatted date of this email vue UI.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function getSnippetDateAttribute()
    {
        if ($this->campaign) {
            return $this->submitted_at->timezone($this->campaign->carbon_timezone)->format('d M, h:i A');
        } else {
            return '';
        }
    }

    /**
     * Get the message text in a format used as quoted text in replies.
     *
     * @return \Illuminate\Support\Collection
     */
    public function getQuotedMessageData()
    {
        $quotedText = '<div class="quote">'.
            '<div dir="ltr">On '.
            $this->submitted_at->timezone($this->campaign->carbon_timezone)->toDayDateTimeString().' '.
            $this->from_name.
            ' &lt;<a href="mailto:'.$this->from.'" target="_blank">'.
            $this->from.
            '</a>&gt; wrote:<br></div>
            <blockquote class="quote"><div dir="ltr">'.$this->message.'</div></blockquote>
            </div>';
        $subject = $this->subject;

        return collect(['subject' => $subject, 'text' => $quotedText]);
    }

    /**
     * Delete an email message and update related Prospect, ProspectActivity and EmailThread.
     * Used for clearing queued messages that were never sent.
     *
     * @return void
     * @throws \Throwable
     */
    public function destroyAndUpdateRelatedModels(string $error = null)
    {

        DB::beginTransaction();
        try {
            FailedEmailMessage::create([
                'email_account_id' => $this->email_account_id,
                'message_id' => $this->message_id,
                'campaign_id' => $this->campaign_id,
                'email_template_id' => $this->email_template_id,
                'subject' => $this->subject,
                'to' => $this->to,
                'error' => $error
            ]);

            $this->delete();

            if ($this->email_template_id && $this->prospect->completed_steps == $this->emailTemplate->campaign_stage_number) {
                $prospect = $this->prospect;
                $prospect->completed_steps = safeDecrement($prospect->completed_steps);
                $prospect->emails_sent = safeDecrement($prospect->emails_sent);
    //            $prospect->next_step = safeDecrement($prospect->next_step);
    //            $prospect->is_complete = false; // Always? What if suppressed?
                if ($prospect->completed_steps == 0) {
    //                $prospect->contacted = false;
                    $prospect->last_contacted = null;
                }

                if ($prospect->emails_sent == 0) {
                    $prospect->email_account_id = null;
                    $prospect->last_email_step = 0;
                } else {
                    $prospect->last_email_step = CampaignStage::ofCampaign($prospect->campaign_id)
                        ->email()
                        ->orderBy('number')
                        ->offset($prospect->emails_sent-1)
                        ->first()
                        ->number;
                }

                $prospect->save(); // ProspectSavingListener will also update contacted, next_step, is_complete

                ProspectActivity::where('email_message_id', $this->id)
                    ->where('prospect_id', $prospect->id)
                    ->delete();

                $emailThread = EmailThread::where('id', $this->email_thread_id)
                    ->withCount(['emailMessages'])
                    ->first();

                if ($emailThread && $emailThread->email_messages_count === 0) {
                    $emailThread->delete();
                }
            }

            PendingEmailMessage::where('email_message_id', $this->id)->delete();

            DB::commit();

        } catch (\Throwable $e) {
            Log::error("Error clearing queued message ({$this->id}): {$e->getMessage()}");
            DB::rollBack();

            throw($e);
        }
    }
}
