<?php

namespace App;

use Illuminate\Support\Str;
use <PERSON><PERSON>\Spark\Plan;

class AgencyPlan extends Plan
{

    /**
     * The plan's slug.
     *
     * @var string
     */
    public $slug;

    /**
     * The plan's tag line or description.
     *
     * @var string
     */
    public $tagLine;

    /**
     * Create a new plan instance.
     *
     * @param  string  $name
     * @param  string  $id
     * @return void
     */
    public function __construct($name, $id)
    {
        $this->id = $id;
        $this->name = $name;
    }


    public function slug($slug)
    {
        $this->slug = $slug;

        return $this;
    }

    public function tagLine($description)
    {
        $this->tagLine = $description;

        return $this;
    }

    public function hasContactsLimit()
    {
        return $this->isWavoVersion(3);
    }

    public function hasAccountsLimit()
    {
        return $this->isWavoVersion(3);
    }

    public function isWavoVersion($version)
    {
        return $this->attributes['wavo_version'] == $version;
    }

    /**
     * Get the array form of the plan for serialization.
     *
     * @return array
     */
    public function jsonSerialize()
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'price' => $this->price,
            'trialDays' => $this->trialDays,
            'interval' => $this->interval,
            'features' => $this->features,
            'active' => $this->active,
            'attributes' => $this->attributes,
            'type' => $this->type,
            'slug' => $this->slug,
            'tagLine' => $this->tagLine,
        ];
    }
}
