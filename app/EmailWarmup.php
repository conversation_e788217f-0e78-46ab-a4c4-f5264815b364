<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class EmailWarmup extends Model
{
    const WARMUP_CONFIG = [
        ['min_days'=>0, 'max_days'=>2, 'send_limit'=>15, 'min_interval'=>450, 'max_interval'=>500],
        ['min_days'=>3, 'max_days'=>6, 'send_limit'=>25, 'min_interval'=>450, 'max_interval'=>500],
        ['min_days'=>7, 'max_days'=>9, 'send_limit'=>40, 'min_interval'=>350, 'max_interval'=>400],
        ['min_days'=>10, 'max_days'=>16, 'send_limit'=>70, 'min_interval'=>250, 'max_interval'=>300],
        ['min_days'=>17, 'max_days'=>30, 'send_limit'=>100, 'min_interval'=>200, 'max_interval'=>250],
        ['min_days'=>31, 'max_days'=>59, 'send_limit'=>150, 'min_interval'=>120, 'max_interval'=>240],
        // after 60 days, turn off email warmup and use the manual send_limit/min/max/ in emailAccount
    ];

    protected $guarded = ['id'];

    protected $casts = [
        'last_paused_at' => 'datetime',
        'last_started_at' => 'datetime',
    ];

    public function emailAccount()
    {
        return $this->belongsTo(EmailAccount::class);
    }
}
