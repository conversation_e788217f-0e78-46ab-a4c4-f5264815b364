<?php

namespace App\Services;

use App\Contact;
use App\Models\StoreLeads\Domain;
use App\Models\StoreLeads\Search;
use App\Models\StoreLeads\AgencyDomain;
use App\Models\StoreLeads\AgencyEmail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class DomainContactsFinderService{
    private $domain;
    private $search;
    private $apiSearchPerformed = false;
    private $apiSearchSuccess = false;

    protected const SEARCHED_MONTHS_AGO = 4;
    protected const VERIFIED_MONTHS_AGO = 6;

    public function __construct(Domain $domain, Search $search)
    {
        $this->domain = $domain;
        $this->search = $search;
    }

    public function searchFromDatabase()
    {
        $this->logInfo("searchFromDatabase: {$this->domain->id} - {$this->domain->name}");

        if ($this->domain->contacts_count == 0) {
            return [
                "emails" => [],
                "confidence" => null,
                "status" => 'error',
                "source" => 'database',
            ];
        }
        $this->logInfo("searchFromDatabase: {$this->domain->name} Results: {$this->domain->contacts_count}");
        // Run verification for contacts that are not verified
        Contact::where('domain_id', $this->domain->id)->each(function($contact) {
            if (empty($contact->verified_at) || $contact->verified_at < now()->subMonths(self::VERIFIED_MONTHS_AGO)) {
                // No verification, let's try our verification service
                $emailSource = !empty($contact->source) ? 'db-'.$contact->source : 'db';
                $verifyResult = app(EmailVerifierService::class)->verifyEmail($contact->email, $emailSource);
                Contact::where('id', $contact->id)->update([
                    'verified_at' => now(),
                    'verification_error' => $verifyResult['error'],
                ]);
                $this->logInfo('searchFromDatabase updated contact verified_at: '.$contact->email.' - '.$verifyResult['error']);
            }
        });

        // Run enrichment for contacts that are not enriched
        $enrichmentService = app()->make(EmailEnrichmentService::class, [
            'domain' => $this->domain,
            'searchId' => $this->search->id,
            'enableEnrichmentApis' => false
        ]);
        Contact::where('domain_id', $this->domain->id)->each(function($contact) use ($enrichmentService) {
            if (empty($contact->enrichment_source) && empty($contact->first_name) && empty($contact->last_name)) {
                // No enrichment, let's try our enrichment service
                // TODO: Add max tries and cooling period to this process
                // Consider enriched if we have both name + position
                $googleEnriched = $enrichmentService->searchGoogle(['email' => $contact->email]);
                if (!empty($googleEnriched['first_name'])) {
                    Contact::where('id', $contact->id)->update([
                        'first_name' => $googleEnriched['first_name'],
                        'last_name' => $googleEnriched['last_name'],
                        'position' => $googleEnriched['position'],
                        'linkedin' => $googleEnriched['linkedin'],
                        'enrichment_source' => $googleEnriched['enrichment_source'],
                    ]);
                    $this->logInfo('searchFromDatabase updated contact enrichment: '.$contact->email.' - '.$googleEnriched['enrichment_source']);
                }
            }
        });

        $emails = Contact::on('mysql::write')
            ->where('domain_id', $this->domain->id)
            ->get()
            ->map(function($contact) {
                return [
                    'email' => $contact->email,
                    'first_name' => $contact->first_name,
                    'last_name' => $contact->last_name,
                    'position' => $contact->position,
                    'linkedin' => $contact->linkedin,
                    'source' => $contact->source,
                    'enrichment_source' => $contact->enrichment_source,
                    'verified_at' => $contact->verified_at,
                    'verification_error' => $contact->verification_error,
                ];
            })
            ->all();

        $this->logInfo('Got ' . count($emails) . ' results from database');

        // Check if we have any verified emails
        $verifiedEmails = array_filter($emails, function($email) {
            if (empty($email['verified_at'])) {
                return false;
            }
            $verificationErrors = [
                'invalid',
                'catch-all',
                'server-not-exist',
                'server-misbehaving',
                'api-error',
            ];
            if (in_array($email['verification_error'], $verificationErrors)) {
                return false;
            }
            return true;
        });
        $verificationStatus = !empty($verifiedEmails) ? 'verified' : 'not_verified';
        $this->logInfo('Got ' . count($verifiedEmails) . ' verified emails from database');
        return [
            "emails" => $emails,
            "confidence" => $verificationStatus,
            "status" => $verificationStatus,
            "source" => 'database'
        ];
    }

    public function searchFromApi()
    {
        $result = [
            "emails" => [],
            "confidence" => null, // 'verified' , 'not_verified'
            "status_code" => null, // 200, 400, 401, 402, 403, 404, 410, 418
            "status" => "not_found", // 'not_found', 'verified', 'not_verified', 'retry', 'billing_error', 'apikey_error', 'unhandled_error'
            "source" => null // 'tomba', 'anymail_finder'
        ];

        $emailsFinder = app()->make(EmailsFinderService::class, [
            'enableEnrichmentApis' => false
        ]);
        $emailsFinder->setSearchId($this->search->id);
        $monthsAgo = now()->subMonths(self::SEARCHED_MONTHS_AGO);
        $contactsQuality = new DomainContactsQualityService();

        if ($contactsQuality->shouldSearchNewContacts($this->domain, $monthsAgo)) {
            $emailsFinder->setDomain($this->domain);
            $apiResult =  $emailsFinder->searchEmails($this->domain);
            $this->apiSearchPerformed = true;

            if(!in_array($apiResult['status'], ['apikey_errors', 'retry_errors'])) {
                $this->apiSearchSuccess = true;
            }

            return $apiResult;
        } else {
            $this->logInfo('not searching from API: '.$this->domain->name);
        }

        return $result;
    }

    public function mergeResults(array $dbResult, array $apiResult): array
    {
        // Create a map of existing emails for easy lookup
        $emailMap = [];
        foreach ($dbResult['emails'] as $email) {
            $emailMap[$email['email']] = $email;
        }

        // Add or update with new emails from API
        foreach ($apiResult['emails'] as $newEmail) {
            $email = $newEmail['email'];

            if (isset($emailMap[$email])) {
                // Update existing email with better information if available
                $emailMap[$email] = $this->mergeEmailData($emailMap[$email], $newEmail);
            } else {
                // Add new email
                $emailMap[$email] = $newEmail;
            }
        }

        return [
            'emails' => array_values($emailMap),
            'confidence' => $this->getBestConfidence($dbResult['confidence'], $apiResult['confidence']),
            'status' => $this->getBestStatus($dbResult['status'], $apiResult['status']),
            'source' => $apiResult['source'] ?? $dbResult['source'],
        ];
    }

    public function saveEmailResults($searchResult)
    {
        $agencyDomain = null;

        if(!count($searchResult['emails'])) {
            $agencyDomain = AgencyDomain::create([
                "agency_id" => $this->search->agency_id,
                "domain_id" => $this->domain->id,
                "email_search_status" => "failed",
                "agency_search_id" => $this->search->id,
            ]);

            $this->updateDomainStats(false);

            return [
                "agency_domain_id" => $agencyDomain->id,
                "status" => 'error',
            ];
        }

        // Get the root and normalized domain and store them
        // TODO: Do we still need this? We are now using cluster domains!
        $domainData = app(DomainNormalizationService::class)
            ->normalizeAndStore($this->domain->name, $this->domain->id);

        try {
            DB::connection('mysql::write')->transaction(function () use ($searchResult, $domainData, &$agencyDomain) {
                // Create agency domain record
                $agencyDomain = AgencyDomain::create([
                    "agency_id" => $this->search->agency_id,
                    "domain_id" => $this->domain->id,
                    "root_domain_id" => $domainData['root_domain_id'],
                    "email_search_status" => "success",
                    "agency_search_id" => $this->search->id,
                ]);

                // Initialize tracking variables for primary email selection
                $primaryEmailConfidence = 0;
                $primaryEmailSeniority = 0;
                $primaryEmailIndex = 0;

                // Process each email result
                $agencyEmailsData = [];
                $contactEnrichmentService = app()->make(ContactEnrichmentService::class);
                $domainIsEnriched = false;

                foreach ($searchResult['emails'] as $key => $emailData) {
                    $source = !empty($searchResult['source']) ? $searchResult['source'] : null;
                    $suggestedNames = !empty($emailData['suggested_names']) ? json_encode($emailData['suggested_names']) : null;


                    // Check if contact exists
                    $contact = $contactEnrichmentService->enrichFromDomainSource(
                        array_merge($emailData, [
                            'domain' => Str::after($emailData['email'], '@'),
                            'domain_id' => $this->domain->id,
                            'source' => $source
                        ])
                    );

                    // Evaluate if this should be the primary email
                    $isPrimary = false;
                    $emailConfidence = $contact->email_confidence;
                    $emailSeniority = $contact->seniority_id;

                    // Set as primary if it's the first email or has better criteria
                    if ($key == 0 || ($emailSeniority > $primaryEmailSeniority &&
                            ($emailConfidence >= Contact::CONFIDENCE_THRESHOLDS['HIGH'] || $emailConfidence > $primaryEmailConfidence))) {

                        $primaryEmailConfidence = $emailConfidence;
                        $primaryEmailSeniority = $emailSeniority;
                        $primaryEmailIndex = $key;
                        $isPrimary = true;
                    }

                    // Prepare AgencyEmail data
                    $agencyEmailsData[] = [
                        "agency_id" => $this->search->agency_id,
                        "domain_id" => $this->domain->id,
                        "agency_domain_id" => $agencyDomain->id,
                        "email" => $emailData['email'],
                        "contact_id" => $contact->id, // Link to Contact
                        "primary" => $isPrimary,
                        "first_name" => !empty($emailData['first_name']) ? $emailData['first_name'] : null,
                        "last_name" => !empty($emailData['last_name']) ? $emailData['last_name'] : null,
                        "linkedin" => !empty($emailData['linkedin']) ? $emailData['linkedin'] : null,
                        "position" => !empty($emailData['position']) ? $emailData['position'] : null,
                        "suggested_names" => $suggestedNames,
                        "source" => !empty($source) ? $source : null,
                        "enrichment_source" => !empty($emailData['enrichment_source']) ? $emailData['enrichment_source'] : null,
                        'verified_at' => !empty($emailData['verified_at']) ? $emailData['verified_at'] : null,
                        'is_imported' => 0,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];

                    // Check if email has first name and position to consider the domain as enriched
                    if ((!empty($contact->first_name) || !empty($contact->last_name)) && (!empty($contact->title) || !empty($contact->position))) {
                        $domainIsEnriched = true;
                    }
                }

                // Second pass: Ensure only one email is set as primary
                $primaryFound = false;
                foreach ($agencyEmailsData as $index => &$emailData) {
                    if ($index === $primaryEmailIndex) {
                        $emailData['primary'] = 1;
                        $primaryFound = true;
                    } else {
                        $emailData['primary'] = 0;
                    }
                }

                // Fallback if no primary was selected
                if (!$primaryFound && !empty($agencyEmailsData)) {
                    $agencyEmailsData[0]['primary'] = 1;
                }

                DB::connection('mysql::write')->table('sl_agency_emails')->insert($agencyEmailsData);

                // Update primary email for agency domain
                $primaryEmail = AgencyEmail::where('agency_domain_id', $agencyDomain->id)
                    ->where('primary', 1)
                    ->first();

                $agencyDomain->update([
                    'current_email_id' => $primaryEmail?->id
                ]);

                if ($domainIsEnriched) {
                    $this->domain->update([
                        'is_enriched' => 1
                    ]);
                }

                // TODO LIST_IDS syncwithoutdetach $this->listIds
                if(!empty($this->search->list_ids)) {
                    $agencyDomain->agencyLists()->syncWithoutDetaching($this->search->list_ids);
                }

                $this->updateDomainStats(true);
            });
        } catch (\InvalidArgumentException $e) {
            // Log invalid domain and continue
            $this->logError("Invalid domain skipped: " . $e->getMessage());
            return;
        } catch (\Throwable $e) {
            // Log storage error and re-throw to trigger transaction rollback
            $this->logError("Failed to store domain: " . $e->getMessage());
            throw $e;
        }

        return [
            "agency_domain_id" => $agencyDomain->id,
            "status" => 'success',
        ];
    }

    protected function updateDomainStats(bool $foundContacts): void
    {
        if ($this->apiSearchPerformed && $this->apiSearchSuccess) {
            $this->domain->last_searched_at = now();

            if ($foundContacts) {
                // Found contacts: reset failures
                $this->domain->failed_searches = 0;
                $this->domain->has_contact = true;
            } else {
                // No contacts found: increment failures
                $this->domain->failed_searches += 1;
                $this->domain->has_contact = false;
            }
        }

        // Note: do not update last_searched_at or failed_searches on API errors,
        // because the job will retry or delay instead.

        $this->domain->save();
    }

    /**
     * Merge data for the same email from different sources
     */
    protected function mergeEmailData(array $existing, array $new): array
    {
        // Fields to check for updates
        $fields = ['first_name', 'last_name', 'full_name', 'position',
            'linkedin', 'verified_at', 'verification_error'];

        $isUpdated = false;
        foreach ($fields as $field) {
            // Prefer non-empty values from new data
            if (!empty($new[$field]) && (
                    empty($existing[$field]) ||
                    $this->isNewerOrBetterData($existing, $new, $field)
                )) {
                $existing[$field] = $new[$field];
                $isUpdated = true;
            }
        }

        // Set better enrichment source
        if ($isUpdated) {
            $existing['enrichment_source'] = $new['enrichment_source'] ?? $existing['enrichment_source'];
        }

        return $existing;
    }

    /**
     * Determine if new data should replace existing data
     */
    protected function isNewerOrBetterData(array $existing, array $new, string $field): bool
    {
        // For verification data, check dates
        if ($field === 'verified_at' && !empty($existing['verified_at']) && !empty($new['verified_at'])) {
            $existingDate = is_string($existing['verified_at'])
                ? Carbon::parse($existing['verified_at'])
                : $existing['verified_at'];

            $newDate = is_string($new['verified_at'])
                ? Carbon::parse($new['verified_at'])
                : $new['verified_at'];

            return $newDate->isAfter($existingDate);
        }

        return false; // Default to keeping existing data
    }

    /**
     * Get the best confidence level
     */
    protected function getBestConfidence(?string $existing, ?string $new): ?string
    {
        $priority = ['verified' => 3, 'not_verified' => 2, 'error' => 1, null => 0];

        $existingPriority = $priority[$existing] ?? 0;
        $newPriority = $priority[$new] ?? 0;

        return $existingPriority > $newPriority ? $existing : $new;
    }

    /**
     * Get the best status
     */
    protected function getBestStatus(?string $existing, ?string $new): ?string
    {
        $priority = ['verified' => 3, 'not_verified' => 2, 'not_found' => 1, 'error' => 0, null => 0];

        $existingPriority = $priority[$existing] ?? 0;
        $newPriority = $priority[$new] ?? 0;

        return $existingPriority > $newPriority ? $existing : $new;
    }

    protected function logInfo($msg)
    {
        $message = "DomainContactsFinderService Domain-{$this->domain->name} ";
        if (!empty($this->searchId)) {
            $message .= "AgencySearch-$this->searchId ";
        }
        Log::info("$message: $msg");
    }

    protected function logError($msg)
    {
        $message = "DomainContactsFinderService Domain-{$this->domain->name} ";
        if (!empty($this->searchId)) {
            $message .= "AgencySearch-$this->searchId ";
        }
        Log::error("$message: $msg");
    }
}