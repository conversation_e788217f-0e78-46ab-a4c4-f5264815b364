<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

class SeniorityRankingService
{
    private const SENIORITY_RANKS = [
        'employee' => 10,
        'senior' => 20,
        'manager' => 30,
        'head' => 40,
        'director' => 50,
        'partner' => 60,
        'c_suite' => 70,
        'owner' => 80,
    ];
    // What is good seniority to contact for a lead? Maybe >= 50 ?

    private const POSITION_PATTERNS = [
        'owner' => [
            '/owner/i',
            '/founder/i',
            '/co-founder/i',
            '/ceo.*owner/i',
            '/proprietor/i',
            '/entrepreneur/i',
            '/president.*owner/i',
            '/founding partner/i'
        ],
        'c_suite' => [
            '/\b(ceo|cfo|cto|coo|cio|cmo|cdo|cpo|vp)\b/i',  // Added CDO (Digital/Data), CPO (Product)
            '/c_suite/i',
            '/chief .*officer/i',
            '/president/i',
            '/executive director/i',
            '/managing director/i'
        ],
        'partner' => [
            '/\bpartner\b/i',
            '/managing partner/i',
            '/senior partner/i',
            '/partnership/i',
            '/associate partner/i'
        ],
        'director' => [
            '/\bdirector\b/i',
            '/\bdirecteur\b/i',
            '/deputy director/i',
            '/regional director/i',
            '/division director/i'
        ],
        'head' => [
            '/\bhead of\b/i',
            '/\bhead,/i',
            '/^head\b/i',
            '/department head/i',
            '/team head/i',
            '/division head/i',
            '/unit head/i'
        ],
        'manager' => [
            '/\bmanager\b/i',
            '/\bmanaging\b/i',
            '/\bmanagement\b/i',
            '/project manager/i',
            '/account manager/i',
            '/supervisor/i',
            '/team leader/i',
            '/operations manager/i',
            '/program manager/i'
        ],
        'senior' => [
            '/\bsenior\b/i',
            '/\bsr\.?\b/i',  // Matches sr or sr.
            '/\blead\b/i',
            '/\bprincipal\b/i',
            '/^sr /i',
            '/senior specialist/i',
            '/senior consultant/i',
            '/team lead/i',
            '/technical lead/i',
            '/subject matter expert/i',
            '/^sme\b/i',
            '/executive/i'
        ],
        'employee' => [
            '/specialist/i',
            '/analyst/i',
            '/consultant/i',
            '/coordinator/i',
            '/associate/i',
            '/assistant/i',
            '/developer/i',
            '/engineer/i',
            '/administrator/i',
            '/representative/i',
            '/officer/i',
            '/agent/i',
            '/advisor/i',
            '/technician/i',
            '/staff/i',
            '/support/i',
            '/designer/i',
            '/researcher/i',
            '/professional/i',
            '/strategist/i',
            '/coordinator/i',
        ]
    ];

    /**
     * Determine the seniority level based on the position title.
     *
     * @param string|null $position The position title to evaluate.
     * @return int The seniority level as an integer.
     */
    public function determineSeniority(?string $position): int
    {
        if (!$position) {

            return 0;
        }

        foreach (self::POSITION_PATTERNS as $level => $patterns) {
            foreach ($patterns as $pattern) {
                if (preg_match($pattern, $position)) {
                    // Log matches for debugging if needed
                    // Log::debug("Position matched", ['position' => $position, 'level' => $level, 'pattern' => $pattern]);
                    return self::SENIORITY_RANKS[$level];
                }
            }
        }

        $this->logUnmatchedPosition($position);
        return self::SENIORITY_RANKS['employee']; // Default to employee level
    }

    private function logUnmatchedPosition(?string $position): void
    {
        // Only log positions that seem meaningful (more than just spaces or special characters)
        if (trim($position) !== '' && !preg_match('/^[\s\W]+$/', $position)) {
            Log::warning('Unmatched position title', [
                'position' => $position,
                'cleaned_position' => preg_replace('/[^a-zA-Z0-9\s]/', '', $position)
            ]);
        }
    }

    /**
     * Helper method to get seniority label from ID
     */
    public static function getSeniorityLabel(int $seniorityId): ?string
    {
        $flipped = array_flip(self::SENIORITY_RANKS);
        return $flipped[$seniorityId] ?? null;
    }
}
