<?php

namespace App\Services;

use App\Contact;
use App\Models\EmailVerification;
use App\Models\StoreLeads\Domain;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use InvalidArgumentException;

class ContactEnrichmentService
{
    private $seniorityService;

    public function __construct(SeniorityRankingService $seniorityService)
    {
        $this->seniorityService = $seniorityService;
    }

    public function enrichFromDomainSource(array $data): ?Contact
    {
        if (!$this->validateRequired($data)) {
            return null;
        }

        $contact = Contact::where('email', $data['email'])
            ->orWhere(function ($q) use($data) {
                // or check if there's an apollo contact by matching data
                $q->where('domain_id', $data['domain_id'])
                    ->where('first_name', $data['first_name'])
                    ->where('last_name', $data['last_name'])
                    ->where('email', 'like', 'email_placeholder_%');
            })
            ->first();

        $isNewContact = !$contact;

        if ($isNewContact) {
            $contact = new Contact();
        }

        $data = $this->prepareData($data);

        if ($isNewContact) {
            $this->processEnrichmentData($contact, $data);
        } else {
            $this->enrichContact($contact, $data);
        }

//        $this->updateSeniority($contact, $isNewContact);
        $contact->save();

        return $contact;
    }

    public function enrichFromExternalSource(array $data): ?Contact
    {
        if (!$this->validateRequired($data)) {
            return null;
        }

        $contact = Contact::where('email', $data['email'])->first();
        $isNewContact = !$contact;

        if ($isNewContact) {
            $contact = new Contact();
        }

        $data = $this->prepareExternalData($data);

        if ($isNewContact) {
            $this->processEnrichmentData($contact, $data);
        } else {
            $this->enrichContact($contact, $data);
        }

        $contact->save();

        return $contact;
    }

    private function validateRequired(array $data): bool
    {
        return !empty($data['email'])
            && !empty($data['domain_id'])
            && !empty($data['domain']);
    }

    private function prepareData(array $data): array
    {
        // Fetch additional domain data to fill in fields in Contact
        $domain = Domain::find($data['domain_id']);
        $domainData = $domain->domainData->data;

        $data['company_name'] = $domain->merchant_name ?? null;
        if (empty($data['company_name'])) {
            $data['company_name'] = $domainData['merchant_name'] ?? $domainData['title'] ?? null;
        }
        $data['website'] = $domainData['name'] ?? $domain['name'] ?? null;
        $data['country'] = $domainData['country_code'] ?? null;
        $region = $domainData['region'] ?? null;
        $data['address'] = (!empty($data['country']) && !empty($region))
            ? trim($data['country'] . ', ' . $region, ', ')
            : null;
        $data['phone'] = null;
        if (!empty($domainData['contact_info'])) {
            $phoneData = Arr::first($domainData['contact_info'], function ($contact, int $key) {
                return $contact['type'] == 'phone';
            });

            if (!empty($phoneData['value'])) {
                $data['phone'] = $phoneData['value'];
            }
        }

        if (!empty($data['source']) && empty($data['external_email_confidence'])) {
            if (!empty($data['verified_at'])) {
                $data['external_email_confidence'] = $this->getVerifiedConfidence($data['source']);
            } else {
                $data['external_email_confidence'] = 50;
            }
        }

        if (!empty($data['verified_at']) && empty($data['verification_error'])) {
            $data['verification_error'] = $this->getVerificationError($data['email'], $data['source']);
        }

        return [
            'email' => $data['email'],
            'domain' => $data['domain'],
            'domain_id' => $data['domain_id'],
            'first_name' => $this->normalizeString($data['first_name'] ?? null),
            'last_name' => $this->normalizeString($data['last_name'] ?? null),
            'title' => $this->normalizeString($data['title'] ?? null),
            'company_name' => $this->normalizeString($data['company_name'] ?? null),
            'website' => $this->normalizeString($data['website'] ?? null, false),
            'phone' => $data['phone'] ?? null,
            'country' => $data['country'] ?? null,
            'address' => $data['address'] ?? null,
            'source' => $this->normalizeString($data['source'] ?? null, false),
            'enrichment_source' => $this->normalizeString($data['enrichment_source'] ?? null, false),
            'position' => $this->normalizeString($data['position'] ?? null),
            'linkedin' => $this->normalizeString($data['linkedin'] ?? null, false),
            'verified_at' => $this->normalizeDate($data['verified_at'] ?? null),
            'verification_error' => $this->normalizeString($data['verification_error'] ?? null, false),
            'external_email_confidence' => $data['external_email_confidence'] ?? null,
        ];
    }

    protected function prepareExternalData(array $data): array
    {
        // Fetch additional domain data to fill in fields in Contact
        $domain = Domain::find($data['domain_id']);
        $domainData = $domain->domainData->data;

        $companyName = $domain->merchant_name ?? null;
        if (empty($companyName)) {
            $companyName = $domainData['merchant_name'] ?? $domainData['title'] ?? null;
        }
        if (!empty($companyName)) {
            $data['company_name'] = $companyName;
        }
        $data['website'] = $domainData['name'] ?? $domain['name'] ?? null;
        $data['country'] = $domainData['country_code'] ?? $data['country'] ?? null;
        // build data['address'] from data city, state, country if available
        $data['address'] = trim(implode(', ', array_filter([
            $data['city'] ?? null,
            $data['state'] ?? null,
            $data['country'] ?? $domainData['country_code'] ?? null
        ])), ', ');
        if (empty($data['phone'])) {
            if (!empty($domainData['contact_info'])) {
                $phoneData = Arr::first($domainData['contact_info'], function ($contact, int $key) {
                    return $contact['type'] == 'phone';
                });

                if (!empty($phoneData['value'])) {
                    $data['phone'] = $phoneData['value'];
                }
            }
        }

        return [
            'email' => $data['email'],
            'domain' => $data['domain'],
            'domain_id' => $data['domain_id'],
            'first_name' => $this->normalizeString($data['first_name'] ?? null),
            'last_name' => $this->normalizeString($data['last_name'] ?? null),
            'title' => $this->normalizeString($data['title'] ?? null),
            'company_name' => $this->normalizeString($data['company_name'] ?? null),
            'website' => $this->normalizeString($data['website'] ?? null, false),
            'phone' => $data['phone'] ?? null,
            'country' => $data['country'] ?? null,
            'address' => $data['address'] ?? null,
            'source' => $this->normalizeString($data['source'] ?? null, false),
            'enrichment_source' => $this->normalizeString($data['enrichment_source'] ?? null, false),
            'position' => $this->normalizeString($data['position'] ?? null),
            'linkedin' => $this->normalizeString($data['linkedin'] ?? null, false),
            'verified_at' => $this->normalizeDate($data['verified_at'] ?? null),
            'verification_error' => $this->normalizeString($data['verification_error'] ?? null, false),
            'external_email_confidence' => $data['external_email_confidence'] ?? null,
        ];
    }

    private function normalizeString(?string $value, bool $capitalize = true): ?string
    {
        if ($value === null || trim($value) === '') {
            return null;
        }
        $value = strtolower(trim($value));
        return $capitalize ? ucwords($value) : $value;
    }

    private function normalizeDate(?string $date): ?string
    {
        if (empty($date)) {
            return null;
        }

        try {
            return Carbon::parse($date)->format('Y-m-d H:i:s');
        } catch (\Exception $e) {
            return null;
        }
    }

    private function processEnrichmentData(Contact $contact, array $data): void
    {
        $contact->fill([
            'email' => $data['email'],
            'domain' => $data['domain'],
            'domain_id' => $data['domain_id'],
            'first_name' => $data['first_name'],
            'last_name' => $data['last_name'],
            'title' => $data['position'],
            'company_name' => $data['company_name'],
            'website' => $data['website'],
            'phone' => $data['phone'],
            'country' => $data['country'],
            'address' => $data['address'],
            'source' => $data['source'],
            'enrichment_source' => $data['enrichment_source'],
            'position' => $data['position'],
            'linkedin' => $data['linkedin'],
            'verified_at' => $data['verified_at'],
            'verification_error' => $data['verification_error'],
            'external_email_confidence' => $data['external_email_confidence'],
        ]);
    }

    private function enrichContact(Contact $contact, array $data): void
    {
        $hasNewerData = $this->hasNewerData($contact, $data);

        $externalSources = ['apollo_data'];
        if (in_array($data['source'], $externalSources)) {
            $shouldUpdate = $this->shouldUpdateContactFromExternalData($contact, $data);
        } else {
            $shouldUpdate = $this->shouldUpdateContact($contact, $data);
        }

        $fieldsToUpdate = [
            'email' => $data['email'],
            'domain' => $data['domain'],
            'domain_id' => $data['domain_id'],
            'first_name' => $data['first_name'],
            'last_name' => $data['last_name'],
            'title' => $data['position'],
            'company_name' => $data['company_name'],
            'website' => $data['website'],
            'phone' => $data['phone'],
            'country' => $data['country'],
            'address' => $data['address'],
            'source' => $data['source'],
            'enrichment_source' => $data['enrichment_source'],
            'position' => $data['position'],
            'linkedin' => $data['linkedin'],
            'verified_at' => $data['verified_at'],
            'verification_error' => $data['verification_error'],
            'external_email_confidence' => $data['external_email_confidence']
        ];

        foreach ($fieldsToUpdate as $field => $value) {
            if ($this->shouldUpdateField($contact, $field, $value, $shouldUpdate, $hasNewerData)) {
                $contact->$field = $value;
            }
        }
    }

    private function shouldUpdateContact(Contact $contact, array $data): bool
    {
        $isBasicSource = in_array($contact->source, ['csv_import', 'user', null]);
        $hasHigherConfidence = !empty($data['external_email_confidence']) && ($contact->external_email_confidence < $data['external_email_confidence']);

        // Contact not verified and data is verified OR
        // Contact previous source was user/csv OR
        // Data has higher external_email_confidence
        return (!$contact->verified_at && !empty($data['verified_at'])) || $isBasicSource || $hasHigherConfidence;
    }

    private function shouldUpdateContactFromExternalData(Contact $contact, array $data): bool
    {
        $shouldUpdate = $this->shouldUpdateContact($contact, $data);
        $isEnriched = !empty($contact->first_name) && (!empty($contact->title) || !empty($contact->position));
        if (!empty($contact->created_at)) {
            $isOldContact = $contact->created_at->isBefore(now()->subYear());
        } else {
            $isOldContact = true;
        }

        // To update contact from external data it should meet requirements of shouldUpdateContact and
        // not be enriched or be old
        return $shouldUpdate && (!$isEnriched || $isOldContact);
    }

    private function hasNewerData(Contact $contact, array $data): bool
    {
        if (empty($data['verified_at'])) {
            return false;
        }

        if (!$contact->verified_at) {
            return true;
        }

        try {
            return Carbon::parse($data['verified_at'])->isAfter($contact->verified_at);
        } catch (\Exception $e) {
            return false;
        }
    }

    private function shouldUpdateField(Contact $contact, string $field, $value, bool $shouldUpdate, bool $hasNewerData): bool
    {
        return empty($contact->$field) || $shouldUpdate || $hasNewerData;
    }

    // Deprecated: Handled in ContactObserver
    private function updateSeniority(Contact $contact, bool $isNewContact): void
    {
        if (($contact->isDirty('position') || $isNewContact) && !empty($contact->position)) {
            $contact->seniority_id = $this->seniorityService->determineSeniority($contact->position);
        }
    }

    /**
     * Get verification error from EmailVerification model for a verified email
     *
     * @param string $email
     * @param string|null $source
     * @return string|null
     */
    private function getVerificationError(string $email, ?string $source = null): ?string
    {
        $query = EmailVerification::where('email', $email)
            ->where('verification', true)
            ->orderByDesc('id');

        // If we have a source, try to match by source too
        if ($source) {
            $query->where('source', $source);
        }

        $verification = $query->first();

        return $verification ? $verification->error : null;
    }

    private function getVerifiedConfidence($source)
    {
        // set confidence depending on agencyEmail source
        if ($source === 'anymailfinder') {
            return 75;
        } elseif ($source === 'tomba') {
            return 75;
        } else { //null source, either of the two
            return 70;
        }
    }
}
