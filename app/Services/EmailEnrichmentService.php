<?php

namespace App\Services;

use App\Exceptions\ExternalServiceBillingException;
use App\Exceptions\ExternalServiceErrorException;
use App\Models\ApiUsageLog;
use App\Models\ExternalService;
use App\Models\StoreLeads\Domain;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Client as HttpClient;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Str;

class EmailEnrichmentService
{
    const PASSING_SCORE = 35;

    private $domain;
    private $searchId;
    private $emails;
    private $commonNames;
    private $enableEnrichmentApis;
    protected $externalServiceHandler;

    protected const DB_READ_CONNECTION = 'mysql::read';

    public function __construct(ExternalServiceHandler $externalServiceHandler, Domain $domain = null, $searchId = null, $enableEnrichmentApis = true)
    {
        $this->domain = $domain;
        $this->searchId = $searchId;
        $this->enableEnrichmentApis = $enableEnrichmentApis;
        $this->commonNames = commonCompanyEmailNames();
        $this->externalServiceHandler = $externalServiceHandler;
    }

    public function enrichEmails($emails)
    {
        $this->logInfo("enrichEmails started");
        $this->emails = $emails;

        $enrichedEmails = [];
        $rapidApiEmployees = []; // save result of step4 so it doesn't call in all iteration of same domain

        foreach ($emails as $emailData) {
            $googleEnriched = $this->searchGoogle($emailData);

            if (!empty($googleEnriched['full_name'])) {
                $enrichedEmails[] = $googleEnriched;
                continue;
            }

            // stop here if enrichement APIs is off for search
            if (!$this->enableEnrichmentApis) {
                $this->logInfo("stop at searchGoogle, enableEnrichmentApis is off");
                $enrichedEmails[] = $emailData;
                continue;
            }

            // Don't run reverseContact for common names like info, support, contact, etc.
            $emailName = strtolower(explode('@', $emailData['email'])[0]);
            if (in_array($emailName, $this->commonNames)) {
                $this->logInfo("stop at commonNames: {$emailName}");
                $enrichedEmails[] = $emailData;
                continue;
            }

            $enriched = $this->reverseContactEmailLookup($emailData['email']);
            // $enriched = $this->reverseContactEmailLookupDemo($emailData['email']);

            if (!empty($enriched['person'])) {
                // process person data and done
                $this->logInfo("Found reverseContact person data");
                $enrichedEmails[] = $enriched['person'];
                continue;
            }
            /* Disable RapidAPI for now
            } elseif (empty($enriched['person']) && !empty($enriched['company'])) {
                // if no person but has companyData - Step 4
                if (empty($rapidApiEmployees)) {
                    $rapidApiEmployees = $this->enrichRapidApiEmployeeSearch($enriched['company']['linkedInId']);
                }

                // get employee details with Step 5
                $employee = $this->matchEmailToEmployees($emailData['email'], $rapidApiEmployees);

                // if email matched in employees, done
                if (!empty($employee)) {
                    array_push($enrichedEmails, $employee);
                    continue;
                } else {
                    // step6?
                }
            } else {
                // get the "linkedinCompanyId" using step 3 methods
                $linkedinUrlName = null;
                $linkedinCompanyId = null;

                $contacts = $this->domain->domainData->data['contact_info'] ?? [];

                $linkedin = Arr::first($contacts, function ($contact) {
                    return strtolower($contact['type']) == 'linkedin';
                });

                if (!empty($linkedin['value'])) {
                    $linkedinUrlName = explode('/company/', $linkedin['value'])[1] ?? null;
                }

                if ($linkedinUrlName) {
                    $linkedinCompanyId = $this->enrichRapidApiCompanyByLinkedinUrl($linkedinUrlName);
                } else {
                    $linkedinCompanyId = $this->enrichRapidApiCompanyByDomain();
                }

                // save employee result so it wont fetch for all emails of the same domain
                if (empty($rapidApiEmployees) && $linkedinCompanyId) {
                    $rapidApiEmployees = $this->enrichRapidApiEmployeeSearch($linkedinCompanyId);
                }

                // get employee details with Step 5
                $employee = $this->matchEmailToEmployees($emailData['email'], $rapidApiEmployees);

                // if email matched in employees, done
                if (!empty($employee)) {
                    array_push($enrichedEmails, $employee);
                    continue;
                }
            }
            */

            Log::info("No enrichment data found for email: {$emailData['email']}");
            Log::info($emailData);

            // if no data, just return it back to the list
            $enrichedEmails[] = $emailData;
        }

        return $enrichedEmails;
    }

    /**
     * Step 2. If we have an email with no data, use ReverseContact "email lookup" to find person with position and company LI data
     * - If a person is found, then stop here and update person and company data ** SUCCESS **
     * - If it finds only company data, then go to step 4 (get company people from LI)
     * - If it finds nothing then continue to step 3
     */
    public function reverseContactEmailLookup($emailAddress)
    {
        $this->logInfo("reverseContactEmailLookup");

        $apiKey = config('app.emailFinders.reverseContact.apiKey');
        $http = new HttpClient(['base_uri' => 'https://api.reversecontact.com/']);

        $status = null;
        $response = null;
        $personData = null;
        $companyData = null;
        $statusCode = null;

        $query = [
            'email' => $emailAddress,
            'companyDomain' => $this->domain->name,
            'apikey' => $apiKey
        ];
        // Log::info($query);

        try {
            $response = $http->get('enrichment', [
                'headers' => [
                    'apikey' => $apiKey
                ],
                'query' => $query
            ]);

            $status = $response->getStatusCode();

        } catch (RequestException $e) {
            $status = $e->getResponse()->getStatusCode();
            $response = null;

            $this->logError("Failed with status {$status}");
            $this->logError(json_encode($e->getMessage(), JSON_INVALID_UTF8_IGNORE));
        }

        if ($status == 401) {
            // Unauthorized	Invalid token provided in Authorization header
            $notificationMessage = 'API Key Error. Please validate API Keys.';
            $this->externalServiceHandler->notifyServiceError('reverse_contact', 'Email Enrichment: ReverseContact', 401, $notificationMessage);
            $this->externalServiceHandler->setServiceError('reverse_contact', 401, 'critical', $notificationMessage);

        } elseif (in_array($status, [402])) {
            // Payment Required	You don’t have enough credits on your account to perform the request
            $notificationMessage = 'Payment Required Error. Please check billing.';
            $this->externalServiceHandler->notifyServiceError('reverse_contact', 'Email Enrichment: ReverseContact', 402, $notificationMessage);
            $this->externalServiceHandler->setServiceError('reverse_contact', 402, 'critical', $notificationMessage);

        } elseif (in_array($status, [403])) {
            // Forbidden The API key doesn’t have permissions to perform the request
            $notificationMessage = 'API Permissions Error. Check API Keys permissions.';
            $this->externalServiceHandler->notifyServiceError('reverse_contact', 'Email Enrichment: ReverseContact', 403, $notificationMessage);
            $this->externalServiceHandler->setServiceError('reverse_contact', 403, 'critical', $notificationMessage);

        } elseif (in_array($status, [429])) {
            // Too Many Requests The request was unacceptable due to too many request (see Rate limit bellow)
            // $notificationMessage = 'API Key Error. Please validate API Keys.';
            // $this->>$this->externalServiceHandler->$this->notifyServiceError('reverse_contact', 'Email Enrichment: ReverseContact', 401, $notificationMessage);
            // $this->externalServiceHandler->setServiceError('reverse_contact', 429, 'busy', $notificationMessage);

        } elseif ($status == 200 && $response) {
            $response->getBody()->rewind();
            $responseData = collect(json_decode($response->getBody()->getContents(), true));

            // save person data
            if (!empty($responseData['person'])) {
                $person = $responseData['person'];
                $position = null;

                if (!empty($person['positions']['positionHistory'][0]['title'])) {
                    $position = strtolower($person['positions']['positionHistory'][0]['title']);
                }

                $personData = array(
                    'email' => $emailAddress,
                    'first_name' => $person['firstName'],
                    'last_name' => $person['lastName'],
                    'full_name' => $person['firstName'] . ' '. $person['lastName'],
                    'position' => $position,
                    'linkedin' => $person['linkedInUrl'],
                    'enrichment_source' => 'reversecontact'
                );
            }

            // save company data
            if (!empty($responseData['company'])) {
                $company = $responseData['company'];

                $companyData = array(
                    'linkedInId' => $company['linkedInId'],
                    'name' => $company['name'],
                    'universalName' => $company['universalName'],
                    'linkedInUrl' => $company['linkedInUrl'],
                    'tagline' => $company['tagline'],
                    'description' => $company['description'],
                );
            }
        }

        if ($status == 200) {
            if (!empty($personData)) {
                $result = "success";
            } else {
                $result = "no person found";
            }
        } elseif ($status == 404) {
            $result = "no person found";
        } else {
            $result = "error";
        }
        ApiUsageLog::create([
            'api' => 'reversecontact',
            'uri' => 'enrichment',
            'status' => $status,
            'result' => $result,
        ]);

        if ($status != 200 && $status != 404) {
            if (in_array($status, [401, 402, 403])) {
                throw new ExternalServiceBillingException("ReverseContact Email Lookup failed with status: $status");
            } else {
                throw new ExternalServiceErrorException("ReverseContact Email Lookup failed with status: $status");
            }
        }

        return array(
            'person' => $personData,
            'company' => $companyData
        );
    }

    /**
     * Step 3.a Get company LI ID & LI url.
     * - If storeleads has company LI url -> use rapidapi Get Company Details to get company details (LI ID + up to 4 employees).
     */
    public function enrichRapidApiCompanyByLinkedinUrl($liUrlName)
    {
        // $liCompanyId = null;
        // $responseData = $this->testRapidCompanyByLinkedinUrl();
        // $liCompanyId = $responseData['data']['id'] ?? null;
        // return $liCompanyId;

        $this->logInfo("enrichRapidApiCompanyByLinkedinUrl start");
        $apiKey = config('app.emailFinders.rapidApi.apiKey');

        $http = new HttpClient(['base_uri' => 'https://linkedin-data-api.p.rapidapi.com/']);
        $status = null;
        $response = null;
        $liCompanyId = null;
        $query = [
            'username' => $liUrlName
        ];
        // Log::info($query);

        try {
            $response = $http->get('get-company-details', [
                'headers' => [
                    'x-rapidapi-host' => 'linkedin-data-api.p.rapidapi.com',
                    'x-rapidapi-key' => $apiKey
                ],
                'query' => $query
            ]);

            $status = $response->getStatusCode();

        } catch (RequestException $e) {
            $status = $e->getResponse()->getStatusCode();
            $response = null;

            $this->logError("failed with status {$status}");
            $this->logError(json_encode($e->getMessage(), JSON_INVALID_UTF8_IGNORE));
        }

        $this->logInfo("status:". $status);

        if ($status == 401) {
            // Unauthorized	Invalid token provided in Authorization header
            $notificationMessage = 'API Key Error. Please validate API Keys. (rockapis-rockapis-default/api/linkedin-data-api)';
            $this->externalServiceHandler->notifyServiceError('rapid_api', 'Email Enrichment: RapidAPI', 401, $notificationMessage);
            $this->externalServiceHandler->setServiceError('rapid_api', 401, 'critical', $notificationMessage);

        } elseif (in_array($status, [402])) {
            // Payment Required	You don’t have enough credits on your account to perform the request
            $notificationMessage = 'Payment Required Error. Please check billing. (rockapis-rockapis-default/api/linkedin-data-api)';
            $this->externalServiceHandler->notifyServiceError('rapid_api', 'Email Enrichment: RapidAPI', 402, $notificationMessage);
            $this->externalServiceHandler->setServiceError('rapid_api', 402, 'critical', $notificationMessage);

        } elseif (in_array($status, [403])) {
            // Forbidden The API key doesn’t have permissions to perform the request
            $notificationMessage = 'API Permission Error. You are not subscribed to this API. (rockapis-rockapis-default/api/linkedin-data-api)';
            $this->externalServiceHandler->notifyServiceError('rapid_api', 'Email Enrichment: RapidAPI', 403, $notificationMessage);
            $this->externalServiceHandler->setServiceError('rapid_api', 403, 'critical', $notificationMessage);

        } elseif (in_array($status, [429])) {
            // Too Many Requests	The request was unacceptable due to too many request (see Rate limit bellow)
        } elseif ($status == 200 && $response) {
            Log::info(200);
            $response->getBody()->rewind();
            $responseData = collect(json_decode($response->getBody()->getContents(), true));
            Log::info($responseData);

            if ($responseData['success']) {
                $liCompanyId = $responseData['data']['id'] ?? null;
            }

        } else {
            // Server Error	The request fail due to a server error.
        }

        return $liCompanyId;
    }

    /**
     * Step 3.b Get company LI ID & LI url.
     * - Else use Rapidapi realtime LI scrapper Get Company By Domain to find company LI from the company domain
     */
    public function enrichRapidApiCompanyByDomain()
    {
        // $liCompanyId = null;
        // $responseData = $this->testRapidCompanyByDomain();
        // $liCompanyId = $responseData['data']['id'] ?? null;
        // return $liCompanyId;

        $this->logInfo("enrichRapidApiCompanyByLinkedinUrl start");
        $apiKey = config('app.emailFinders.rapidApi.apiKey');

        $http = new HttpClient(['base_uri' => 'https://linkedin-data-api.p.rapidapi.com/']);
        $status = null;
        $response = null;
        $liCompanyId = null;
        $query = [
            'domain' => $this->domain->name
        ];
        // Log::info($query);

        try {
            $response = $http->get('get-company-by-domain', [
                'headers' => [
                    'x-rapidapi-host' => 'linkedin-data-api.p.rapidapi.com',
                    'x-rapidapi-key' => $apiKey
                ],
                'query' => $query
            ]);

            $status = $response->getStatusCode();

        } catch (RequestException $e) {
            $status = $e->getResponse()->getStatusCode();
            $response = null;

            Log::error("failed with status {$status}");
            Log::error(json_encode($e->getMessage(), JSON_INVALID_UTF8_IGNORE));
        }

        $this->logInfo("status: ".$status);

        if ($status == 401) {
            // Unauthorized	Invalid token provided in Authorization header
            $notificationMessage = 'API Key Error. Please validate API Keys.  (rockapis-rockapis-default/api/linkedin-data-api)';
            $this->externalServiceHandler->notifyServiceError('rapid_api', 'Email Enrichment: RapidAPI', 401, $notificationMessage);
            $this->externalServiceHandler->setServiceError('rapid_api', 401, 'critical', $notificationMessage);

        } elseif (in_array($status, [402])) {
            // Payment Required	You don’t have enough credits on your account to perform the request
            $notificationMessage = 'Payment Required Error. Please check billing.  (rockapis-rockapis-default/api/linkedin-data-api)';
            $this->externalServiceHandler->notifyServiceError('rapid_api', 'Email Enrichment: RapidAPI', 402, $notificationMessage);
            $this->externalServiceHandler->setServiceError('rapid_api', 402, 'critical', $notificationMessage);

        } elseif (in_array($status, [403])) {
            // Forbidden The API key doesn’t have permissions to perform the request
            $notificationMessage = 'API Permission Error. You are not subscribed to this API.  (rockapis-rockapis-default/api/linkedin-data-api)';
            $this->externalServiceHandler->notifyServiceError('rapid_api', 'Email Enrichment: RapidAPI', 403, $notificationMessage);
            $this->externalServiceHandler->setServiceError('rapid_api', 403, 'critical', $notificationMessage);

        } elseif (in_array($status, [429])) {
            // Too Many Requests	The request was unacceptable due to too many request (see Rate limit bellow)
        } elseif ($status == 200 && $response) {
            $response->getBody()->rewind();
            $responseData = collect(json_decode($response->getBody()->getContents(), true));
            Log::info($responseData);

            if ($responseData['success']) {
                $liCompanyId = $responseData['data']['id'] ?? null;
            }

        } else {
            // Server Error	The request fail due to a server error.
        }

        return $liCompanyId;
    }

    /**
     * Step 4. Get company people from company LI ID (required liId)
     * - Get list of company employees through rapidapi realitime LI scrapper Employee Search using company's LI ID
     */
    public function enrichRapidApiEmployeeSearch($liCompanyId)
    {
        // $responseData = $this->testRapidEmployees();
        // if ($responseData['success'] && !empty($responseData['data']['items'])) {
        //     return $responseData['data']['items'];
        // }

        $this->logInfo("enrichRapidApiEmployeeSearch start");
        $apiKey = config('app.emailFinders.rapidApi.apiKey');

        $http = new HttpClient(['base_uri' => 'https://linkedin-data-api.p.rapidapi.com/']);
        $status = null;
        $response = null;
        $employees = [];
        $query = [
            'companyId' => $liCompanyId
        ];
        // Log::info($query);

        try {
            $response = $http->get('search-employees', [
                'headers' => [
                    'x-rapidapi-host' => 'linkedin-data-api.p.rapidapi.com',
                    'x-rapidapi-key' => $apiKey
                ],
                'query' => $query
            ]);

            $status = $response->getStatusCode();

        } catch (RequestException $e) {
            $status = $e->getResponse()->getStatusCode();
            $response = null;

            Log::error("failed with status {$status}");
            Log::error(json_encode($e->getMessage(), JSON_INVALID_UTF8_IGNORE));
        }

        $this->logInfo("status: ".$status);

        if ($status == 401) {
            // Unauthorized	Invalid token provided in Authorization header
            $notificationMessage = 'API Key Error. Please validate API Keys.  (rockapis-rockapis-default/api/linkedin-data-api)';
            $this->externalServiceHandler->notifyServiceError('rapid_api', 'Email Enrichment: RapidAPI', 401, $notificationMessage);
            $this->externalServiceHandler->setServiceError('rapid_api', 401, 'critical', $notificationMessage);

        } elseif (in_array($status, [402])) {
            // Payment Required	You don’t have enough credits on your account to perform the request
            $notificationMessage = 'Payment Required Error. Please check billing. (rockapis-rockapis-default/api/linkedin-data-api)';
            $this->externalServiceHandler->notifyServiceError('rapid_api', 'Email Enrichment: RapidAPI', 402, $notificationMessage);
            $this->externalServiceHandler->setServiceError('rapid_api', 402, 'critical', $notificationMessage);

        } elseif (in_array($status, [403])) {
            // Forbidden The API key doesn’t have permissions to perform the request
            $notificationMessage = 'API Permission Error. You are not subscribed to this API. (rockapis-rockapis-default/api/linkedin-data-api)';
            $this->externalServiceHandler->notifyServiceError('rapid_api', 'Email Enrichment: RapidAPI', 403, $notificationMessage);
            $this->externalServiceHandler->setServiceError('rapid_api', 403, 'critical', $notificationMessage);

        } elseif (in_array($status, [429])) {
            // Too Many Requests	The request was unacceptable due to too many request (see Rate limit bellow)
        } elseif ($status == 200 && $response) {
            $response->getBody()->rewind();
            $responseData = collect(json_decode($response->getBody()->getContents(), true));
            Log::info($responseData);

            if ($responseData['success'] && !empty($responseData['data']['items'])) {
                $employees = $responseData['data']['items'];
            }

        } else {
            // Server Error	The request fail due to a server error.
        }

        return $employees;
    }

    /**
     * Step 5. Try to match names to emails
     * - search for name parts in emails found by anymail finder to match people to emails
     * - if this doesn't work and we can't match any of them, then move to next step
     */
    public function matchEmailToEmployees($email, $employees)
    {
        $emailName = explode("@", $email)[0];
        $emailEmployee = null;

        foreach ($employees as $employee) {

            if (Str::contains($emailName, [$employee['lastName'], $employee['firstName']], ignoreCase: true)) {
                $position = $employee['currentPositions'][0]['title'] ?? null;

                $emailEmployee = array(
                    'email' => $email,
                    'first_name' => $employee['firstName'],
                    'last_name' => $employee['lastName'],
                    'full_name' => $employee['fullName'],
                    'linkedin' => $employee['profile'],
                    'position' => $position,
                    'enrichment_source' => 'rapidapi'
                );

                break;
            }
        }

        return $emailEmployee;
    }

    public function searchGoogle($emailData)
    {
        $emailAddress = $emailData['email'];
        $this->logInfo("searchGoogle searching email: $emailAddress");

        $profile = $emailData;

        $emailName = explode('@', $emailAddress)[0];
        if (in_array(strtolower($emailName), $this->commonNames)) {
            $this->logInfo("searchGoogle email is in commonNames: {$emailName}");
            return $profile;
        }

        $notificationMessage = 'API Key Error. Please validate API Keys.';
        $apiKey = config('app.emailFinders.serper.apiKey');
        $http = new HttpClient(['base_uri' => 'https://google.serper.dev/']);
        $status = null;
        $response = null;
        $query = [
            'q' => $emailAddress
        ];

        try {
            $response = $http->post('search', [
                'headers' => [
                    'X-API-KEY' => $apiKey,
                    'Content-Type' => 'application/json'
                ],
                'json' => $query
            ]);

            $status = $response->getStatusCode();

        } catch (RequestException $e) {
            $response = null;
            $status = $e->getResponse()->getStatusCode();
            $errMsg = $e->getMessage();
            $errResponse = $e->getResponse() ? json_decode($e->getResponse()->getBody()->getContents(), true) : null;

            $this->logError("searchGoogle failed with status {$status}");
            $this->logError(json_encode($errMsg, JSON_INVALID_UTF8_IGNORE));

            if (!empty($errResponse['message'])) {
                $this->logError("searchGoogle failed with error: {$errResponse['message']}");
                $notificationMessage = "'{$errResponse['message']}'";
            }
        }

        ApiUsageLog::create([
            'api' => 'google-serper',
            'uri' => 'search',
            'status' => $status,
            'result' => $status == 200 ? 'success' : 'error',
        ]);

        if ($status == 200 && $response) {
            $response->getBody()->rewind();
            $responseData = collect(json_decode($response->getBody()->getContents(), true));
            $results = $responseData['organic'] ?? [];
            $parsedResult = $this->parseGoogleSearchResults($results, $emailAddress);

            if(!empty($parsedResult['full_name'])) {
                // overwrite profile with new data from google search for available fields
                $profile['full_name'] = $parsedResult['full_name'];
                $profile['first_name'] = $parsedResult['first_name'] ?? $profile['first_name'] ?? null;
                $profile['last_name'] = $parsedResult['last_name'] ?? $profile['last_name'] ?? null;
                $profile['position'] = $parsedResult['position'] ?? $profile['position'] ?? null;
                $profile['linkedin'] = $parsedResult['linkedin'] ?? $profile['linkedin'] ?? null;;
                $profile['enrichment_source'] = $parsedResult['enrichment_source'];

                $this->logInfo("searchGoogle found profile: {$profile['full_name']} from {$profile['enrichment_source']}");
            }
        } else {
            if ($status == 400 || $status == 401) {
                $this->externalServiceHandler->notifyServiceError('serper', 'Email Enrichment: Serper Google Search', $status, $notificationMessage);
                $this->externalServiceHandler->setServiceError('serper', $status, 'critical', $notificationMessage);
                throw new ExternalServiceBillingException("Serper Google Search failed with status: $status");
            } else {
                $this->externalServiceHandler->setServiceError('serper', $status, 'busy', $notificationMessage);
                throw new ExternalServiceErrorException("Serper Google Search failed with status: $status");
            }
        }

        // mock serper response
        // $search = $this->testSerperDev();
        // $results = $search['organic'];
        // $parsedResult = $this->parseGoogleSearchResults($results, $emailAddress);
        // if(!empty($parsedResult['full_name'])) {
        //     $profile = $parsedResult;
        // }

        return $profile;
    }

    public function parseGoogleSearchResults($results, $emailAddress)
    {
        $matcher = new EmailMatcherService();
        $profile = [
            'email' => $emailAddress,
            'score' => 0,
//            'full_name' => null,
//            'enrichment_source' => null,
//            'position' => '',
//            'linkedin' => ''
        ];
        $enhancedProfiles = [];

        $this->logInfo("Parse google search results for email: $emailAddress");

        foreach ($results as $result) {
            Log::debug('parse result:');
            Log::debug(collect($result));
            try {
                $newProfile = $this->getPersonFromGoogleSearchResult($result, $emailAddress);
                if (!empty($newProfile['full_name'])) {
                    // Store enhanced data for potential fallback
                    $enhancedProfiles[] = array_merge($newProfile, [
                        'source_title' => $result['title'],
                        'source_snippet' => $result['snippet'],
                        'source_position' => $result['position']
                    ]);
                    $match = $matcher->calculateSearchResultMatchProbability(
                        $emailAddress,
                        $newProfile['full_name'],
                        $newProfile['company'],
                        $result['position']
                    );
                    $this->logInfo("Email: $emailAddress, matching from {$newProfile['enrichment_source']}, with full_name: {$newProfile['full_name']} and company: {$newProfile['company']}");
                    $this->logInfo(collect($match));

                    if ($match['isLikelyMatch'] && $match['score'] > $profile['score']) {
                        $profile = array_replace($profile, $newProfile);
                        $profile['score'] = $match['score'];
                        if (Str::contains($result['link'], 'linkedin.com/in')) {
                            $profile['linkedin'] = $result['link'];
                        }
                    }
                }
            } catch (\Throwable $e) {
                $this->logError('Error parsing google search result: ' . $e->getMessage());
                $this->logError(collect($result));

                continue;
            }
        }

        if (empty($profile['enrichment_source']) || $profile['score'] < 50) {
//            $this->logInfo("Email: $emailAddress, building enhanced profile.");
            $enhancedProfile = $this->buildEnhancedProfile($enhancedProfiles, $emailAddress);
            if ($enhancedProfile) {
//                $this->logInfo("Email: $emailAddress, matching from enhanced profile");
//                $this->logInfo(collect($enhancedProfile));
                $match = $matcher->calculateSearchResultMatchProbability(
                    $emailAddress,
                    $enhancedProfile['full_name'],
                    $enhancedProfile['company'],
                );

                if ($match['isLikelyMatch'] && $match['score'] > $profile['score']) {
                    $profile = array_merge($enhancedProfile, [
                        'email' => $emailAddress,
                        'score' => $match['score']
                    ]);
                }
            }
        }

        if (!empty($profile['full_name'])) {
            $capitalizedName = mb_convert_case($profile['full_name'], MB_CASE_TITLE, "UTF-8");
            $nameParts = $matcher->getNameParts($capitalizedName);

            $profile = array_replace($profile, [
                'full_name' => $capitalizedName,
                'first_name' => $nameParts['firstName'],
                'last_name' => $nameParts['lastName'],
            ]);
        }

        return $profile;
    }

    public function buildEnhancedProfile($profiles, $emailAddress)
    {
        if (empty($profiles)) {
            return null;
        }

        $matcher = new EmailMatcherService();

        // Extract domain from email
        $emailDomain = explode('@', $emailAddress)[1];

        // First find profiles with matching company
        $companyProfiles = array_filter($profiles, function($profile) use ($matcher, $emailDomain) {
            if (empty($profile['company'])) {
                return false;
            }

            $companyScore = $matcher->calculateCompanyScore($emailDomain, $profile['company']);
            return $companyScore >= 40; // Using same threshold as in EmailMatcherService
        });

        if (empty($companyProfiles)) {
            return null;
        }

        $matches = [];

        // For each company profile, try to find matching names in title/snippet
        foreach ($companyProfiles as $companyProfile) {
            $titleAndSnippet = $companyProfile['source_title'] . ' ' .
                $companyProfile['source_snippet'];

            // Try to match each profile's name against the company profile text
            foreach ($profiles as $profile) {
                if (empty($profile['full_name'])) {
                    continue;
                }

                $nameParts = $matcher->getNameParts($profile['full_name']);
                $nameParts['firstName'] = $matcher->normalizeString($nameParts['firstName']);
                $nameParts['lastName'] = $matcher->normalizeString($nameParts['lastName']);
                $nameScore = $matcher->calculateNameScore(
                    $matcher->normalizeString($titleAndSnippet),
                    $nameParts
                );

                if ($nameScore >= 50) { // Using same threshold as in matcher service
                    $matches[] = [
                        'score' => $nameScore,
                        'full_name' => $profile['full_name'],
                        'company' => $companyProfile['company'],
                        'position' => $profile['position'] ?? '',
                        'enrichment_source' => 'google_enhanced'
                    ];
                }
            }
        }

        if (empty($matches)) {
            return null;
        }

        // Sort matches by score descending
        usort($matches, function($a, $b) {
            return $b['score'] <=> $a['score'];
        });

        // Get the name from highest scoring match
        $bestMatch = $matches[0];

        // Find first match with a position
        $position = '';
        foreach ($matches as $match) {
            if (!empty($match['position'])) {
                $position = $match['position'];
                break;
            }
        }

        return [
            'full_name' => $bestMatch['full_name'],
            'company' => $bestMatch['company'],
            'position' => $position,
            'enrichment_source' => 'google_enhanced',
            'match_score' => $bestMatch['score']
        ];
    }

    public function getPersonFromGoogleSearchResult($result, $emailAddress)
    {
        $title = $result['title'];
        $link = $result['link'];
        $snippet = $result['snippet'];

        if (Str::contains($link, 'linkedin.com/in')) {
            $profile = $this->getPersonFromLinkedinResult($title, $link, $emailAddress);
        } elseif (Str::contains($link, 'instagram.com')) {
            $profile = $this->getPersonFromInstagramResult($title, $link);
        } elseif (Str::contains($link, 'crunchbase.com/person')) {
            $profile = $this->getPersonFromCrunchbaseResult($title, $link);
        } elseif (Str::contains($link, 'zoominfo.com/p/')) {
            $profile = $this->getPersonFromZoominfoResult($title, $link);
        } elseif (Str::contains($link, 'facebook.com')) {
            $profile = $this->getPersonFromFacebookResult($title, $link);
        } elseif (Str::contains($link, 'rocketreach.co')) {
            $profile = $this->getPersonFromRocketreachResult($title, $link, $snippet);
        } else {
            $profile = ['full_name' => '', 'enrichment_source' => 'google_unknown'];
        }

        return $profile;
    }

    public function hasAvailableSources()
    {
//        $sources = ['reverse_contact', 'serper'];
        $sources = ['serper'];

        return ExternalService::whereIn('name', $sources)->where('status', 'active')->count() == count($sources);
    }

    /**
     * @param $title
     * @param $link
     * @return array{
     *      full_name: string,
     *      company: string,
     *      position: string,
     *      source: string,
     *  }
     */
    protected function getPersonFromLinkedinResult($title, $link, $emailAddress)
    {
        $title = trim(Str::of($title)->replace(['| LinkedIn', '- LinkedIn'], '')->toString());
        $parts = array_map('trim', explode(' - ', $title));

        // Default structure
        $profile = [
            'full_name' => $parts[0],
            'company' => '',
            'position' => '',
            'enrichment_source' => 'google_linkedin'
        ];

        if (count($parts) === 1) {
            return $profile;
        }

        if (count($parts) === 2) {
            $secondPart = $parts[1];

            // Check if it matches the email domain
            $emailDomain = explode('@', $emailAddress)[1] ?? '';
            $domainName = explode('.', $emailDomain)[0] ?? '';

            // Common position keywords
            $positionKeywords = [
                'manager', 'director', 'ceo', 'cto', 'cfo',
                'lead', 'head', 'advisor', 'consultant',
                'engineer', 'developer', 'specialist',
                'operationeel', 'adviseur', 'eigenaar'
            ];

            // Check if second part contains position keywords
            $isLikelyPosition = Str::contains(
                Str::lower($secondPart),
                $positionKeywords
            );

            // If domain matches second part, it's likely the company
            if (!empty($domainName) && Str::contains(Str::lower($secondPart), Str::lower($domainName))) {
                $profile['company'] = $secondPart;
            }
            // If contains position keywords, it's likely the position
            elseif ($isLikelyPosition) {
                $profile['position'] = $secondPart;
            }
            // Default to company if we can't determine
            else {
                $profile['company'] = $secondPart;
            }
        }

        // For 3 parts, assume name - position - company format
        if (count($parts) === 3) {
            $profile['position'] = $parts[1];
            $profile['company'] = $parts[2];
        }

        return $profile;
    }

    protected function getPersonFromInstagramResult($title, $link)
    {
        if (Str::contains($title, '(')) {
            $titleParts = explode('(', $title);
            $name = trim($titleParts[0]) ?? '';
        } else {
            $name = '';
        }

        return [
            'full_name' => $name,
            'company' => '',
            'position' => '',
            'enrichment_source' => 'google_instagram'
        ];
    }

    protected function getPersonFromCrunchbaseResult($title, $link)
    {
        $title = trim(Str::of($title)->replace('- Crunchbase', '')->toString());
        $titleParts = explode(' - ', $title);
        $name = trim($titleParts[0]) ?? '';
        $company = '';
        $position = '';

        // check if has position and company "- position @ company"
        if (!empty($titleParts[1]) && Str::contains($titleParts[1], '@')) {
            // $company = trim(explode('@', $titleParts[1])[1]);
            $companyParts = explode(' @ ', $titleParts[1]);
            $position = array_shift($companyParts);
            $company = implode(' ', $companyParts);
        }

        return [
            'full_name' => $name,
            'company' => $company,
            'position' => $position,
            'enrichment_source' => 'google_crunchbase'
        ];
    }

    protected function getPersonFromZoominfoResult($title, $link)
    {
        $title = trim(Str::of($title)
            ->replace('Email & Phone Number', '')
            ->replace('- Zoominfo', '')
            ->replace('| Zoominfo', '')
            ->replace('- ZoomInfo', '')
            ->replace('| ZoomInfo', '')
            ->toString()
        );

        $separator = Str::contains($title, ' | ') ? ' | ' : ' - ';
        $titleParts = explode($separator, $title);
        $name = trim($titleParts[0]) ?? '';
        $company = trim($titleParts[1] ?? '');
        $position = '';

        // company might contain position
        if ($company && Str::contains($company, ' at ')) {
            $companyParts = explode(' at ', $company);
            $position = array_shift($companyParts);
            $company = implode(' ', $companyParts);
        }

        return ['full_name' => $name, 'company' => $company, 'position' => $position, 'enrichment_source' => 'google_zoominfo'];
    }

    protected function getPersonFromFacebookResult($title, $link)
    {
        $name = '';
        $company = '';

        $fbSlug = Str::of($link)->betweenFirst('facebook.com/', '&')->toString();
        $excludedPaths = ['groups', 'photos/', 'videos/', 'posts/', 'reel/'];

        if ($fbSlug != $link && !Str::contains($fbSlug, $excludedPaths) ) {
            $name = trim(Str::of($title)->replace('- Facebook', '')->toString());
        }

        return [
            'full_name' => $name,
            'company' => $company,
            'position' => '',
            'enrichment_source' => 'google_facebook'
        ];
    }

    protected function getPersonFromRocketReachResult($title, $link, $snippet)
    {
        $profile = [
            'full_name' => '',
            'company' => '',
            'position' => '',
            'enrichment_source' => 'google_rocketreach'
        ];

        $parts = explode(' · ', $snippet);

        if (count($parts) > 0) {
            $profile['full_name'] = trim($parts[0]);
        }

        if (count($parts) > 1) {
            if (str_contains($parts[1], ' at ')) {
                $positionParts = explode(' at ', $parts[1]);
                $profile['position'] = trim($positionParts[0]);
                $profile['company'] = trim($positionParts[1]);
            } else {
                $profile['company'] = trim($parts[1]);
            }
        }

        return $profile;
    }


    // DEPRECATED
    // fullname - company - linkedin
    protected function searchParseLinkedin($title, $emailName, $emailDomain)
    {
        $profile = [ 'full_name' => null, 'score' => 0, 'source' => null, 'position' => '', 'linkedin' => '' ];

        $title = trim(Str::of($title)->replace('| LinkedIn', '')->toString());
        $titleParts = explode('-', $title);
        $name = trim($titleParts[0]) ?? '';
        $company = trim($titleParts[1] ?? '');

        $sameName = similar_text($emailName, $name, $liNamePercent);
        $avgPercent = $liNamePercent;

        if($company) {
            $sameDomain = similar_text($emailDomain, $company, $liCompanyPercent);
            $avgPercent = ($liNamePercent + $liCompanyPercent) / 2;
        }

        if($avgPercent > self::PASSING_SCORE) {
            $profile['full_name'] = $name;
            $profile['score'] = $avgPercent;
            $profile['source'] = 'linkedin';
        }

        return $profile;
    }

    // DEPRECATED
    // fullname (@username)
    protected function searchParseInstagram($title, $emailName, $emailDomain)
    {
        $profile = [ 'full_name' => null, 'score' => 0, 'source' => null, 'position' => '', 'linkedin' => '' ];

        $titleParts = explode('(', $title);
        $name = trim($titleParts[0]) ?? '';
        $sameName = similar_text($emailName, $name, $liNamePercent);

        if($liNamePercent > self::PASSING_SCORE) {
            $profile['full_name'] = $name;
            $profile['score'] = $liNamePercent;
            $profile['source'] = 'instagram';
        }

        return $profile;
    }

    // DEPRECATED
    // fullname - position @ company?
    protected function searchParseCrunchbase($title, $emailName, $emailDomain)
    {
        $profile = [ 'full_name' => null, 'score' => 0, 'source' => null, 'position' => '', 'linkedin' => '' ];

        $title = trim(Str::of($title)->replace('- Crunchbase', '')->toString());
        $titleParts = explode(' - ', $title);
        $name = trim($titleParts[0]) ?? '';
        $position = '';

        $sameName = similar_text($emailName, $name, $liNamePercent);
        $avgPercent = $liNamePercent;

        // check if has position and company "- position @ company"
        if(!empty($titleParts[1]) && Str::contains($titleParts[1], '@')) {
            // $company = trim(explode('@', $titleParts[1])[1]);
            $companyParts = explode(' @ ', $titleParts[1]);
            $position = array_shift($companyParts);
            $company = implode(' ', $companyParts);

            $sameDomain = similar_text($emailDomain, $company, $liCompanyPercent);
            $avgPercent = ($liNamePercent + $liCompanyPercent) / 2;
        }

        if($avgPercent > self::PASSING_SCORE) {
            $profile['full_name'] = $name;
            $profile['score'] = $avgPercent;
            $profile['source'] = 'crunchbase';
        }

        return $profile;
    }

    // DEPRECATED
    // fullname Email & Phone Number - position at company
    // Chad Offerdahl Email & Phone Number | Partner at Scent Thief
    // Amin Nasser Email & Phone Number - Saudi Aramco - Zoominfo
    protected function searchParseZoominfo($title, $emailName, $emailDomain)
    {
        $profile = [ 'full_name' => null, 'score' => 0, 'source' => null, 'position' => '', 'linkedin' => '' ];

        $title = trim(Str::of($title)
            ->replace('Email & Phone Number', '')
            ->replace('- Zoominfo', '')
            ->replace('| Zoominfo', '')
            ->replace('- ZoomInfo', '')
            ->replace('| ZoomInfo', '')
            ->toString()
        );

        $separator = Str::contains($title, ' | ') ? ' | ' : ' - ';
        $titleParts = explode($separator, $title);
        $name = trim($titleParts[0]) ?? '';
        $company = trim($titleParts[1] ?? '');
        $position = '';

        $sameName = similar_text($emailName, $name, $liNamePercent);
        $avgPercent = $liNamePercent;

        if($company) {
            // company might contain position
            if(Str::contains($company, ' at ')) {
                $companyParts = explode(' at ', $company);
                $position = array_shift($companyParts);
                $company = implode(' ', $companyParts);

                $sameDomain = similar_text($emailDomain, $company, $liCompanyPercent);
            } else {
                $sameDomain = similar_text($emailDomain, $company, $liCompanyPercent);
            }

            $avgPercent = ($liNamePercent + $liCompanyPercent) / 2;
        }

        if($avgPercent > self::PASSING_SCORE) {
            $profile['full_name'] = $name;
            $profile['score'] = $avgPercent;
            $profile['position'] = $position;
            $profile['source'] = 'zoominfo';
        }

        return $profile;
    }

    // DEPRECATED
    // check based on facebook URL, get string after 'facebook.com/'
    // get the remaining string until the first '/'
    // check if not posts, stories, groups, photo, public etc
    protected function searchParseFacebook($link, $title, $emailName, $emailDomain)
    {
        $profile = [ 'full_name' => null, 'score' => 0, 'source' => null, 'position' => '', 'linkedin' => '' ];

        $fbSlug = Str::of($link)->betweenFirst('facebook.com/', '&')->toString();
        $excludedPaths = ['groups', 'photos/', 'videos/', 'posts/', 'reel/'];

        if($fbSlug != $link && !Str::contains($fbSlug, $excludedPaths) ) {
            $name = trim(Str::of($title)->replace('- Facebook', '')->toString());
            $sameName = similar_text($emailName, $name, $liNamePercent);

            if($liNamePercent > self::PASSING_SCORE) {
                $profile['full_name'] = $name;
                $profile['score'] = $liNamePercent;
                $profile['source'] = 'facebook';
            }
        }

        return $profile;
    }

    protected function logInfo($msg)
    {
        $message = "EmailEnrichmentService Domain-{$this->domain->name} ";
        if (!empty($this->searchId)) {
            $message .= "AgencySearch-$this->searchId ";
        }
        Log::info("$message: $msg");
    }

    protected function logError($msg)
    {
        $message = "EmailEnrichmentService Domain-{$this->domain->name} ";
        if (!empty($this->searchId)) {
            $message .= "AgencySearch-$this->searchId ";
        }
        Log::error("$message: $msg");
    }

    public function reverseContactEmailLookupDemo($emailAddress)
    {
        $this->logInfo("reverseContactEmailLookupDemo start");

        $responseData = $this->testRCPerson();
        // $responseData = $this->testRCCompany();
        // $responseData = $this->testRCNull();
        $personData = null;
        $companyData = null;

        // save person data
        if (!empty($responseData['person'])) {
            $person = $responseData['person'];
            $position = null;

            if (!empty($person['positions']['positionHistory'][0]['title'])) {
                $position = strtolower($person['positions']['positionHistory'][0]['title']);
            }

            $personData = array(
                'email' => $emailAddress,
                'first_name' => $person['firstName'],
                'last_name' => $person['lastName'],
                'full_name' => $person['firstName'] . ' '. $person['lastName'],
                'position' => $position,
                'linkedin' => $person['linkedInUrl']
            );
        }

        if (!empty($responseData['company'])) {
            $company = $responseData['company'];
            $position = null;

            $companyData = array(
                'linkedInId' => $company['linkedInId'],
                'name' => $company['name'],
                'universalName' => $company['universalName'],
                'linkedInUrl' => $company['linkedInUrl'],
                'tagline' => $company['tagline'],
                'description' => $company['description'],
            );
        }

        return array(
            'person' => $personData,
            'company' => $companyData
        );
    }

    protected function testRCPerson()
    {
        return [
            "success" => true,
            "email" => "<EMAIL>",
            "emailType" => "professional",
            "person" => [
                "publicIdentifier" => "bobmanwaring",
                "linkedInIdentifier" => "ACoAAAeI5ZwBk3xG5e8QgA6MlSlfgR7KixzfdTw",
                "linkedInUrl" => "https://www.linkedin.com/in/bobmanwaring",
                "firstName" => "Bob",
                "lastName" => "Manwaring",
                "headline" => "Owner, Far North Courier Service, LLC",
                "location" => "Anchorage, Alaska, United States of America",
                "summary" => null,
                "photoUrl" => "https://media.licdn.com/dms/image/v2/D5603AQGoSKiOdGWOHw/profile-displayphoto-shrink_800_800/profile-displayphoto-shrink_800_800/0/1677902281204?e=1729728000&v=beta&t=LaQn3ut0MRXvb0a8kqb1oQ5P_ZZ_oN3Gi2AHeue0pqo",
                "openToWork" => false,
                "creationDate" => [
                    "month" => 6,
                    "year" => 2011
                ],
                "followerCount" => 920,
                "positions" => [
                    "positionsCount" => 6,
                    "positionHistory" => [
                        [
                            "title" => "Owner",
                            "companyName" => "Far North Courier Service, LLC",
                            "companyLocation" => "United States",
                            "description" => "",
                            "startEndDate" => [
                                "start" => [
                                    "month" => 7,
                                    "year" => 2023
                                ],
                                "end" => null
                            ],
                            "contractType" => "Full-time",
                            "companyLogo" => null,
                            "linkedInUrl" => "https://www.linkedin.com/search/results/all/?keywords=Far+North+Courier+Service%2C+LLC"
                        ]
                    ]
                ],
                "schools" => [
                    "educationsCount" => 1,
                    "educationHistory" => [
                        [
                            "degreeName" => "",
                            "fieldOfStudy" => "",
                            "description" => "Student.  Organizer of the Wurzburg Alumni Association.",
                            "linkedInUrl" => "https://www.linkedin.com/search/results/all/?keywords=Wurzburg+American+High+School",
                            "schoolLogo" => null,
                            "schoolName" => "Wurzburg American High School",
                            "startEndDate" => [
                                "start" => [
                                    "month" => 1,
                                    "year" => 1972
                                ],
                                "end" => [
                                    "month" => 1,
                                    "year" => 1976
                                ]
                            ]
                        ]
                    ]
                ],
                "skills" => [
                    "Marketing",
                    "Microsoft Office",
                    "Social Media Marketing",
                    "Marketing Strategy",
                    "New Business Development",
                    "Social Media",
                    "Public Speaking",
                    "Microsoft Excel",
                    "Business Development",
                    "Social Networking",
                    "Training",
                    "Fundraising",
                    "Small Business",
                    "Analysis",
                    "Budgets",
                    "Public Relations"
                ],
                "languages" => [
                ],
                "recommendations" => [
                    "recommendationsCount" => 1,
                    "recommendationHistory" => [
                        [
                            "caption" => "March 24, 2023, Erich was Bob’s client",
                            "description" => "I've had the pleasure to work with Bob in both the Real Estate and Title industries- well over 20 years I figure! always responsive, always ready to help solve issues in our crazy industry.",
                            "authorFullname" => "Erich Heinrich",
                            "authorUrl" => "https://www.linkedin.com/in/eheinrich907"
                        ]
                    ]
                ],
                "certifications" => [
                    "certificationsCount" => 0,
                    "certificationHistory" => [
                    ]
                ]
            ],
            "company" => null,
            "credits_left" => 15,
            "rate_limit_left" => 16
        ];
    }

    protected function testRCCompany()
    {
        return [
            "success" => true,
            "email" => "<EMAIL>",
            "emailType" => "professional",
            "person" => null,
            "company" => [
                "linkedInId" => "74880759",
                "name" => "Alaska REALTORS®",
                "universalName" => "alaska-realtors",
                "linkedInUrl" => "https://www.linkedin.com/company/74880759",
                "employeeCount" => 2,
                "followerCount" => 22,
                "employeeCountRange" => [
                    "start" => 2,
                    "end" => 10
                ],
                "websiteUrl" => "https://alaskarealtors.com/",
                "tagline" => "The Alaska REALTORS® is the voice for real estate in Alaska.",
                "description" => "The objective of the Alaska REALTORS® is to unite Alaska REALTORS®, strengthen our industry and create an environment that promotes and protects private property rights and encourages real property ownership.",
                "industry" => "Real Estate",
                "phone" => null,
                "specialities" => [
                ],
                "headquarter" => [
                    "city" => "Anchorage",
                    "country" => "US",
                    "postalCode" => "99503",
                    "geographicArea" => "Alaska",
                    "street1" => "4205 Minnesota Dr",
                    "street2" => null
                ],
                "logo" => "https://media.licdn.com/dms/image/v2/C560BAQFsSWvYXMuVeA/company-logo_400_400/company-logo_400_400/0/1630640056066?e=1732147200&v=beta&t=mPpss-BZucRhaVKJxTsuPGULf_oyKReIxIJnDxlVMNk"
            ],
            "credits_left" => 14,
            "rate_limit_left" => 15
        ];
    }

    protected function testRCNull()
    {
        return [
            "success" => true,
            "email" => "<EMAIL>",
            "emailType" => "professional",
            "person" => null,
            "company" => null,
            "credits_left" => 15,
            "rate_limit_left" => 16
        ];
    }

    protected function testRapidEmployees()
    {
        return [
            "success" => true,
            "message" => "",
            "data" => [
                "items" => [
                    [
                        "id" => "2689608",
                        "firstName" => "Rosetta",
                        "lastName" => "Alcantra",
                        "fullName" => "Rosetta Alcantra",
                        "username" => "ACwAAAApCkgBa9qTJcxLD0ZJ4Wu_3qno_J2WBYo",
                        "profile" => "https://www.linkedin.com/in/ACwAAAApCkgBa9qTJcxLD0ZJ4Wu_3qno_J2WBYo",
                        "geoRegion" => "Greater Anchorage Area",
                        "openLink" => false,
                        "premium" => false,
                        "currentPositions" => [
                            [
                                "title" => "President",
                                "description" => "Elected and serving as President/CEO for Alaska Native Village Corporation. Providing management oversight of parent company and shared services for subsidiary. Focused on administrative capacity and foundational governance policies. ",
                                "tenureAtPosition" => [
                                    "numMonths" => 1,
                                    "numYears" => 3
                                ],
                                "startedOn" => [
                                    "year" => 2021,
                                    "month" => 8,
                                    "day" => 0
                                ],
                                "companyName" => "Hee Yea Lingde Corporation",
                                "companyLogo" => null,
                                "companyId" => "",
                                "linkedinURL" => "https://www.linkedin.com/company/",
                                "companyIndustry" => "",
                                "companyLocation" => "",
                                "current" => true
                            ]
                        ],
                        "profilePicture" => [
                            [
                                "width" => 100,
                                "height" => 100,
                                "url" => "https://media.licdn.com/dms/image/v2/D5603AQFh6VNFhAdvVQ/profile-displayphoto-shrink_100_100/profile-displayphoto-shrink_100_100/0/1700090833003?e=1729728000&v=beta&t=7WIXokZPF8P1RI3A1lam43Ky5vZJQ7I8yn1YNkTJhMA"
                            ]
                        ]
                    ],
                    [
                        "id" => "25278076",
                        "firstName" => "Sandy",
                        "lastName" => "Eherenman",
                        "fullName" => "Sandy Eherenman",
                        "username" => "ACwAAAGBtnwBP1U62Dif1V8cia5TXprxuJ4I0QQ",
                        "profile" => "https://www.linkedin.com/in/ACwAAAGBtnwBP1U62Dif1V8cia5TXprxuJ4I0QQ",
                        "geoRegion" => "Anchorage, Alaska, United States",
                        "openLink" => false,
                        "premium" => false,
                        "currentPositions" => [
                            [
                                "title" => "Executive Officer",
                                "description" => "",
                                "tenureAtPosition" => [
                                    "numMonths" => 0,
                                    "numYears" => 0
                                ],
                                "startedOn" => [
                                    "year" => 0,
                                    "month" => 0,
                                    "day" => 0
                                ],
                                "companyName" => "Alaska Assoc. of REALTORS",
                                "companyLogo" => null,
                                "companyId" => "",
                                "linkedinURL" => "https://www.linkedin.com/company/",
                                "companyIndustry" => "",
                                "companyLocation" => "",
                                "current" => true
                            ]
                        ],
                        "profilePicture" => null
                    ]
                ],
                "total" => 2,
                "totalPage" => 1
            ]
        ];


    }

    protected function testRapidCompanyByLinkedinUrl()
    {
        return [
            "success" => true,
            "message" => "",
            "data" => [
                "id" => "33231676",
                "name" => "La bouche rouge, Paris ",
                "universalName" => "la-bouche-rouge-paris",
                "linkedinUrl" => "https://www.linkedin.com/company/la-bouche-rouge-paris",
                "tagline" => "Beauty for you and the planet. Refillable and Responsible.",
                "description" => "Since launching in 2017, La bouche rouge has been defining a new standard in pure, sustainable beauty as the French Clean Beauty Pioneer. Concerned that the Cosmetics industry is the third largest environmental polluter, and that 83% of the World's water is polluted with microplastics, La bouche rouge has decided to avoid the use of plastic from the product itself, to the formula and production processes. Starting with the lipstick as its icon, the Paris-based makeup Maison has set out to unite French traditional craftsmanship, and eco-responsibility. In 2020 the Maison has grown its collection to a full sustainable and revolutionary make up line – clean, microplastic-free and refillable. The range was created through the prism of the Maison’s four main values: responsibility, sustainability, traceability and creativity. Beauty for you and the planet. Refillable and Responsible.",
                "type" => "Partnership",
                "phone" => "",
                "Images" => [
                    "logo" => "https://media.licdn.com/dms/image/v2/C4E0BAQFZD-19ukqHsQ/company-logo_400_400/company-logo_400_400/0/1631374636988/la_bouche_rouge_paris_logo?e=1732147200&v=beta&t=M7mQVmvIEKSK4Y0RKBYKnF3c1tIOyJNwJrhpXLoteGs",
                    "cover" => "https://media.licdn.com/dms/image/v2/C561BAQEEqCByG7oltg/company-background_10000/company-background_10000/0/1600089797059/la_bouche_rouge_paris_cover?e=1724958000&v=beta&t=BTuBrFP_Cv7AkRxUVttIhZNYSPWKCidPQelwlLjvyRs"
                ],
                "backgroundCoverImages" => [
                    [
                        "url" => "https://media.licdn.com/dms/image/v2/C561BAQEEqCByG7oltg/company-background_10000/company-background_10000/0/1600089797059/la_bouche_rouge_paris_cover?e=1724958000&v=beta&t=BTuBrFP_Cv7AkRxUVttIhZNYSPWKCidPQelwlLjvyRs",
                        "width" => 1536,
                        "height" => 768
                    ]
                ],
                "logos" => [
                    [
                        "url" => "https://media.licdn.com/dms/image/v2/C4E0BAQFZD-19ukqHsQ/company-logo_200_200/company-logo_200_200/0/1631374636988/la_bouche_rouge_paris_logo?e=1732147200&v=beta&t=DH5XPZX9Sfh-NgaviVSOfLMfTiF6fmIssx5h9sz_5KM",
                        "width" => 200,
                        "height" => 200
                    ]
                ],
                "staffCount" => 30,
                "headquarter" => [
                    "geographicArea" => "Île-de-France",
                    "country" => "FR",
                    "city" => "Paris",
                    "postalCode" => "75002",
                    "line1" => "10, Rue de la Paix"
                ],
                "locations" => [
                    [
                        "geographicArea" => "Île-de-France",
                        "country" => "FR",
                        "city" => "Paris",
                        "postalCode" => "75002",
                        "description" => "Office La Bouche Rouge",
                        "headquarter" => true,
                        "line1" => "10, Rue de la Paix"
                    ]
                ],
                "industries" => [
                    "Personal Care Product Manufacturing"
                ],
                "specialities" => [
                ],
                "website" => "https://www.laboucherougeparis.com/",
                "founded" => [
                    "year" => 2017
                ],
                "callToAction" => [
                    "callToActionType" => "VIEW_WEBSITE",
                    "visible" => true,
                    "callToActionMessage" => [
                        "textDirection" => "USER_LOCALE",
                        "text" => "Visit website"
                    ],
                    "url" => "https://www.laboucherougeparis.com"
                ],
                "followerCount" => 8709,
                "staffCountRange" => "11 - 50",
                "crunchbaseUrl" => "https://www.crunchbase.com/organization/la-bouche-rouge-paris",
                "fundingData" => [
                    "updatedAt" => "1718092136",
                    "updatedDate" => "2024-06-11 07:48:56 +0000 UTC",
                    "numFundingRounds" => 1,
                    "lastFundingRound" => [
                        "investorsCrunchbaseUrl" => "https://www.crunchbase.com/funding_round/la-bouche-rouge-paris-series-a--b1896361",
                        "leadInvestors" => null,
                        "fundingRoundCrunchbaseUrl" => "https://www.crunchbase.com/funding_round/la-bouche-rouge-paris-series-a--b1896361?utm_source=linkedin&utm_medium=referral&utm_campaign=linkedin_companies&utm_content=last_funding",
                        "fundingType" => "SERIES_A",
                        "moneyRaised" => [
                            "currencyCode" => "USD",
                            "amount" => "11190552"
                        ],
                        "numOtherInvestors" => 3,
                        "announcedOn" => [
                            "month" => 2,
                            "day" => 24,
                            "year" => 2022
                        ]
                    ]
                ]
            ]
        ];

    }

    protected function testRapidCompanyByDomain()
    {
        return [
            "success" => true,
            "message" => "",
            "data" => [
                "id" => "35505004",
                "name" => "Wavo",
                "universalName" => "wavo-co",
                "linkedinUrl" => "https://www.linkedin.com/company/wavo-co",
                "tagline" => "Largest E-Commerce Decision Maker Database w/ AI-Powered Cold Email",
                "description" => "Wavo revolutionizes cold email outreach with AI-powered personalization and the industry’s largest database of e-commerce decision-makers. Our platform ensures your messages are impactful and relevant, driving a 3-5x increase in sales. Experience efficient, scalable lead generation tailored to your prospects. Request an invitation to transform your outreach results with Wavo.",
                "type" => "Privately Held",
                "phone" => "",
                "Images" => [
                    "logo" => "https://media.licdn.com/dms/image/v2/C4E0BAQH--wLfmXQSzA/company-logo_400_400/company-logo_400_400/0/1630598598469?e=1732147200&v=beta&t=uDZw80VOyJRbLvpg92UVNjLeOb0wJp9ISwTSXwNhxm4",
                    "cover" => ""
                ],
                "backgroundCoverImages" => null,
                "logos" => [
                    [
                        "url" => "https://media.licdn.com/dms/image/v2/C4E0BAQH--wLfmXQSzA/company-logo_200_200/company-logo_200_200/0/1630598598469?e=1732147200&v=beta&t=7kkHiXKcYEaDXX-sr5NcKCOkg7g1Snw6xFd1e0OTJMw",
                        "width" => 200,
                        "height" => 200
                    ]
                ],
                "staffCount" => 5,
                "headquarter" => [
                    "geographicArea" => "Ontario",
                    "country" => "CA",
                    "city" => "Toronto",
                    "postalCode" => "M5A 1R6",
                    "line1" => "100-32 Britain street"
                ],
                "locations" => [
                    [
                        "geographicArea" => "Ontario",
                        "country" => "CA",
                        "city" => "Toronto",
                        "postalCode" => "M5A 1R6",
                        "headquarter" => true,
                        "line1" => "100-32 Britain street"
                    ]
                ],
                "industries" => [
                    "Software Development"
                ],
                "specialities" => [
                    "lead generation"
                ],
                "website" => "https://wavo.co/",
                "founded" => [
                    "year" => 2019
                ],
                "callToAction" => [
                    "callToActionType" => "VIEW_WEBSITE",
                    "visible" => true,
                    "callToActionMessage" => [
                        "textDirection" => "USER_LOCALE",
                        "text" => "Visit website"
                    ],
                    "url" => "https://wavo.co/"
                ],
                "followerCount" => 854,
                "staffCountRange" => "2 - 10",
                "crunchbaseUrl" => ""
            ]
        ];


    }

    protected function testSerperDev()
    {
        return [
            "searchParameters" => [
                "q" => "<EMAIL>",
                "type" => "search",
                "engine" => "google"
            ],
            "organic" => [
                [
                    "title" => "Nicolas Gerlier Email & Phone Number | Founder at La Bouche Rouge Paris",
                    "link" => "https://www.zoominfo.com/p/Nicolas-Gerlier/3255217281",
                    "snippet" => "Discover the first eco-responsible French beauty house to offer clean, microplastic-free formulas. Respectful of your skin and the planet.",
                    "attributes" => [
                        "Missing" => "ngerlier@ | Show results with:ngerlier@"
                    ],
                    "position" => 1
                ],
                [
                    "title" => "Nicolas Gerlier Email & Phone Number - La Bouche Rouge Paris - Zoominfo",
                    "link" => "https://www.zoominfo.com/p/Nicolas-Gerlier/6344351478",
                    "snippet" => "La bouche rouge offers handcrafted, plastic-free lip makeup essentials, sustainable alternatives with infinitely refillable upcycled leather cases.",
                    "attributes" => [
                        "Missing" => "ngerlier@ | Show results with:ngerlier@"
                    ],
                    "position" => 2
                ],
                [
                    "title" => "Nicolas Gerlier (@nicolas_gerlier) • Instagram photos and videos",
                    "link" => "https://www.instagram.com/nicolas_gerlier/?hl=en",
                    "snippet" => "Photo by Nicolas Gerlier in Paris, France with @unitednations, and @laboucherougeparis. Today, it's United Nations World Oceans Day. · Photo by Nicolas Gerlier ...",
                    "position" => 3
                ],
                [
                    "title" => "Nicolas Gerlier - La bouche rouge, Paris | LinkedIn",
                    "link" => "https://fr.linkedin.com/in/nicolas-gerlier-1907343b",
                    "snippet" => "Développer le lien entre la finance et l'art à travers l'organisation de colloques sur le mécénat et d'expositions avec les l'école des beaux Art de Paris.",
                    "position" => 4
                ],
                [
                    "title" => "Nicolas Gerlier - Founder - La bouche rouge, Paris - LinkedIn",
                    "link" => "https://fr.linkedin.com/in/nicolas-gerlier-1907343b",
                    "snippet" => "Founder at La bouche rouge, Paris · Experience: Evolved Commerce · Education: Brigham Young University · Location: Provo · 151 connections on LinkedIn.",
                    "position" => 4
                ],
                [
                    "title" => "Nicolas Gerlier - Founder & CEO @ La bouche rouge, Paris - Crunchbase",
                    "link" => "https://www.crunchbase.com/person/nicolas-gerlier",
                    "snippet" => "Nicolas Gerlier combined his love for luxury beauty and artisan design, with sustainability to create La bouche rouge, Paris—the first French makeup brand ...",
                    "position" => 5
                ],
                [
                    "title" => "Nicolas Gerlier - Facebook",
                    "link" => "https://www.facebook.com/nicolas.gerlier",
                    "snippet" => "Nicolas Gerlier is on Facebook. Join Facebook to connect with Nicolas Gerlier and others you may know. Facebook gives people the power to share and makes...",
                    "position" => 6
                ],
                [
                    "title" => "Nicolas Gerlier Email & Phone Number | Evolved Commerce",
                    "link" => "https://contactout.com/nicolas-gerlier-17298504",
                    "snippet" => "Maison de Haute beauté. A pioneer in clean make-up and fragrances. Refillable, sustainable & handmade. www.laboucherougeparis.com.",
                    "position" => 7
                ]
            ],
            "peopleAlsoAsk" => [
                [
                    "question" => "Is La Bouche Rouge a clean brand?",
                    "snippet" => "La bouche rouge, Paris is a clean brand, even if all of our ingredients are not of natural origin. When we develop our products, we seek to guarantee clean treatment and serum formulas, as well as impeccable quality and sensoriality for which synthetic pigments may sometimes be necessary.",
                    "title" => "FAQs - La Bouche Rouge Paris",
                    "link" => "https://laboucherougeparis.com/pages/faq"
                ],
                [
                    "question" => "Who is the founder of La Bouche Rouge?",
                    "snippet" => "Meet Nicolas Gerlier, Founder of La Bouche Rouge.",
                    "title" => "Meet Nicolas Gerlier, Founder of La Bouche Rouge - Muse & Heroine",
                    "link" => "https://museandheroine.com/blogs/live-like-a-heroine/meet-nicolas-gerlier-the-founder-of-la-bouche-rouge"
                ]
            ],
            "credits" => 1
        ];
    }
}
